import org.sonarqube.gradle.SonarQubePlugin

buildscript {
    ext {
        springBootVersion = '3.3.8'
    }
    repositories {
        maven {
            url 'https://nexus.tmbbank.local:8081/repository/oneapp'
            credentials {
                username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
                password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
            }
        }
        maven {
            url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
            credentials {
                username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
                password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
            }
        }
    }

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "com.palantir.gradle.docker:gradle-docker:0.35.0"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.4.1.3373"
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: "com.palantir.docker"
apply plugin: 'jacoco'
apply plugin: SonarQubePlugin
group = 'com.tmb.oneapp'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

if (project.hasProperty('projVersion')) {
    project.version = project.projVersion
} else {
    project.version = '13.0.0'
}

test {
    useJUnitPlatform()
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    maven {
        url 'https://nexus.tmbbank.local:8081/repository/oneapp'
        credentials {
            username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
            password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
        }
    }
}

springBoot {
    buildInfo()
}

ext {
    set('springCloudVersion', "2023.0.2")
    set('log4j2.version',"2.17.1")
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
//    implementation 'org.springframework.boot:spring-boot-starter-data-jdbc'
//    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'commons-io:commons-io:2.11.0'
    implementation group: 'org.apache.commons', name: 'commons-pool2', version: '2.11.1'
    implementation 'com.github.ben-manes.caffeine:guava:3.1.1'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.junit.platform:junit-platform-commons:1.10.2'
//    implementation 'com.oracle.ojdbc:ojdbc8:19.3.0.0'
    implementation('io.micrometer:micrometer-registry-prometheus')
    implementation group: 'org.ehcache', name: 'ehcache'
    implementation group: 'javax.cache', name: 'cache-api'
    implementation group: 'io.github.resilience4j', name: 'resilience4j-spring-boot3', version: '2.0.2'
    implementation group: 'io.github.resilience4j', name: 'resilience4j-circuitbreaker', version: '2.0.2'
    implementation group: 'io.github.resilience4j', name: 'resilience4j-micrometer', version: '2.0.2'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'com.google.zxing:core:3.4.1'
    implementation 'com.google.zxing:javase:3.4.1'
    implementation('com.github.ulisesbocchio:jasypt-spring-boot-starter:2.1.0')
    implementation group: 'javax.xml.ws', name: 'jaxws-api', version: '2.3.1'
    implementation group: 'javax.jws', name: 'javax.jws-api', version: '1.1'
    implementation group: 'com.sun.xml.ws', name: 'rt', version: '2.3.5'
    implementation group: 'com.sun.xml.ws', name: 'jaxws-rt', version: '2.3.1'
    implementation fileTree(dir: 'ecm', include: ['*.jar'])
    implementation group: 'org.springdoc', name: 'springdoc-openapi-starter-webmvc-ui', version: '2.0.2'
    implementation 'com.googlecode.json-simple:json-simple:1.1.1'
    implementation 'org.json:json:20231013'
    testImplementation group: 'org.junit.jupiter', name: 'junit-jupiter-api', version: '5.10.1'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.13.0'
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    implementation 'com.google.guava:guava:32.1.3-jre'
    implementation 'com.tmb.common:tmb_common_utility:3.1.0-rc.2'
    implementation 'com.tmb.common:oneapp-redis-client-lib:3.1.1-rc.4'
    implementation 'com.tmb.common:one-kafka-lib:1.0.0-rc.4'
    implementation 'com.ttb.oneapp:oneapp-mongo-client-lib:3.0.1-jdk17'
    implementation 'org.mapstruct:mapstruct:1.5.3.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
    implementation 'io.github.openfeign:feign-okhttp:13.5'

}

bootRun {
    systemProperties System.properties
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

jar {
    enabled = false
    archiveClassifier = ''
}

bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

docker {
    name "com.tmb.oneapp/${project.name}-jvm-17:${project.version}"
    dockerfile file('Dockerfile')
    files jar.archiveFile
    buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}

tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacoco {
    toolVersion = "0.8.11"
}


jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        csv.required = true
    }
}

sonarqube {
    if (System.getProperty("sonar.host.url").equals(null)) {
        properties {
            System.setProperty('sonar.host.url', 'http://localhost:9000')
        }
    }

    def deprecatedVersion =
            '**/V1TransferController.java' // please remove the controller when Binary min version is R12 (Prod 5.12.0)
    properties {
        property 'sonar.coverage.exclusions', '**/RedisCacheService.java,**/configuration/*,**/model/** ,**/constant/*, **/TransferServiceApplication.java,**/utils/*, **/CachingConfig.java, **/ECMWebServiceClient.java, **/predicate/**' + deprecatedVersion
    }
    properties {
        property 'sonar.exclusions', '**/RedisCacheService.java,**/configuration/*,**/AESSecurityUtil.java, **/ECMWebServiceClient.java, **/predicate/**' + deprecatedVersion
    }
}
test.finalizedBy jacocoTestReport

{
   "port": 8080,
   "protocol": "http",
   "name": "proxy",
   "stubs": [
     <% include predicates/helloworld.ejs %>,
     <% include predicates/configuration-transfer.ejs %>,
     {
       "responses": [{
         "proxy": {
           "to": "http://localhost",
           "mode": "proxyAlways",
           "predicateGenerators": [{ "matches": { "path": true } }]
         }
       }]
     }
   ]
 }
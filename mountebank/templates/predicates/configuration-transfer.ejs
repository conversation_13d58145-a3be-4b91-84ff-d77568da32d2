{"predicates": [{"equals": {"method": "GET", "path": "/v1/transfer-service/configuration", "headers": {"mock": "case-1-success"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-service", "description": "mountbank response"}, "data": {"id": "transfer_module", "promptpay_account_fee": [{"min": "0", "max": "20000", "fee": "25"}, {"min": "20000", "max": "2000000", "fee": "35"}], "withdraw_td_cutoff_time": {"start": "06:00", "end": "22:30"}, "promptpay_account_trans_limit": "2000000", "promptpay_proxy_trans_limit": "2000000", "promptpay_proxy_fee": [{"min": "0", "max": "5000", "fee": "0"}, {"min": "5000", "max": "30000", "fee": "2"}, {"min": "30000", "max": "100000", "fee": "5"}, {"min": "100000", "max": "2000000", "fee": "10"}], "prompt_pay_proxy_waive_fee": "N", "exchange_trans_limit": "5000000", "schedule_promptpay": {"citizen": "Y", "moible": "N"}}}}}]}
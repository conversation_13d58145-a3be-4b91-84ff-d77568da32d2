package com.tmb.oneapp.transferservice.service;

import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.OffUsValidateActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.PromptPayValidateActivityLog;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import com.tmb.oneapp.transferservice.feature.common.service.CommonService;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.feature.ete.model.Sender;
import com.tmb.oneapp.transferservice.feature.ete.model.Terminal;
import com.tmb.oneapp.transferservice.feature.ete.service.EteService;
import com.tmb.oneapp.transferservice.feature.financiallog.service.FinancialLogService;
import com.tmb.oneapp.transferservice.model.CommonAuthenResult;
import com.tmb.oneapp.transferservice.model.CustomSlip;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.bank.BankInfoDataModel;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankConfirmResponse;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.Balance;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import com.tmb.oneapp.transferservice.model.transfer.PaymentCacheData;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferAccount;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.APP_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FLOW_NAME_TRANSFER;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_TRANSFER_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;
import static com.tmb.oneapp.transferservice.utils.TransferUtils.circuitBreakException;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V1TransferOtherBankServiceTest {
    @Mock
    private V1TransferServiceHelperImpl v1TransfersServiceHelper;
    @Mock
    private CommonService commonService;
    @Mock
    private CustomerService customerService;
    @Mock
    private EteService eteService;
    @Mock
    ConfigurationService configurationService;
    @Mock
    private FaceRecognizeService faceRecognizeService;
    @Mock
    private FinancialLogService financialLogService;
    @InjectMocks
    private V1TransferOtherBankService v1TransferOtherBankService;

    @Mock
    OauthService oauthService;

    String crmId;
    String correlationId;
    HttpHeaders header;

    @BeforeEach
    void init() {
        ReflectionTestUtils.setField(v1TransferOtherBankService, "amloAmountValidate", new BigDecimal(1));
        ReflectionTestUtils.setField(v1TransferOtherBankService, "minVersionCommonAuthTrigger", "5.12.0");
        crmId = "12345";
        correlationId = "54321";
        header = new HttpHeaders();
    }

    @Test
    void otherBankValidateSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String crmId = "12345";
        String expectedReceiverName = "เทส จ้า";
        String expectedTransId = "TRANSFER_" + crmId + "_" + UUID.randomUUID();

        String toBankShortName = "SCB";
        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        V1CrmProfile crmProfile = V1CrmProfile
                .builder()
                .build();
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode("14");

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(new BigDecimal("100.50"));
        validateETEResponse.setSender(sender);
        validateETEResponse.setTerminal(terminal);
        validateETEResponse.setReceiver(receiver);
        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        TmbOneServiceResponse<List<BankInfoDataModel>> body = new TmbOneServiceResponse();
        List<BankInfoDataModel> banks = new ArrayList<>();
        banks.add(BankInfoDataModel.builder().bankCd("14").bankShortname("SCB").build());
        body.setData(banks);
        ResponseEntity<TmbOneServiceResponse<List<BankInfoDataModel>>> tmbOneServiceResponseResponseEntity = new ResponseEntity<>(body, HttpStatus.OK);
        when(v1TransfersServiceHelper.generateTransId(crmId)).thenReturn(expectedTransId);
        when(v1TransfersServiceHelper.getBankShortName(anyString(), anyString())).thenReturn("SCB");

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);
        commonAuthenResult.setPinFree(false);
        commonAuthenResult.setIsForceFr(false);

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(true);
        faceRecognizeResponse.setPaymentAccuUsgAmt(new BigDecimal(10000));

        VerifyTransactionResult verifyTransactionResult = VerifyTransactionResult.builder()
                .isRequirePin(true)
                .faceRecognizeResponse(faceRecognizeResponse)
                .commonAuthenResult(commonAuthenResult)
                .build();

        when(v1TransfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(verifyTransactionResult);

        TransferOtherBankValidateResponse actual = v1TransferOtherBankService.transferOtherBankValidate(otherBankVerifyRequest, crmId, "1234", headers);

        assertEquals(expectedReceiverName, actual.getToAccountName());

        ArgumentCaptor<TPromptPayVerifyETEResponse> dataSaveToCacheArgument = ArgumentCaptor.forClass(TPromptPayVerifyETEResponse.class);
        verify(v1TransfersServiceHelper, Mockito.times(1))
                .saveDataToCache(anyString(), dataSaveToCacheArgument.capture());

        TPromptPayVerifyETEResponse actualDataSaveToCache = dataSaveToCacheArgument.getValue();
        Assertions.assertNull(actualDataSaveToCache.getPaymentCacheData().getToFavoriteNickname());
        assertEquals("14", actualDataSaveToCache.getPaymentCacheData().getBankCode());
        assertEquals("Note", actualDataSaveToCache.getPaymentCacheData().getNote());
        assertEquals("1", actualDataSaveToCache.getPaymentCacheData().getCategoryId());
        assertEquals("Enter-detail", actualDataSaveToCache.getPaymentCacheData().getFlow());
        assertEquals(toBankShortName, actualDataSaveToCache.getPaymentCacheData().getBankShortName());
        assertEquals(validateETEResponse.getSender().getAccountName(), actualDataSaveToCache.getSender().getAccountName());
        assertEquals(otherBankVerifyRequest.getToBankCode(), actualDataSaveToCache.getPaymentCacheData().getBankCode());
        assertFalse(actualDataSaveToCache.getPaymentCacheData().isTransferToOwnAccount());
        assertEquals(new BigDecimal(10000), actualDataSaveToCache.getPaymentCacheData().getPaymentAccuUsgAmt());
        assertTrue(actualDataSaveToCache.getPaymentCacheData().getIsRequireFr());
    }

    @Test
    void otherBankValidate_CallNotPermited_Failed() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String crmId = "12345";
        String expectedReceiverName = "เทส จ้า";

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode("14");

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(new BigDecimal("100.50"));
        validateETEResponse.setSender(sender);
        validateETEResponse.setTerminal(terminal);
        validateETEResponse.setReceiver(receiver);
        when(eteService.validateTransferPromptPay(any())).thenThrow(circuitBreakException());

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            v1TransferOtherBankService.transferOtherBankValidate(otherBankVerifyRequest, crmId, "1234", headers);
        });

        assertEquals("0014", exception.getErrorCode());
    }

    @Test
    void otherBankValidate_FeignException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String crmId = "12345";
        String expectedReceiverName = "เทส จ้า";

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode("14");

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(new BigDecimal("100.50"));
        validateETEResponse.setSender(sender);
        validateETEResponse.setTerminal(terminal);
        validateETEResponse.setReceiver(receiver);
        when(eteService.validateTransferPromptPay(any())).thenThrow(TMBCommonException.class);

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        Exception exception = assertThrows(Exception.class, () -> {
            v1TransferOtherBankService.transferOtherBankValidate(otherBankVerifyRequest, crmId, "1234", headers);
        });
    }

    @Test
    void transferPromptPayValidateSuccessTest()
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String expectedReceiverName = "เทส จ้า";
        when(v1TransfersServiceHelper.getBankShortName(anyString(), anyString())).thenReturn("SCB");
        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");
        Terminal terminal = new Terminal();
        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode("14");
        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(new BigDecimal("100.50"));
        validateETEResponse.setSender(sender);
        validateETEResponse.setTerminal(terminal);
        validateETEResponse.setReceiver(receiver);
        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        V1CrmProfile crmProfile = V1CrmProfile
                .builder()
                .build();
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);
        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(true);
        faceRecognizeResponse.setPaymentAccuUsgAmt(new BigDecimal(10000));

        TransferModuleModel transferModuleModel = TransferModuleModel.builder()
                .promptPayProxyWaiveFee("Y")
                .build();
        when(configurationService.getTransferConfiguration())
                .thenReturn(transferModuleModel);


        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);
        commonAuthenResult.setPinFree(false);
        commonAuthenResult.setIsForceFr(null);

        VerifyTransactionResult verifyTransactionResult = VerifyTransactionResult.builder()
                .isRequirePin(true)
                .faceRecognizeResponse(faceRecognizeResponse)
                .commonAuthenResult(commonAuthenResult)
                .build();

        when(v1TransfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(verifyTransactionResult);

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        TransferOtherBankValidateResponse actual = v1TransferOtherBankService.transferPromptPayValidate(otherBankVerifyRequest, crmId, "1234", headers);

        assertNotNull(actual);
        ArgumentCaptor<PromptPayValidateActivityLog> promptPayValidateActivityLogArgumentCaptor = ArgumentCaptor.forClass(PromptPayValidateActivityLog.class);

        verify(v1TransfersServiceHelper, times(1)).publishActivityTransaction(promptPayValidateActivityLogArgumentCaptor.capture());
        assertEquals("-", promptPayValidateActivityLogArgumentCaptor.getValue().getDdpFlag());
    }

    @Test
    void transferPromptPayValidateSameAccountExceptionTest()
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String expectedReceiverName = "เทส จ้า";
        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);
        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");
        Terminal terminal = new Terminal();
        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode(TTB_BANK_CODE);
        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(new BigDecimal("100.50"));
        validateETEResponse.setSender(sender);
        validateETEResponse.setTerminal(terminal);
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");
        assertThrows(
                TMBCustomCommonExceptionWithResponse.class,
                () ->
                        v1TransferOtherBankService.transferPromptPayValidate(otherBankVerifyRequest, crmId, "1234", new HttpHeaders()));
    }

    @Test
    void transferPromptPayValidate_CallNotPermitted_Failed()
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String expectedReceiverName = "เทส จ้า";
        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);
        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");
        Terminal terminal = new Terminal();
        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode(TTB_BANK_CODE);

        when(eteService.validateTransferPromptPay(any())).thenThrow(circuitBreakException());

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            v1TransferOtherBankService.transferPromptPayValidate(otherBankVerifyRequest, crmId, "1234", new HttpHeaders());
        });
        assertEquals("0014", exception.getErrorCode());
    }

    @Test
    void transferPromptPayValidate_FeignException()
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String expectedReceiverName = "เทส จ้า";
        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("*************");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);
        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");
        Terminal terminal = new Terminal();
        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode(TTB_BANK_CODE);

        when(eteService.validateTransferPromptPay(any())).thenThrow(TMBCommonException.class);

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder().accountNumber("*********").productNickname("A02").build());
        otherBankVerifyRequest.setAmount("1000.50");
        otherBankVerifyRequest.setToBankCode("14");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");
        Exception exception = assertThrows(Exception.class, () -> {
            v1TransferOtherBankService.transferPromptPayValidate(otherBankVerifyRequest, crmId, "1234", new HttpHeaders());
        });
    }


    @Test
    void transferOtherBankConfirmSuccessTest() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(new BigDecimal("********.00"));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        ArgumentCaptor<TPromptPayETERequest> tPromptPayETERequestArgumentCaptor = ArgumentCaptor.forClass(TPromptPayETERequest.class);

        when(eteService.confirmTransferPromptPay(tPromptPayETERequestArgumentCaptor.capture())).thenReturn(confirmETERes);
        doNothing().when(faceRecognizeService).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        TransferOtherBankConfirmResponse transferOtherBankConfirmResponse = v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);

        verify(v1TransfersServiceHelper, times(1)).updateDailyUsage(anyString(), anyString(), any(), any(), anyBoolean());
        verify(v1TransfersServiceHelper, times(1)).validateCommonFR(any(), any(), any(), any(), any());
        assertNotNull(transferOtherBankConfirmResponse);
        assertEquals(new BigDecimal("********.00"), tPromptPayETERequestArgumentCaptor.getValue().getAmount());

        verify(v1TransfersServiceHelper, times(1)).publishActivityCustomSlip(any(CustomSlipCompleteActivityLog.class));
    }

    @Test
    void transferPromptPayConfirmSuccessTest() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenReturn(confirmETERes);
        doNothing().when(faceRecognizeService).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        TransferOtherBankConfirmResponse transferOtherBankConfirmResponse = v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);

        verify(v1TransfersServiceHelper, times(1)).updateDailyUsage(anyString(), anyString(), any(), any(), anyBoolean());
        assertNotNull(transferOtherBankConfirmResponse);

        verify(v1TransfersServiceHelper, times(1)).publishActivityCustomSlip(any(CustomSlipCompleteActivityLog.class));
    }

    @Test
    void transferPromptPayConfirmWhenPromptPayToOwnAccountShouldNotUpdateDailyLimitTest() throws IOException, TMBCommonException, WriterException, TMBCustomCommonExceptionWithResponse {
        boolean transferToOwnAccount = true;

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(transferToOwnAccount);
        paymentCacheData.setQr("qr");
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        paymentCacheData.setBankCode(TTB_BANK_CODE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenReturn(confirmETERes);

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        TransferOtherBankConfirmResponse transferOtherBankConfirmResponse = v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);

        verify(v1TransfersServiceHelper, never()).updateDailyUsage(anyString(), anyString(), any(), any(), anyBoolean());
        verify(faceRecognizeService, never()).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));

        assertNotNull(transferOtherBankConfirmResponse);
    }

    @Test
    void transferOtherBankConfirmSuccessTest2() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(true);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);
        doNothing().when(oauthService).verifyPinCache(anyString(), anyString(), anyString(), anyString());

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenReturn(confirmETERes);
        doNothing().when(faceRecognizeService).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        TransferOtherBankConfirmResponse transferOtherBankConfirmResponse = v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);
        assertNotNull(transferOtherBankConfirmResponse);
    }


    @Test
    void transferOtherBankConfirmWhenPromptPayToOwnAccountSuccessTest() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        boolean isTransferToOwnAccount = true;

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(true);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(isTransferToOwnAccount);
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);
        doNothing().when(oauthService).verifyPinCache(anyString(), anyString(), anyString(), anyString());

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenReturn(confirmETERes);

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");

        TransferOtherBankConfirmResponse transferOtherBankConfirmResponse = v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);

        assertNotNull(transferOtherBankConfirmResponse);
        verify(v1TransfersServiceHelper, never()).updateDailyUsage(anyString(), anyString(), any(Double.class), any(V1CrmProfile.class), anyBoolean());
        verify(faceRecognizeService, never()).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));
    }

    @Test
    void transferOtherBankConfirmSuccessTestWhenRequirePinTrue() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(true);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);
        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenReturn(confirmETERes);
        doNothing().when(faceRecognizeService).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        TransferOtherBankConfirmResponse transferOtherBankConfirmResponse = v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);

        verify(oauthService, Mockito.times(1)).verifyPinCache(anyString(), anyString(), anyString(), anyString());
        assertNotNull(transferOtherBankConfirmResponse);
    }

    @Test
    void transferOtherBankConfirm_CallNotPermit_Failed() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenThrow(circuitBreakException());

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);
        });
        assertEquals("0014", exception.getErrorCode());
    }

    @Test
    void transferOtherBankConfirm_FeignException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenThrow(TMBCommonException.class);

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        Assertions.assertThrows(Exception.class, () -> v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header));
    }

    @Test
    void transferOtherBankConfirmExceptionTest() throws TMBCommonException, IOException, WriterException {

        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(BigDecimal.valueOf(10));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.00);
        crmProfile.setEbMaxLimitAmtCurrent(2.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenThrow(TMBCommonException.class);


        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);


        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");
        assertThrows(TMBCommonException.class, () -> {
            v1TransferOtherBankService.transferOtherBankConfirm(transferOtherBankConfirmRequest, crmId, correlationId, header);
        });
    }

    @ParameterizedTest
    @CsvSource({"*********, 024",
            "*********, 025",
            "*********, 604",
            "*********, 604",
            "*********, 605",
            "*********, 605",
            "*********, 606",
            "*********, 606",
            "*********, 607",
            "*********, 607",
            "'', ACTIVITY LOG ID NOT MATCH WITH ANY FIN ID"})
    void setActivityFinIdByActivityLog(String activityTypeId, String expectFinId) {
        String actual = ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "setActivityFinIdByActivityLog", activityTypeId);

        assertEquals(expectFinId, actual);
    }

    @Test
    void testNewVersionWhenValidateAuthenticationThenUseCommonAuthFlagIsTrue() throws TMBCommonException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("test123");

        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setRequireCommonAuthentication(true);
        cacheResponse.setPaymentCacheData(paymentCacheData);

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.12.0");

        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(v1TransferOtherBankService,
                "shouldUseCommonAuth", headers.getFirst(APP_VERSION)));

        assertTrue(result);
        verify(oauthService, never()).verifyPinCache(any(), any(), any(), any());
    }

    @Test
    void testLowerMinimumCommonAuthVersionWhenValidateAuthenticationThenUseCommonFR() throws TMBCommonException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("test123");
        request.setFrUuid("fr-uuid-123");

        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setIsRequireFr(true);
        paymentCacheData.setFlow("test-flow");
        cacheResponse.setPaymentCacheData(paymentCacheData);
        cacheResponse.setRequireConfirmPin(true);

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.11.0");
        headers.set(IP_ADDRESS, "127.0.0.1");

        ReflectionTestUtils.invokeMethod(v1TransferOtherBankService,
                "validateAuthentication",
                request,
                cacheResponse,
                headers,
                correlationId,
                crmId);

        verify(v1TransfersServiceHelper, times(1)).validateCommonFR(
                eq("fr-uuid-123"),
                eq(correlationId),
                eq(crmId),
                eq("127.0.0.1"),
                eq("test-flow")
        );

        verify(oauthService, times(1)).verifyPinCache(
                eq(correlationId),
                eq(crmId),
                eq("VERIFY_PIN_REF_ID_test123"),
                eq("transfer")
        );
    }

    @Test
    void testTransferToTTBWhenTransferToOwnAccountThenReturnTrue() {
        String toAccountNo = "**********";
        List<DepositAccount> accounts = Collections.singletonList(
                DepositAccount.builder()
                        .accountNumber(toAccountNo)
                        .build()
        );

        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(v1TransferOtherBankService,
                "checkTransferToOwnAccount", toAccountNo, accounts));

        assertTrue(result);
    }

    @Test
    void testValidateAmountWhenGetTaxIdThenReturnCustomerTaxId() throws TMBCommonException {
        String amount = "100000.00";
        String crmId = "crmId123";
        String correlationId = "corrId123";
        String expectedTaxId = "**********123";

        CustomerKYCResponse kycResponse = new CustomerKYCResponse();
        kycResponse.setIdNo(expectedTaxId);

        when(customerService.getCustomerKyc(crmId, correlationId)).thenReturn(kycResponse);

        String result = ReflectionTestUtils.invokeMethod(v1TransferOtherBankService,
                "getTaxIdFromCustomerKyc", crmId, correlationId, amount);

        assertEquals(expectedTaxId, result);
        verify(customerService, times(1)).getCustomerKyc(crmId, correlationId);
    }

    @Test
    void testWaiveFeePromptPayWhenGetFeeThenReturnZero() throws TMBCommonException {
        BigDecimal feeFromETE = new BigDecimal("25.00");
        String waiveFeePromptPay = "Y";

        TransferModuleModel transferConfig = TransferModuleModel.builder()
                .promptPayProxyWaiveFee(waiveFeePromptPay)
                .build();

        when(configurationService.getTransferConfiguration()).thenReturn(transferConfig);

        String result = ReflectionTestUtils.invokeMethod(v1TransferOtherBankService,
                "getPromptPayFee", feeFromETE, waiveFeePromptPay);

        assertEquals("0.00", result);
    }

    @Test
    void testTransferToOtherBankConfirmWithCustomSlipShouldPublishSlipActivity() throws Exception {
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);

        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();

        CustomSlip customSlip = new CustomSlip();
        customSlip.setSlipId("test-slipId");
        customSlip.setSlipCategory("test-slipCategory");
        customSlip.setSlipDefaultFlag(true);

        request.setCustomSlip(customSlip);
        request.setTransId("test123");
        request.setFrUuid("test-frUuid");

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(cacheResponse);
        when(customerService.getCrmProfile(any(), any())).thenReturn(new V1CrmProfile());
        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmResponse = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal("2000.00"));
        confirmResponse.setBalance(balance);
        when(eteService.confirmTransferPromptPay(any())).thenReturn(confirmResponse);

        doNothing().when(v1TransfersServiceHelper).publishActivityTransaction(any());
        doNothing().when(v1TransfersServiceHelper).publishActivityCustomSlip(any());
        doNothing().when(v1TransfersServiceHelper).deleteAccountCache(any(), any(), any());
        doNothing().when(v1TransfersServiceHelper).deleteTransferDraftData(any());
        doNothing().when(v1TransfersServiceHelper).sendNotification(any(), any(), any(), any(), any());

        header.set("pre-login", "false");
        header.set("device-id", "test-device");
        header.set("activity-id", "test-activity");

        v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, header);

        ArgumentCaptor<CustomSlipCompleteActivityLog> captor = ArgumentCaptor.forClass(CustomSlipCompleteActivityLog.class);
        verify(v1TransfersServiceHelper, times(1)).publishActivityCustomSlip(captor.capture());

        CustomSlipCompleteActivityLog activityLog = captor.getValue();
        assertEquals("test-slipId", activityLog.getSlipId());
        assertEquals("test-slipCategory", activityLog.getSlipCategory());
        assertEquals("test-device", activityLog.getDeviceId());
    }

    @Test
    void testCommonAuthRequiredWhenConfirmTransferThenValidateCommonAuth() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setRequireCommonAuthentication(true);
        paymentCacheData.setFlow("test-flow");

        CommonAuthenticationInformation commonAuthInfo = new CommonAuthenticationInformation();
        commonAuthInfo.setFeatureId("transfer");
        commonAuthInfo.setTotalPaymentAccumulateUsage(BigDecimal.valueOf(5000));
        commonAuthInfo.setToBankCode("014");
        commonAuthInfo.setFlowName("test-flow");
        paymentCacheData.setCommonAuthenticationInformation(commonAuthInfo);

        cacheResponse.setPaymentCacheData(paymentCacheData);


        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("test123");

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(cacheResponse);
        when(customerService.getCrmProfile(any(), any())).thenReturn(new V1CrmProfile());
        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmResponse = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal("2000.00"));
        confirmResponse.setBalance(balance);
        when(eteService.confirmTransferPromptPay(any())).thenReturn(confirmResponse);

        header.set(APP_VERSION, "5.12.0");

        v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, header);

        ArgumentCaptor<CommonAuthenWithPayloadRequest> payloadCaptor = ArgumentCaptor.forClass(CommonAuthenWithPayloadRequest.class);
        verify(oauthService).verifyCommonAuthenWithPayload(
                eq(header),
                payloadCaptor.capture()
        );

        CommonAuthenWithPayloadRequest payload = payloadCaptor.getValue();
        assertEquals(cacheResponse.getAmount().toString(), payload.getAmount());
        assertEquals(commonAuthInfo.getFeatureId(), payload.getFeatureId());
        assertEquals(commonAuthInfo.getToBankCode(), payload.getBankCode());
        assertEquals(commonAuthInfo.getFlowName(), payload.getFlowName());
        assertEquals(request.getTransId(), payload.getRefId());
        assertEquals(commonAuthInfo.getTotalPaymentAccumulateUsage().toString(), payload.getDailyAmount());

        verify(oauthService, never()).verifyPinCache(any(), any(), any(), any());
        verify(v1TransfersServiceHelper, never()).validateCommonFR(any(), any(), any(), any(), any());

        verify(v1TransfersServiceHelper).deleteAccountCache(any(), any(), any());
        verify(v1TransfersServiceHelper).deleteTransferDraftData(any());
        verify(v1TransfersServiceHelper).sendNotification(any(), any(), any(), any(), any());
        verify(v1TransfersServiceHelper).publishActivityCustomSlip(any());
    }

    @Test
    void testNewAppVersionWhenValidateTransferThenRequireCommonAuth() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toBankShortName = "SCB";
        String expectedReceiverName = "เทส จ้า";
        String expectedTransId = "TRANSFER_" + crmId + "_" + UUID.randomUUID();
        BigDecimal amount = new BigDecimal("10000.00");

        V1CrmProfile crmProfile = V1CrmProfile.builder()
                .paymentAccuUsgAmt(new BigDecimal("5000.00"))
                .build();
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("FromAccountName");
        sender.setAccountType("DDA");

        Terminal terminal = new Terminal();

        Receiver receiver = new Receiver();
        receiver.setAccountDisplayName(expectedReceiverName);
        receiver.setAccountId("**********");
        receiver.setBankCode("014");

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(amount);
        validateETEResponse.setSender(sender);
        validateETEResponse.setTerminal(terminal);
        validateETEResponse.setReceiver(receiver);
        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        when(v1TransfersServiceHelper.generateTransId(crmId)).thenReturn(expectedTransId);
        when(v1TransfersServiceHelper.getBankShortName(anyString(), anyString())).thenReturn("SCB");

        TransferOtherBankValidateRequest otherBankVerifyRequest = new TransferOtherBankValidateRequest();
        otherBankVerifyRequest.setDepositAccount(DepositAccount.builder()
                .accountNumber("*********")
                .productNickname("A02")
                .build());
        otherBankVerifyRequest.setAmount(amount.toString());
        otherBankVerifyRequest.setToBankCode("014");
        otherBankVerifyRequest.setToAccountNo("**********");
        otherBankVerifyRequest.setCategoryId("1");
        otherBankVerifyRequest.setNote("Note");
        otherBankVerifyRequest.setFlow("Enter-detail");

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.12.0");

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(true);
        commonAuthenResult.setPinFree(false);
        commonAuthenResult.setIsForceFr(null);

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(false);
        faceRecognizeResponse.setPaymentAccuUsgAmt(BigDecimal.ZERO);

        VerifyTransactionResult verifyTransactionResult = VerifyTransactionResult.builder()
                .faceRecognizeResponse(faceRecognizeResponse)
                .commonAuthenResult(commonAuthenResult)
                .build();

        when(v1TransfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any()))
                .thenReturn(verifyTransactionResult);

        TransferOtherBankValidateResponse response = v1TransferOtherBankService.transferOtherBankValidate(
                otherBankVerifyRequest, crmId, correlationId, headers);

        assertNotNull(response);
        assertEquals(expectedTransId, response.getTransId());
        assertEquals(expectedReceiverName, response.getToAccountName());
        assertEquals(amount.toString(), response.getAmount());
        assertEquals("0.00", response.getFee());
        assertTrue(response.getIsRequireCommonAuthen());
        assertFalse(response.getIsRequireFr());

        ArgumentCaptor<TPromptPayVerifyETEResponse> dataSaveToCacheArgument =
                ArgumentCaptor.forClass(TPromptPayVerifyETEResponse.class);
        verify(v1TransfersServiceHelper).saveDataToCache(anyString(), dataSaveToCacheArgument.capture());

        TPromptPayVerifyETEResponse actualDataSaveToCache = dataSaveToCacheArgument.getValue();
        assertNotNull(actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation());
        assertEquals(TRANSFER_FEATURE_ID, actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation().getFeatureId());
        assertEquals(receiver.getBankCode(), actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation().getToBankCode());
        assertEquals(FLOW_NAME_TRANSFER, actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation().getFlowName());
        assertEquals(3, actualDataSaveToCache.getPaymentCacheData().getCommonAuthenticationInformation().getToBankCode().length());

        PaymentCacheData paymentCacheData = actualDataSaveToCache.getPaymentCacheData();
        assertNotNull(paymentCacheData);
        assertTrue(paymentCacheData.isRequireCommonAuthentication());
        assertEquals(toBankShortName, paymentCacheData.getBankShortName());
        assertEquals(validateETEResponse.getSender().getAccountName(), actualDataSaveToCache.getSender().getAccountName());
        assertEquals(otherBankVerifyRequest.getToBankCode(), paymentCacheData.getBankCode());

        assertNotNull(response.getCommonAuthenticationInformation());
        assertEquals(3, response.getCommonAuthenticationInformation().getToBankCode().length());

        ArgumentCaptor<OffUsValidateActivityLog> offUsValidateActivityLogArgumentCaptor = ArgumentCaptor.forClass(OffUsValidateActivityLog.class);
        verify(v1TransfersServiceHelper, times(1)).publishActivityTransaction(offUsValidateActivityLogArgumentCaptor.capture());
        assertEquals("-", offUsValidateActivityLogArgumentCaptor.getValue().getDdpFlag());
    }

    @Test
    void testTransactionExceedLimitWhenValidateTransferThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = TransferOtherBankValidateRequest.builder()
                .amount("100000.00")
                .depositAccount(DepositAccount.builder()
                        .accountNumber("**********")
                        .productNickname("A02")
                        .build())
                .toAccountNo("**********")
                .toBankCode("014")
                .build();

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        V1CrmProfile crmProfile = V1CrmProfile.builder()
                .ebAccuUsgAmtDaily(50000.00)
                .ebMaxLimitAmtCurrent(90000.00)
                .build();
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(BigDecimal.ZERO);
        validateETEResponse.setAmount(new BigDecimal("100000.00"));

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("Test Account");
        validateETEResponse.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setBankCode("014");
        receiver.setAccountId("**********");
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);
        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(true);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v1TransferOtherBankService.transferOtherBankValidate(request, crmId, correlationId, new HttpHeaders()));

        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), exception.getErrorMessage());
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), exception.getService());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());

        verify(customerService).getCrmProfile(correlationId, crmId);
        verify(v1TransfersServiceHelper).checkTransactionLimited(any(), eq(100000.00));
    }

    @Test
    void testValidateTransferToTTBOwnAccountThenSetTransferToTTBOwnAccountIsTrue() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toAccountNo = "**********";
        TransferOtherBankValidateRequest request = TransferOtherBankValidateRequest.builder()
                .amount("1000.00")
                .depositAccount(DepositAccount.builder()
                        .accountNumber("**********")
                        .productNickname("A02")
                        .build())
                .toAccountNo(toAccountNo)
                .toBankCode(TTB_BANK_CODE)
                .build();

        V1CrmProfile crmProfile = V1CrmProfile.builder().build();
        when(customerService.getCrmProfile(correlationId, crmId)).thenReturn(crmProfile);
        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKycResponse);

        List<DepositAccount> depositAccounts = Arrays.asList(
                DepositAccount.builder().accountNumber(toAccountNo).build(),
                DepositAccount.builder().accountNumber("**********").build()
        );
        when(customerService.getAccountsTransfer(correlationId, crmId)).thenReturn(depositAccounts);

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(BigDecimal.ZERO);
        validateETEResponse.setAmount(new BigDecimal("1000.00"));

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("Test Account");
        validateETEResponse.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setBankCode(TTB_BANK_CODE);
        receiver.setAccountId(toAccountNo);
        receiver.setAccountDisplayName("Test Receiver");
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        when(v1TransfersServiceHelper.getBankShortName(anyString(), anyString())).thenReturn("TTB");
        when(v1TransfersServiceHelper.generateTransId(anyString())).thenReturn("trans123");

        CommonData commonDataInfo = new CommonData();
        commonDataInfo.setPinFreeMaxTrans("20");
        commonDataInfo.setPinFreePaymentMaxLimit("20");

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(false);
        faceRecognizeResponse.setPaymentAccuUsgAmt(BigDecimal.ZERO);

        VerifyTransactionResult verifyTransactionResult = new VerifyTransactionResult(true,
                faceRecognizeResponse, commonAuthenResult);
        when(v1TransfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any()))
                .thenReturn(verifyTransactionResult);

        TransferOtherBankValidateResponse response = v1TransferOtherBankService
                .transferOtherBankValidate(request, crmId, correlationId, new HttpHeaders());

        verify(customerService).getAccountsTransfer(correlationId, crmId);
        verify(customerService).getCustomerKyc(crmId, correlationId);

        ArgumentCaptor<TPromptPayVerifyETEResponse> cacheDataCaptor =
                ArgumentCaptor.forClass(TPromptPayVerifyETEResponse.class);
        verify(v1TransfersServiceHelper).saveDataToCache(anyString(), cacheDataCaptor.capture());

        TPromptPayVerifyETEResponse cachedData = cacheDataCaptor.getValue();
        assertTrue(cachedData.getPaymentCacheData().isTransferToOwnAccount());
        assertEquals(TTB_BANK_CODE, cachedData.getPaymentCacheData().getBankCode());
        assertEquals("Test Receiver", response.getToAccountName());
    }

    @Test
    void testValidatePromptPayTransferThenReturnSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toAccountNo = "**********";
        String toMobileNo = "**********";

        TransferOtherBankValidateRequest request = TransferOtherBankValidateRequest.builder()
                .amount("1000.00")
                .depositAccount(DepositAccount.builder()
                        .accountNumber("**********")
                        .productNickname("A02")
                        .waiveFeeForPromptPay("Y")
                        .build())
                .toAccountNo(toMobileNo)
                .toBankCode(TTB_BANK_CODE)
                .build();

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKycResponse);

        V1CrmProfile crmProfile = V1CrmProfile.builder().build();
        when(customerService.getCrmProfile(correlationId, crmId)).thenReturn(crmProfile);
        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("0.00"));
        validateETEResponse.setAmount(new BigDecimal("1000.00"));

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("Test Account");
        validateETEResponse.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setBankCode(TTB_BANK_CODE);
        receiver.setAccountId(toAccountNo);
        receiver.setAccountDisplayName("Test Receiver");
        receiver.setProxyValue(toMobileNo);
        receiver.setProxyType("MOBILE");
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        TransferModuleModel transferModuleModel = TransferModuleModel.builder()
                .promptPayProxyWaiveFee("Y")
                .build();
        when(configurationService.getTransferConfiguration()).thenReturn(transferModuleModel);

        when(v1TransfersServiceHelper.getBankShortName(anyString(), anyString())).thenReturn("TTB");
        when(v1TransfersServiceHelper.generateTransId(anyString())).thenReturn("trans123");

        CommonData commonDataInfo = new CommonData();
        commonDataInfo.setPinFreeMaxTrans("20");
        commonDataInfo.setPinFreePaymentMaxLimit("20");


        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(false);
        faceRecognizeResponse.setPaymentAccuUsgAmt(BigDecimal.ZERO);

        VerifyTransactionResult verifyTransactionResult = new VerifyTransactionResult(true,
                faceRecognizeResponse, commonAuthenResult);
        when(v1TransfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any()))
                .thenReturn(verifyTransactionResult);

        TransferOtherBankValidateResponse response = v1TransferOtherBankService
                .transferPromptPayValidate(request, crmId, correlationId, new HttpHeaders());

        assertNotNull(response);
        assertEquals("trans123", response.getTransId());
        assertEquals("Test Receiver", response.getToAccountName());
        assertEquals("1000.00", response.getAmount());
        assertEquals("0.00", response.getFee());
        assertFalse(response.getIsRequireFr());
        assertFalse(response.getIsRequireCommonAuthen());

        verify(customerService).getCustomerKyc(crmId, correlationId);
        verify(customerService).getCrmProfile(correlationId, crmId);
        verify(configurationService).getTransferConfiguration();

        ArgumentCaptor<TPromptPayVerifyETEResponse> cacheDataCaptor =
                ArgumentCaptor.forClass(TPromptPayVerifyETEResponse.class);
        verify(v1TransfersServiceHelper).saveDataToCache(anyString(), cacheDataCaptor.capture());

        TPromptPayVerifyETEResponse cachedData = cacheDataCaptor.getValue();
        assertNotNull(cachedData.getPaymentCacheData());
        assertEquals(TTB_BANK_CODE, cachedData.getPaymentCacheData().getBankCode());
        assertEquals("MOBILE", cachedData.getReceiver().getProxyType());
        assertEquals(toMobileNo, cachedData.getReceiver().getProxyValue());
    }

    @Test
    void testValidatePromptPayTransferSameAccountThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String accountId = "**********";
        String mobileNo = "**********";

        TransferOtherBankValidateRequest request = TransferOtherBankValidateRequest.builder()
                .amount("1000.00")
                .depositAccount(DepositAccount.builder()
                        .accountNumber(accountId)
                        .build())
                .toAccountNo(mobileNo)
                .toBankCode(TTB_BANK_CODE)
                .build();

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKycResponse);

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setAmount(new BigDecimal("1000.00"));

        Sender sender = new Sender();
        sender.setAccountId(accountId);
        validateETEResponse.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setBankCode(TTB_BANK_CODE);
        receiver.setAccountId(accountId);
        receiver.setProxyValue(mobileNo);
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);
        when(v1TransfersServiceHelper.formatMobileOrCitizen(anyString())).thenReturn(mobileNo);

        TMBCustomCommonExceptionWithResponse exception = assertThrows(
                TMBCustomCommonExceptionWithResponse.class,
                () -> v1TransferOtherBankService.transferPromptPayValidate(
                        request, crmId, correlationId, new HttpHeaders())
        );

        assertEquals(ResponseCode.PROMPTPAY_LINKED.getCode(), exception.getErrorCode());
        assertEquals(ResponseCode.PROMPTPAY_LINKED.getService(), exception.getService());
        assertTrue(exception.getErrorMessage().contains(mobileNo));

        verify(customerService).getCustomerKyc(crmId, correlationId);
        verify(eteService).validateTransferPromptPay(any());
        verify(v1TransfersServiceHelper).formatMobileOrCitizen(mobileNo);
    }

    @Test
    void testTransferPromptPayValidateWhenExceedDailyLimitThenThrowDailyLimitException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = TransferOtherBankValidateRequest.builder()
                .amount("100000.00")
                .depositAccount(DepositAccount.builder()
                        .accountNumber("**********")
                        .productNickname("A02")
                        .waiveFeeForPromptPay("1")
                        .build())
                .toAccountNo("**********")
                .toBankCode("014")
                .build();

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        V1CrmProfile crmProfile = V1CrmProfile.builder()
                .ebAccuUsgAmtDaily(50000.00)
                .ebMaxLimitAmtCurrent(90000.00)
                .build();
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        TransferModuleModel transferModuleModel = TransferModuleModel.builder()
                .promptPayProxyWaiveFee("Y")
                .build();
        when(configurationService.getTransferConfiguration()).thenReturn(transferModuleModel);

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("25.00"));
        validateETEResponse.setAmount(new BigDecimal("100000.00"));

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("Test Account");
        validateETEResponse.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setBankCode("014");
        receiver.setAccountId("**********");
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(true);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v1TransferOtherBankService.transferPromptPayValidate(
                        request, crmId, correlationId, new HttpHeaders())
        );

        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), exception.getErrorMessage());
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), exception.getService());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());

        verify(customerService).getCrmProfile(correlationId, crmId);
        verify(v1TransfersServiceHelper).checkTransactionLimited(any(), eq(100000.00));
        verify(configurationService).getTransferConfiguration();
    }

    @Test
    void testTransferPromptPayValidateWhenTransactionNotOverDailyLimitThenNotRequireAnyAuthenticationMethod() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toAccountNo = "**********";
        String toMobileNo = "**********";

        TransferOtherBankValidateRequest request = TransferOtherBankValidateRequest.builder()
                .amount("1000.00")
                .depositAccount(DepositAccount.builder()
                        .accountNumber("**********")
                        .productNickname("A02")
                        .waiveFeeForPromptPay("1")
                        .build())
                .toAccountNo(toMobileNo)
                .toBankCode(TTB_BANK_CODE)
                .build();

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        customerKycResponse.setIdNo("**********123");
        when(customerService.getCustomerKyc(crmId, correlationId)).thenReturn(customerKycResponse);

        V1CrmProfile crmProfile = V1CrmProfile.builder().build();
        when(customerService.getCrmProfile(correlationId, crmId)).thenReturn(crmProfile);
        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TransferModuleModel transferModuleModel = TransferModuleModel.builder()
                .promptPayProxyWaiveFee("Y")
                .build();
        when(configurationService.getTransferConfiguration()).thenReturn(transferModuleModel);

        TPromptPayVerifyETEResponse validateETEResponse = new TPromptPayVerifyETEResponse();
        validateETEResponse.setFee(new BigDecimal("25.00"));
        validateETEResponse.setAmount(new BigDecimal("1000.00"));

        Sender sender = new Sender();
        sender.setAccountId("**********");
        sender.setAccountName("Test Account");
        validateETEResponse.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setBankCode(TTB_BANK_CODE);
        receiver.setAccountId(toAccountNo);
        receiver.setAccountDisplayName("Test Receiver");
        receiver.setProxyValue(toMobileNo);
        receiver.setProxyType("MOBILE");
        validateETEResponse.setReceiver(receiver);

        when(eteService.validateTransferPromptPay(any())).thenReturn(validateETEResponse);

        when(v1TransfersServiceHelper.getBankShortName(anyString(), anyString())).thenReturn("TTB");
        when(v1TransfersServiceHelper.generateTransId(anyString())).thenReturn("trans123");

        CommonData commonDataInfo = new CommonData();
        commonDataInfo.setPinFreeMaxTrans("20");
        commonDataInfo.setPinFreePaymentMaxLimit("20");

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(false);

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(false);
        faceRecognizeResponse.setPaymentAccuUsgAmt(BigDecimal.ZERO);

        VerifyTransactionResult verifyTransactionResult = new VerifyTransactionResult(true,
                faceRecognizeResponse, commonAuthenResult);
        when(v1TransfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any()))
                .thenReturn(verifyTransactionResult);

        TransferOtherBankValidateResponse response = v1TransferOtherBankService
                .transferPromptPayValidate(request, crmId, correlationId, new HttpHeaders());

        assertNotNull(response);
        assertEquals("trans123", response.getTransId());
        assertEquals("Test Receiver", response.getToAccountName());
        assertEquals("1000.00", response.getAmount());
        assertEquals("0.00", response.getFee());
        assertFalse(response.getIsRequireFr());
        assertFalse(response.getIsRequireCommonAuthen());

        verify(customerService).getCustomerKyc(crmId, correlationId);
        verify(customerService).getCrmProfile(correlationId, crmId);
        verify(configurationService).getTransferConfiguration();
        verify(eteService).validateTransferPromptPay(any());
    }

    @Test
    void testConfirmTransferWhenExceedDailyLimitThenThrowDailyLimitException() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(new BigDecimal("100000.00"));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(50000.00);
        crmProfile.setEbMaxLimitAmtCurrent(90000.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(true);

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            v1TransferOtherBankService.transferOtherBankConfirm(
                    transferOtherBankConfirmRequest,
                    crmId,
                    correlationId,
                    header
            );
        });

        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), exception.getErrorMessage());
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), exception.getService());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());

        verify(customerService).getCrmProfile(correlationId, crmId);
        verify(v1TransfersServiceHelper).checkTransactionLimited(any(), eq(100000.00));
    }

    @Test
    void testConfirmPromptPayTransferWhenNotOverDailyLimitThenSuccess() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse {
        TPromptPayVerifyETEResponse transferData = new TPromptPayVerifyETEResponse();
        transferData.setTransactionReference("ref1");
        transferData.setRequireConfirmPin(false);

        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setFee(BigDecimal.ZERO);

        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setAmount(new BigDecimal("1000.00"));

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setQr("qr");
        paymentCacheData.setIsRequireFr(Boolean.TRUE);
        paymentCacheData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_TRANSFER_OTHER_BANK);
        receiver.setBankCode("22");
        receiver.setProxyValue("**********");
        transferData.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("**********");
        transferData.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test_id");
        transferData.setTerminal(terminal);

        when(v1TransfersServiceHelper.getTransferDraftData(any(), any())).thenReturn(transferData);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(5000.00);
        crmProfile.setEbMaxLimitAmtCurrent(90000.00);
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);

        when(v1TransfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);

        TPromptPayVerifyETEResponse confirmETERes = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        confirmETERes.setBalance(balance);

        when(eteService.confirmTransferPromptPay(any(TPromptPayETERequest.class))).thenReturn(confirmETERes);
        doNothing().when(faceRecognizeService).updatePaymentAccumulateUsageAmount(
                anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class)
        );

        TransferOtherBankConfirmRequest transferOtherBankConfirmRequest = new TransferOtherBankConfirmRequest();
        transferOtherBankConfirmRequest.setTransId("**********");

        TransferOtherBankConfirmResponse response = v1TransferOtherBankService.transferOtherBankConfirm(
                transferOtherBankConfirmRequest,
                crmId,
                correlationId,
                header
        );

        assertNotNull(response);

        verify(customerService).getCrmProfile(correlationId, crmId);
        verify(v1TransfersServiceHelper).checkTransactionLimited(any(), eq(1000.00));
        verify(eteService).confirmTransferPromptPay(any());
    }


    private void setUpBasicCacheResponse(TPromptPayVerifyETEResponse response) {
        response.setTransactionReference("ref1");
        response.setAmount(BigDecimal.valueOf(1000));
        response.setFee(BigDecimal.ZERO);

        PaymentCacheData paymentCacheData = new PaymentCacheData();
        paymentCacheData.setTransferToOwnAccount(false);
        paymentCacheData.setFlow("test-flow");
        paymentCacheData.setRequireCommonAuthentication(true);
        response.setPaymentCacheData(paymentCacheData);

        Receiver receiver = new Receiver();
        receiver.setBankCode("014");
        receiver.setProxyType("P");
        receiver.setProxyValue("test-proxy");
        response.setReceiver(receiver);

        Sender sender = new Sender();
        sender.setAccountId("test-account");
        response.setSender(sender);

        Terminal terminal = new Terminal();
        terminal.setId("test-terminal");
        response.setTerminal(terminal);

        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal(2000));
        response.setBalance(balance);
    }

    @Test
    void testValidateCommonAuthRequestParamsWhenSuccessShotNotThrowsException() {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("123456");

        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        cacheResponse.setAmount(BigDecimal.valueOf(100.00));

        assertDoesNotThrow(() ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "validateCommonAuthRequestParams", request, cacheResponse)
        );
    }

    @Test
    void testValidateCommonAuthRequestParamsWhenNullRequestThenThrowsIllegalArgumentException() {
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        cacheResponse.setAmount(BigDecimal.valueOf(100.00));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "validateCommonAuthRequestParams", null, cacheResponse)
        );
        assertEquals("Transfer request cannot be null", exception.getMessage());
    }

    @Test
    void testValidateCommonAuthRequestParamsWhenNullCacheResponseThenThrowsIllegalArgumentException() {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("123456");

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "validateCommonAuthRequestParams", request, null)
        );
        assertEquals("Cache response cannot be null", exception.getMessage());
    }

    @Test
    void testValidateCommonAuthRequestParamsWhenEmptyTransIdThenThrowsIllegalArgumentException() {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("");

        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        cacheResponse.setAmount(BigDecimal.valueOf(100.00));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "validateCommonAuthRequestParams", request, cacheResponse)
        );
        assertEquals("Transaction ID cannot be empty", exception.getMessage());
    }

    @Test
    void testValidateCommonAuthRequestParamsWhenNullTransIdThenThrowsIllegalArgumentException() {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId(null);

        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        cacheResponse.setAmount(BigDecimal.valueOf(100.00));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "validateCommonAuthRequestParams", request, cacheResponse)
        );
        assertEquals("Transaction ID cannot be empty", exception.getMessage());
    }

    @Test
    void testValidateCommonAuthRequestParamsWhenNullAmountThenThrowsIllegalArgumentException() {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setTransId("123456");

        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        cacheResponse.setAmount(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "validateCommonAuthRequestParams", request, cacheResponse)
        );
        assertEquals("Transfer amount cannot be null", exception.getMessage());
    }

    @Test
    void testPublishActivityTransactionFailWhenLogConfirmActivityThenThrowRuntimeException() {
        HttpHeaders headers = new HttpHeaders();
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);

        TPromptPayVerifyETEResponse confirmResponse = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal("1000.00"));
        confirmResponse.setBalance(balance);

        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setCustomSlip(new CustomSlip());
        String correlationId = "correlationId";
        String crmId = "crmId";
        String transactionDateTime = "2024-02-17T10:00:00";

        doThrow(new RuntimeException("Failed to publish activity"))
                .when(v1TransfersServiceHelper)
                .publishActivityTransaction(any());

        assertThrows(RuntimeException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "logConfirmActivity", headers, cacheResponse, confirmResponse,
                        request, correlationId, crmId, transactionDateTime));

        verify(v1TransfersServiceHelper).publishActivityTransaction(any());
        verify(v1TransfersServiceHelper, never()).publishActivityCustomSlip(any());
        verify(financialLogService, never()).saveLogFinancialAndTransactionEvent(any(), any(), any());
    }

    @Test
    void testPublishActivityCustomSlipFailWhenLogConfirmActivityThenThrowRuntimeException() {
        HttpHeaders headers = new HttpHeaders();
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);
        TPromptPayVerifyETEResponse confirmResponse = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal("1000.00"));
        confirmResponse.setBalance(balance);

        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setCustomSlip(new CustomSlip());
        String correlationId = "correlationId";
        String crmId = "crmId";
        String transactionDateTime = "2024-02-17T10:00:00";

        doNothing()
                .when(v1TransfersServiceHelper)
                .publishActivityTransaction(any());

        doThrow(new RuntimeException("Failed to publish custom slip"))
                .when(v1TransfersServiceHelper)
                .publishActivityCustomSlip(any());

        assertThrows(RuntimeException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "logConfirmActivity", headers, cacheResponse, confirmResponse,
                        request, correlationId, crmId, transactionDateTime));

        verify(v1TransfersServiceHelper).publishActivityTransaction(any());
        verify(v1TransfersServiceHelper).publishActivityCustomSlip(any());
        verify(financialLogService, never()).saveLogFinancialAndTransactionEvent(any(), any(), any());
    }

    @Test
    void testSaveLogFinancialFailWhenLogConfirmActivityThenThrowRuntimeException() {
        HttpHeaders headers = new HttpHeaders();
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);
        TPromptPayVerifyETEResponse confirmResponse = new TPromptPayVerifyETEResponse();
        Balance balance = new Balance();
        balance.setAvailable(new BigDecimal("1000.00"));
        confirmResponse.setBalance(balance);

        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        request.setCustomSlip(new CustomSlip());
        String correlationId = "correlationId";
        String crmId = "crmId";
        String transactionDateTime = "2024-02-17T10:00:00";

        doNothing()
                .when(v1TransfersServiceHelper)
                .publishActivityTransaction(any());

        doNothing()
                .when(v1TransfersServiceHelper)
                .publishActivityCustomSlip(any());

        doThrow(new RuntimeException("Failed to save financial log"))
                .when(financialLogService)
                .saveLogFinancialAndTransactionEvent(any(), any(), any());

        assertThrows(RuntimeException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "logConfirmActivity", headers, cacheResponse, confirmResponse,
                        request, correlationId, crmId, transactionDateTime));

        verify(v1TransfersServiceHelper).publishActivityTransaction(any());
        verify(v1TransfersServiceHelper).publishActivityCustomSlip(any());
        verify(financialLogService).saveLogFinancialAndTransactionEvent(any(), any(), any());
    }

    @Test
    void testUpdateUsageMetricsWhenAppVersionCommonAuthVersionShouldNotThrowException() {
        String appVersion = "5.12.0";
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(5000.00);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("5000.00"));
        crmProfile.setPinFreeTxnCount(1);
        ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "updateUsageMetrics", correlationId, crmId, cacheResponse, crmProfile, appVersion);

        verify(v1TransfersServiceHelper, times(1)).updateUsageAccumulation(any(), any(), any(), any(), anyBoolean());
    }

    @Test
    void testUpdateUsageMetricsWhenAppVersionCommonAuthVersionAndNotRequirePinShouldUpdatePinFreeAndNotThrowException() {
        String appVersion = "5.12.0";
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);
        cacheResponse.getPaymentCacheData().setRequireCommonAuthentication(false);
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(5000.00);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("5000.00"));
        crmProfile.setPinFreeTxnCount(1);
        ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "updateUsageMetrics", correlationId, crmId, cacheResponse, crmProfile, appVersion);

        verify(v1TransfersServiceHelper, times(1)).updateUsageAccumulation(any(), any(), any(), any(), anyBoolean());
        verify(v1TransfersServiceHelper, times(1)).updatePinFreeCount(anyString(), anyString(), any());
    }

    @Test
    void testUpdateUsageMetricsWhenAppVersionLowerThanCommonAuthVersionShouldNotThrowException() {
        String appVersion = "5.11.0";
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);
        cacheResponse.getPaymentCacheData().setIsRequireFr(true);
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(5000.00);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("5000.00"));
        crmProfile.setPinFreeTxnCount(1);
        ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "updateUsageMetrics", correlationId, crmId, cacheResponse, crmProfile, appVersion);

        verify(v1TransfersServiceHelper, never()).updateUsageAccumulation(any(), any(), any(), any(), anyBoolean());
        verify(v1TransfersServiceHelper, times(1)).updateDailyUsage(anyString(), anyString(), any(), any(), anyBoolean());
        verify(faceRecognizeService, times(1)).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(), anyBoolean(), any());
    }

    @Test
    void testUpdateUsageMetricsWhenGotErrorShouldThrowException() {
        String appVersion = "5.12.0";
        TPromptPayVerifyETEResponse cacheResponse = new TPromptPayVerifyETEResponse();
        setUpBasicCacheResponse(cacheResponse);
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(5000.00);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("5000.00"));
        crmProfile.setPinFreeTxnCount(1);

        doThrow(new RuntimeException()).when(v1TransfersServiceHelper).updateUsageAccumulation(any(), any(), any(), any(), anyBoolean());

        assertThrows(RuntimeException.class, () ->
                ReflectionTestUtils.invokeMethod(v1TransferOtherBankService, "updateUsageMetrics", correlationId, crmId, cacheResponse, crmProfile, appVersion));

    }
}

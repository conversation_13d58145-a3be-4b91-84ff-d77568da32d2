package com.tmb.oneapp.transferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.authen.client.OauthFeignClient;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenVerifyRefResponse;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.transferservice.feature.authen.model.VerifyPinCacheRequest;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OauthServiceTest {
    @Mock
    OauthFeignClient oauthFeignClient;

    HttpHeaders headers;
    String crmId;
    String correlationId;
    String key;
    String module;
    String ipAddress;

    @InjectMocks
    OauthService oauthService;

    @BeforeEach
    void setup() {
        crmId = "001100000000000000000012004012";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        key = "trans-id";
        module = "transfer";
        ipAddress = "********";

        headers = new HttpHeaders();
        headers.set(HEADER_CRM_ID, crmId);
        headers.set(HEADER_CORRELATION_ID, correlationId);
        headers.set(IP_ADDRESS, ipAddress);
    }

    @Test
    void verifyPinCacheV2WhenSuccess() {
        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData("success");
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        when(oauthFeignClient.getVerifyPinCache(eq(correlationId), any(VerifyPinCacheRequest.class))).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        assertDoesNotThrow(() -> oauthService.verifyPinCache(correlationId, crmId, key, module));
    }

    @Test
    void verifyPinCacheWhenFailed() {
        when(oauthFeignClient.getVerifyPinCache(eq(correlationId), any(VerifyPinCacheRequest.class))).thenThrow(FeignException.FeignClientException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> oauthService.verifyPinCache(correlationId, crmId, key, module));

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void verifyCommonAuthenWithPayloadSuccessTest() {
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setRefId("transId_000111");
        commonAuthenWithPayloadRequest.setAmount("200.00");
        commonAuthenWithPayloadRequest.setDailyAmount("10000.00");
        commonAuthenWithPayloadRequest.setFlowName("Home-Lander-Star-Light");
        commonAuthenWithPayloadRequest.setFeatureId(TRANSFER_FEATURE_ID);
        commonAuthenWithPayloadRequest.setBankCode(TTB_BANK_CODE);

        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("200.0")
                .setDailyAmount("10000.00")
                .setFeatureId(TRANSFER_FEATURE_ID)
                .setBankCode(TTB_BANK_CODE)
        );

        Mockito.when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> oauthService.verifyCommonAuthenWithPayload(headers, commonAuthenWithPayloadRequest));
    }

    @Test
    void verifyCommonAuthenWithPayloadSuccessWithNullDailyAmountTest() {
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setRefId("transId_000111");
        commonAuthenWithPayloadRequest.setAmount("200.00");
        commonAuthenWithPayloadRequest.setDailyAmount(null);
        commonAuthenWithPayloadRequest.setFlowName("Home-Lander-Star-Light");
        commonAuthenWithPayloadRequest.setFeatureId(TRANSFER_FEATURE_ID);
        commonAuthenWithPayloadRequest.setBankCode(TTB_BANK_CODE);

        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("200.0")
                .setDailyAmount("")
                .setFeatureId(TRANSFER_FEATURE_ID)
                .setBankCode(TTB_BANK_CODE)
        );

        Mockito.when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> oauthService.verifyCommonAuthenWithPayload(headers, commonAuthenWithPayloadRequest));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "999.00, 100.00, not changed, not changed",
            "100.00, 999.00, not changed, not changed",
            "100.00, 100.00, changed, not changed",
            "100.00, 100.00, not changed, changed",
    }, delimiterString = ",")
    void verifyCommonAuthenWithPayloadWhenPayloadChangeShouldThrowsExceptionTest(String amount, String dailyAmount, String featureId, String bankCode) {
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setAmount(amount);
        commonAuthenWithPayloadRequest.setDailyAmount(dailyAmount);
        commonAuthenWithPayloadRequest.setFeatureId(featureId);
        commonAuthenWithPayloadRequest.setBankCode(bankCode);

        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("100.00")
                .setDailyAmount("100.00")
                .setFeatureId("not changed")
                .setBankCode("not changed")
        );

        Mockito.when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertThrows(TMBCommonException.class, () -> oauthService.verifyCommonAuthenWithPayload(headers, commonAuthenWithPayloadRequest));
    }


    @Test
    void verifyCommonAuthenWithPayloadForTermDepositOldVersionSuccessTest() {
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setRefId("transId_000111");
        commonAuthenWithPayloadRequest.setAmount("200.00");
        commonAuthenWithPayloadRequest.setDailyAmount("10000.00");
        commonAuthenWithPayloadRequest.setFlowName("Home-Lander-Star-Light");

        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("200.0")
                .setDailyAmount("10000.00"));

        Mockito.when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> oauthService.verifyCommonAuthenWithPayloadForTermDepositOldVersion(headers, commonAuthenWithPayloadRequest));
    }

    @Test
    void verifyCommonAuthenWithPayloadSuccessWithNullDailyAmountTestForTermDepositOldVersion() {
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setRefId("transId_000111");
        commonAuthenWithPayloadRequest.setAmount("200.00");
        commonAuthenWithPayloadRequest.setDailyAmount(null);
        commonAuthenWithPayloadRequest.setFlowName("Home-Lander-Star-Light");

        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("200.0")
                .setDailyAmount(""));

        Mockito.when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> oauthService.verifyCommonAuthenWithPayloadForTermDepositOldVersion(headers, commonAuthenWithPayloadRequest));
    }

    @Test
    void verifyCommonAuthenWithPayloadWhenPayloadForTermDepositOldVersionChangeShouldThrowsExceptionTest() {
        String changeAmount = "1.00";
        String changeDailyAmount = "2.00";
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setRefId("transId_000111");
        commonAuthenWithPayloadRequest.setAmount(changeAmount);
        commonAuthenWithPayloadRequest.setDailyAmount(changeDailyAmount);
        commonAuthenWithPayloadRequest.setFlowName("Home-Lander-Star-Light");

        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("200.00")
                .setDailyAmount("10000.00"));

        Mockito.when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertThrows(TMBCommonException.class, () -> oauthService.verifyCommonAuthenWithPayloadForTermDepositOldVersion(headers, commonAuthenWithPayloadRequest));
    }

    @Test
    void verifyCommonAuthenticationSuccessTest() {
        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse()
                .setAmount("200.00")
                .setDailyAmount("10000.00"));
        when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> oauthService.verifyCommonAuthentication(headers, new CommonAuthenVerifyRefRequest()));
    }

    @Test
    void verifyCommonAuthenticationExceptionTest() {
        when(oauthFeignClient.verifyCommonAuthen(any(HttpHeaders.class), any())).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> oauthService.verifyCommonAuthentication(headers, new CommonAuthenVerifyRefRequest()));
    }

    @ParameterizedTest
    @ValueSource(strings = {HEADER_CRM_ID, HEADER_CORRELATION_ID, IP_ADDRESS})
    void verifyCommonAuthenticationWhenRequireHeadersNullExceptionTest(String keyRequireNull) {
        headers.set(keyRequireNull, null);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> oauthService.verifyCommonAuthentication(headers, new CommonAuthenVerifyRefRequest()));

        Assertions.assertEquals(ResponseCode.INVALID_REQUEST.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatus().value());
    }
}

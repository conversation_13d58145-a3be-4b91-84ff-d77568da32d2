package com.tmb.oneapp.transferservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.internationaltransfer.OTTCountry;
import com.tmb.common.util.TMBUtils;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.*;
import com.tmb.oneapp.transferservice.model.InternationalTransferPurpose;
import com.tmb.oneapp.transferservice.feign.ODSFeignClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InternationalTransferDataServiceTest {
    @Mock
    MongoTemplate mongoTemplate;

    @Mock
    CacheService cacheService;

    @Mock
    ODSFeignClient odsFeignClient;

    @InjectMocks
    InternationalTransferDataService internationalTransferDataService;

    @Test
    void testCacheFoundPurposeDataWhenFetchPurposeMasterDataThenSuccess() throws TMBCommonException {
        String cacheHpModuleConfig = "[{\"transfer_purpose_code\": \"318231\"," +
                "    \"transfer_purpose_seq\": \"01\"," +
                "    \"transfer_purpose_name\": \"Cost of Goods/ค่าสินค้าเข้าและค่าสินค้าออก\"," +
                "    \"transfer_purpose_recommended\": \"Invoice/เอกสารเรียกเก็บเงินจากผู้ขาย หรือใบสรุปค่าสินค้า\"," +
                "    \"transfer_purpose_status\": \"Y\"," +
                "    \"recommended_doc_flag\": \"Y\"," +
                "    \"special_remark_flag\": \"Y\"," +
                "    \"recommended_doc_th\": \"เอกสารเรียกเก็บเงินจากผู้ขาย หรือใบสรุปค่าสินค้า\"," +
                "    \"recommended_doc_en\": \"Invoice\"}]";
        when(cacheService.get(TransferServiceConstant.COMMON_TRANSFER_PURPOSE_MASTER_DATA)).thenReturn(cacheHpModuleConfig);

        List<InternationalTransferPurpose> actual = internationalTransferDataService.fetchPurposeMasterData();

        Assertions.assertEquals("318231", actual.get(0).getTransferPurposeCode());
    }

    @Test
    void testGetCachePurposeDataWithExceptionWhenRedisDownShouldReturnConfigFromMongo() throws TMBCommonException, JsonProcessingException {
        String cacheHpModuleConfig = "[{\"transfer_purpose_code\": \"318231\"," +
                "    \"transfer_purpose_seq\": \"01\"," +
                "    \"transfer_purpose_name\": \"Cost of Goods/ค่าสินค้าเข้าและค่าสินค้าออก\"," +
                "    \"transfer_purpose_recommended\": \"Invoice/เอกสารเรียกเก็บเงินจากผู้ขาย หรือใบสรุปค่าสินค้า\"," +
                "    \"transfer_purpose_status\": \"Y\"," +
                "    \"recommended_doc_flag\": \"Y\"," +
                "    \"special_remark_flag\": \"Y\"," +
                "    \"recommended_doc_th\": \"เอกสารเรียกเก็บเงินจากผู้ขาย หรือใบสรุปค่าสินค้า\"," +
                "    \"recommended_doc_en\": \"Invoice\"}]";
        List<InternationalTransferPurpose> purposeList = (List<InternationalTransferPurpose>) TMBUtils.convertStringToJavaObjWithTypeRef(cacheHpModuleConfig, new TypeReference<List<InternationalTransferPurpose>>() {});
        when(cacheService.get(TransferServiceConstant.COMMON_TRANSFER_PURPOSE_MASTER_DATA)).thenThrow(new RuntimeException());
        when(mongoTemplate.findAll(InternationalTransferPurpose.class)).thenReturn(purposeList);

        List<InternationalTransferPurpose> actual = internationalTransferDataService.fetchPurposeMasterData();

        Assertions.assertEquals("318231", actual.get(0).getTransferPurposeCode());
    }

    @Test
    void testCacheOttCountryWhenFetchCountryDataThenSuccess() throws TMBCommonException {
        String cacheHpModuleConfig = "[{" +
                "    \"cl_code\": \"US\"," +
                "    \"cl_desc1\": \"UNITED STATES\"," +
                "    \"cl_desc2\": \"UNITED STATES\"," +
                "    \"cl_name\": \"\"," +
                "    \"cl_type\": \"B5\"," +
                "    \"image_url\": \"https://firebasestorage.googleapis.com/v0/b/oneapp-vit.appspot.com/o/images%2Fcountry%2Fus%2Fus.png?alt=media&token=403000ea-a9a2-43df-bd55-0a04ab7bd5e3\"" +
                "}]";
        when(cacheService.get(TransferServiceConstant.COMMON_TRANSFER_OTT_COUNTRY_MASTER_DATA)).thenReturn(cacheHpModuleConfig);

        List<OTTCountry> actual = internationalTransferDataService.fetchOttCountryMasterData();

        Assertions.assertEquals("US", actual.get(0).getClCode());
    }

    @Test
    void testGetCacheOttCountryWithExceptionWhenRedisDownShouldReturnCountryDataFromMongo() throws TMBCommonException, JsonProcessingException {
        String ottCountryMaster = "[{" +
                "    \"cl_code\": \"US\"," +
                "    \"cl_desc1\": \"UNITED STATES\"," +
                "    \"cl_desc2\": \"UNITED STATES\"," +
                "    \"cl_name\": \"\"," +
                "    \"cl_type\": \"B5\"," +
                "    \"image_url\": \"https://firebasestorage.googleapis.com/v0/b/oneapp-vit.appspot.com/o/images%2Fcountry%2Fus%2Fus.png?alt=media&token=403000ea-a9a2-43df-bd55-0a04ab7bd5e3\"" +
                "}]";
        List<OTTCountry> countryList = (List<OTTCountry>) TMBUtils.convertStringToJavaObjWithTypeRef(ottCountryMaster, new TypeReference<List<OTTCountry>>() {});
        when(cacheService.get(TransferServiceConstant.COMMON_TRANSFER_OTT_COUNTRY_MASTER_DATA)).thenThrow(new RuntimeException());
        when(mongoTemplate.findAll(OTTCountry.class)).thenReturn(countryList);

        List<OTTCountry> actual = internationalTransferDataService.fetchOttCountryMasterData();

        Assertions.assertEquals("US", actual.get(0).getClCode());
    }

    @Test
    void testGetInterestRateWhenSuccess() throws TMBCommonException, IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_REQUEST_UUID, UUID.randomUUID().toString());
        headers.add(HEADER_APP_ID, CHANNEL_MB);
        headers.add(HEADER_SERVICE_NAME, "get-rate-by-product-group");

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        String requestDateTime = simpleDateFormat.format(new Date());
        headers.add(HEADER_REQUEST_DATE_TIME, requestDateTime);

        InterestRateODSResponse interestRateODSResponse = new InterestRateODSResponse();
        TmbStatus status = new TmbStatus();
        status.setCode("0000");
        status.setMessage("Success");
        interestRateODSResponse.setStatus(status);
        List<InterestRateResponse> rateResponseList = new ArrayList<>();
        InterestRateResponse interestRate = new InterestRateResponse();
        interestRate.setISMAXOFGRP("Y");
        interestRate.setRTGRP01(1.0);
        interestRate.setInActiveFlag("N");
        rateResponseList.add(interestRate);
        interestRateODSResponse.setInterestRates(rateResponseList);

        ODSRequest odsRequest = new ODSRequest();
        ODSService odsService = new ODSService();
        InterestRates interestRates = new InterestRates();
        interestRates.setPrdgrp("Prdgrp");
        interestRates.setInterestRatesFromOTT(rateResponseList);
        odsService.setInterestRates(Collections.singletonList(interestRates));

        odsRequest.setProductGroup("productGroup");
        odsRequest.setOdsService(odsService);

        ResponseEntity<InterestRateODSResponse> odsResult = ResponseEntity.ok(interestRateODSResponse);
        doReturn(odsResult).when(odsFeignClient).getInterestRate(any(),any());

        InterestRateResponse actual = internationalTransferDataService.getInterestRate("221");
        Assertions.assertEquals(1.0,actual.getRTGRP01());
        Assertions.assertEquals("0000", interestRateODSResponse.getStatus().getCode());
        Assertions.assertEquals("Success", interestRateODSResponse.getStatus().getMessage());
        Assertions.assertEquals("Prdgrp", odsService.getInterestRates().get(0).getPrdgrp());
        Assertions.assertEquals(1.0, odsService.getInterestRates().get(0).getInterestRatesFromOTT().get(0).getRTGRP01());
    }

}
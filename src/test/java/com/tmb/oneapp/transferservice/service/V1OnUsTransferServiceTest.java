package com.tmb.oneapp.transferservice.service;

import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.account.client.AccountsServiceClient;
import com.tmb.oneapp.transferservice.feature.account.model.TDRequest;
import com.tmb.oneapp.transferservice.feature.account.model.TDResponse;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.ete.model.AccountStatus;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteTransferAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.OwnTMBToFromAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.TMBDataSuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferETERequest;
import com.tmb.oneapp.transferservice.feature.ete.service.EteService;
import com.tmb.oneapp.transferservice.feature.financiallog.service.FinancialLogService;
import com.tmb.oneapp.transferservice.model.CommonAuthenResult;
import com.tmb.oneapp.transferservice.model.FXCache;
import com.tmb.oneapp.transferservice.model.FXExchangeRate;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOnUsConfirmRequest;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferConfirmResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferAccount;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import feign.FeignException;
import jakarta.validation.Valid;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.List;

import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.APP_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FLOW_NAME_TRANSFER;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE_3DIGITS;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;


@ExtendWith(MockitoExtension.class)
class V1OnUsTransferServiceTest {

    @Mock
    private V1TransfersServiceHelper transfersServiceHelper;

    @Mock
    private EteService eteService;

    @Mock
    private CustomerService customerService;

    @Mock
    private FaceRecognizeService frService;

    @Mock
    private FinancialLogService financialLogService;

    @Mock
    private V1OnUsTransferService onUsTransferServiceMock;
    @Mock
    private OauthService oauthService;
    @Mock
    private AccountsServiceClient accountsServiceClient;
    @Mock
    private ConfigurationService configurationService;

    @InjectMocks
    private V1OnUsTransferService onUsTransferService;

    @Test
    void validate_CdaAccountShouldReturnTransId() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        ReflectionTestUtils.setField(onUsTransferService, "tdRequireAppVersion", "6.0.0");

        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(1));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setDepositNo("**********");
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("**********");
        depositAccount.setProductNickname("MyAcc");
        depositAccount.setAccountName("testAccountName");
        request.setDepositAccount(depositAccount);
        String crmId = "123";
        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "6.0.0");
        String correlationId = "abc";

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("50000"));
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        Mockito.when(transfersServiceHelper.generateTransactionRef(anyString(), anyInt())).thenReturn("ref1");
        Mockito.when(transfersServiceHelper.generateTransId(any())).thenReturn("trans1");
        EteDepositTermResponse eteDepositTermResponse = getEteDepositTermResponse();
        Mockito.when(eteService.getDepositWithdrawalInfo(any())).thenReturn(eteDepositTermResponse);
        EteTransferAccount eteAccount = new EteTransferAccount();
        Mockito.when(eteService.getDepositAccount(anyString(), anyString(), any(), any())).thenReturn(eteAccount);

        OwnTMBToFromAccount fromAccount = new OwnTMBToFromAccount();
        fromAccount.setAccountName("testSender");

        TMBDataSuccess eteData = new TMBDataSuccess();
        eteData.setFromAccount(fromAccount);
        eteData.setFeeAmount(10.0);

        FundTransferOwnTMBETESuccess eteResponse = new FundTransferOwnTMBETESuccess();
        eteResponse.setData(eteData);

        Mockito.when(eteService.validateFundTransfer(anyString(), anyString(), anyString(), any(), anyString())).thenReturn(eteResponse);
        Mockito.when(customerService.getAllAccountsTransfer(any(), any())).thenReturn(List.of(depositAccount));

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(true);
        faceRecognizeResponse.setPaymentAccuUsgAmt(new BigDecimal(10000));

        V1OnUsTransferValidateResponse actualResponse = onUsTransferService.validate(request, crmId, correlationId, headers);

        Assertions.assertNotNull(actualResponse.getTransId());
        verify(transfersServiceHelper, never()).validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any());
    }

    @NotNull
    private static EteDepositTermResponse getEteDepositTermResponse() {
        EteDepositTermResponse eteDepositTermResponse = new EteDepositTermResponse();
        EteDepositTermResponse.Account eteAccount2 = new EteDepositTermResponse.Account();
        EteDepositTermResponse.WithdrawalInfo withDrawInfo = new EteDepositTermResponse.WithdrawalInfo();
        withDrawInfo.setTotalInterest(BigDecimal.valueOf(11));
        withDrawInfo.setTaxAmounts(BigDecimal.valueOf(7));
        withDrawInfo.setInterestAmounts(BigDecimal.valueOf(10));
        withDrawInfo.setInterestRate("7");
        withDrawInfo.setPenaltyAmounts(BigDecimal.valueOf(10));
        withDrawInfo.setOutstandingBalances(BigDecimal.valueOf(10000));
        eteAccount2.setWithdrawalInfo(withDrawInfo);
        eteDepositTermResponse.setAccount(eteAccount2);
        return eteDepositTermResponse;
    }

    @ParameterizedTest
    @CsvSource({
            "5.9.9",
            "2.0.0",
            "''"
    })
    void validate_CdaAccount_AppVersionLowerThanRequireAppVersionShouldThrowException(String currentAppVersion) throws TMBCommonException {
        ReflectionTestUtils.setField(onUsTransferService, "tdRequireAppVersion", "6.0.0");

        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, currentAppVersion);

        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(1));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setDepositNo("**********");

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("**********");
        depositAccount.setProductNickname("MyAcc");
        depositAccount.setAccountName("testAccountName");
        request.setDepositAccount(depositAccount);
        Mockito.when(customerService.getAllAccountsTransfer(any(), any())).thenReturn(List.of(depositAccount));

        String crmId = "123";
        String correlationId = "abc";

        TMBCommonException a = assertThrows(TMBCommonException.class, () -> onUsTransferService.validate(request, crmId, correlationId, headers));

        Assertions.assertEquals(ResponseCode.PB_UPDATE_APP.getCode(), a.getErrorCode());
    }

    @ParameterizedTest
    @CsvSource({"**********,**********,0001", "**********,**********,0002"})
    void validate_NormalFlowShouldReturnTransId(String formAccount, String toAccount, String acctCtl2)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(5000));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo(formAccount);
        request.setToAccountNo(toAccount);
        request.setDepositNo(toAccount);
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber(formAccount);
        depositAccount.setProductNickname("MyAcc");
        depositAccount.setAccountName("testAccountName");
        depositAccount.setAcctCtl2(acctCtl2);
        if(!"0001".equalsIgnoreCase(acctCtl2)) {
            request.setFxTransId("FX_001********0000000000006192730_1de16635-f0f0-4acc-88fd-104bb8815ac2");
            depositAccount.setBalanceCurrency("EUR");
        }
        request.setDepositAccount(depositAccount);
        String crmId = "123";
        HttpHeaders headers = new HttpHeaders();
        String correlationId = "abc";

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        Mockito.when(transfersServiceHelper.generateTransactionRef(anyString(), anyInt())).thenReturn("ref1");
        Mockito.when(transfersServiceHelper.generateTransId(any())).thenReturn("trans1");
        EteTransferAccount eteAccount = new EteTransferAccount();
        Mockito.lenient().when(eteService.getDepositAccount(anyString(), anyString(), any(), any(), any())).thenReturn(eteAccount);
        Mockito.lenient().when(eteService.getDepositAccount(anyString(), anyString(), any(), any())).thenReturn(eteAccount);

        OwnTMBToFromAccount fromAccount = new OwnTMBToFromAccount();
        fromAccount.setAccountName("testSender");

        TMBDataSuccess eteData = new TMBDataSuccess();
        eteData.setFromAccount(fromAccount);
        eteData.setFeeAmount(10.0);

        FundTransferOwnTMBETESuccess eteResponse = new FundTransferOwnTMBETESuccess();
        eteResponse.setData(eteData);

        Mockito.lenient().when(eteService.validateFundTransfer(anyString(), anyString(), anyString(), any(), anyString())).thenReturn(eteResponse);
        Mockito.lenient().when(eteService.validateFundTransferV3(any(), any(), any(), any(), any(), any(), any())).thenReturn(eteResponse);
        Mockito.when(customerService.getAllAccountsTransfer(any(), any())).thenReturn(List.of(depositAccount));

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(true);
        faceRecognizeResponse.setPaymentAccuUsgAmt(new BigDecimal(10000));

        VerifyTransactionResult verifyTransactionResult = new VerifyTransactionResult(false, faceRecognizeResponse, new CommonAuthenResult());
        Mockito.when(transfersServiceHelper.validateIsRequireVerifyTransaction(any(), any(), anyBoolean(), any())).thenReturn(verifyTransactionResult);

        FXCache fxCache = new FXCache();
        FXExchangeRate fxExchangeRate = new FXExchangeRate();
        fxExchangeRate.setRtCcy("EUR");
        fxExchangeRate.setUnitCurrency("1");
        fxExchangeRate.setBuyRate("1.00");
        fxCache.setFxExchangeRates(List.of(fxExchangeRate));
        Mockito.lenient().when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(fxCache);

        Mockito.lenient().when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(fxCache);
        Mockito.lenient().when(configurationService.getTransferConfiguration())
                .thenReturn(TransferModuleModel.builder().fcdTdMinimumAmount(BigDecimal.valueOf(5000)).build());

        V1OnUsTransferValidateResponse actualResponse = onUsTransferService.validate(request, crmId, correlationId, headers);

        Assertions.assertNotNull(actualResponse.getTransId());
    }

    @NotNull@SuppressWarnings("SameParameterValue")
    private static V1OnUsValidateRequest getV1OnUsValidateRequest(String fromAccountNo,
                                                                  String toAccountNo,
                                                                  String depositNo) {
        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(1));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo(fromAccountNo);
        request.setToAccountNo(toAccountNo);
        request.setDepositNo(depositNo);
        request.setAmount(BigDecimal.valueOf(100));
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber(fromAccountNo);
        depositAccount.setProductNickname("MyAcc");
        request.setDepositAccount(depositAccount);
        return request;
    }

    @Test
    void validate_CheckRequiredPinFromCustomerServiceShouldReturnTrue() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(1));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setDepositNo("**********");
        request.setAmount(BigDecimal.valueOf(100));
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("**********");
        depositAccount.setProductNickname("MyAcc");
        depositAccount.setAccountName("testAccountName");
        request.setDepositAccount(depositAccount);
        String crmId = "123";
        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        String correlationId = "abc";

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeSeetingFlag("N");
        crmProfile.setPinFreeTrLimit(0.1);
        crmProfile.setPinFreeTrLimit(500);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("500.00"));
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        Mockito.when(transfersServiceHelper.generateTransactionRef(anyString(), anyInt())).thenReturn("ref1");
        Mockito.when(transfersServiceHelper.generateTransId(any())).thenReturn("trans1");
        EteTransferAccount eteAccount = new EteTransferAccount();
        Mockito.when(eteService.getDepositAccount(anyString(), anyString(), any(), any())).thenReturn(eteAccount);

        OwnTMBToFromAccount fromAccount = new OwnTMBToFromAccount();
        fromAccount.setAccountName("testSender");

        TMBDataSuccess eteData = new TMBDataSuccess();
        eteData.setFromAccount(fromAccount);
        eteData.setFeeAmount(10.0);

        FundTransferOwnTMBETESuccess eteResponse = new FundTransferOwnTMBETESuccess();
        eteResponse.setData(eteData);

        Mockito.when(eteService.validateFundTransfer(anyString(), anyString(), anyString(), any(), anyString())).thenReturn(eteResponse);
        Mockito.when(customerService.getAllAccountsTransfer(any(), any())).thenReturn(List.of(depositAccount));

        VerifyTransactionResult verifyTransactionResult = getVerifyTransactionResult();
        Mockito.when(transfersServiceHelper.validateIsRequireVerifyTransaction(headers, request.getAmount(), false, crmProfile)).thenReturn(verifyTransactionResult);

        V1OnUsTransferValidateResponse actualResponse = onUsTransferService.validate(request, crmId, correlationId, headers);

        BigDecimal expectedTotalPaymentAccumulateUsage = crmProfile.getPaymentAccuUsgAmt().add(request.getAmount());
        Assertions.assertNotNull(actualResponse.getTransId());
        Assertions.assertTrue(actualResponse.getIsRequireConfirmPin());
        Assertions.assertTrue(actualResponse.getIsRequireFr());
        Assertions.assertTrue(actualResponse.getIsRequireCommonAuthen());
        Assertions.assertEquals(TRANSFER_FEATURE_ID, actualResponse.getCommonAuthenticationInformation().getFeatureId());
        Assertions.assertEquals(TTB_BANK_CODE_3DIGITS, actualResponse.getCommonAuthenticationInformation().getToBankCode());
        Assertions.assertEquals(FLOW_NAME_TRANSFER, actualResponse.getCommonAuthenticationInformation().getFlowName());
        Assertions.assertEquals(expectedTotalPaymentAccumulateUsage, actualResponse.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
    }

    @NotNull
    private static VerifyTransactionResult getVerifyTransactionResult() {
        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();
        faceRecognizeResponse.setIsRequireFr(true);
        faceRecognizeResponse.setPaymentAccuUsgAmt(new BigDecimal(10000));

        CommonAuthenResult commonAuthenResult = new CommonAuthenResult();
        commonAuthenResult.setRequireCommonAuthen(true);
        commonAuthenResult.setIsForceFr(true);
        commonAuthenResult.setPinFree(true);
        return new VerifyTransactionResult(true, faceRecognizeResponse, commonAuthenResult);
    }

    @Test
    void validate_onUs_failed() throws TMBCommonException {

        V1OnUsValidateRequest request = getV1OnUsValidateRequest("**********", "**********", "**********");
        String crmId = "123";
        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        String correlationId = "abc";

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeSeetingFlag("Y");
        crmProfile.setPinFreeTrLimit(100000.0);
        crmProfile.setPinFreeTrLimit(500);
        crmProfile.setPinFreeTxnCount(4);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenThrow(FeignException.class);
        assertThrows(FeignException.class, () -> onUsTransferService.validate(request, crmId, correlationId, headers));
    }

    @Test
    void confirm_NormalFlowWhenTransferToOwnAccountShouldReturnRefNo() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse, ParseException {
        boolean isToOwnAccount = true;
        @Valid TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        request.setFrUuid("frUUid");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();
        headers.set(IP_ADDRESS, "127.0.01");

        V1TransferData transferData = getTransferData(isToOwnAccount, "B", new BigDecimal("********.00"));
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);

        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        eteFromAccount.setAvailBalance("100000");
        eteConfirmData.setFromAccount(eteFromAccount);
        eteFromAccount.setAccountType(TransferServiceConstant.ACCOUNT_TYPE_SAVING);
        ArgumentCaptor<TransferETERequest> transferETERequestArgumentCaptor = ArgumentCaptor.forClass(TransferETERequest.class);
        Mockito.when(eteService.confirmTransfer(transferETERequestArgumentCaptor.capture())).thenReturn(eteConfirmData);

        V1OnUsTransferConfirmResponse actualResponse = onUsTransferService.confirm(request, crmId, correlationId, headers);

        Assertions.assertNotNull(actualResponse.getReferenceNo());
        Assertions.assertEquals(new BigDecimal("********.00"), transferETERequestArgumentCaptor.getValue().getAmount());

        verify(transfersServiceHelper, times(1)).publishActivityCustomSlip(any());
        verify(frService, times(0)).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));
    }

    @NotNull
    private static V1TransferData getTransferData(boolean isToOwnAccount, String accountType, BigDecimal amount) {
        V1TransferData transferData = new V1TransferData();
        transferData.setTransactionReference("ref1");
        transferData.setIsToOwnAccount(isToOwnAccount);
        transferData.setIsRequireFr(Boolean.TRUE);
        transferData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setFeeFromETE(BigDecimal.ONE);
        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setToAccount(toAccount);
        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType(accountType);
        transferData.setFromAccount(fromAccount);
        transferData.setAmount(amount);
        return transferData;
    }

    @Test
    void confirm_NormalFlowWhenTransferToOtherTTBReturnRefNo() throws TMBCommonException, IOException, WriterException, ParseException, TMBCustomCommonExceptionWithResponse {
        boolean isToOwnAccount = false;

        @Valid TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        V1TransferData transferData = getV1TransferData(isToOwnAccount);
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);
        Mockito.when(transfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);
        doNothing().when(oauthService).verifyPinCache(any(), any(), any(), any());
        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        eteFromAccount.setAvailBalance("100000");
        eteFromAccount.setAccountType(TransferServiceConstant.ACCOUNT_TYPE_SAVING);
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);
        V1OnUsTransferConfirmResponse actualResponse = onUsTransferService.confirm(request, crmId, correlationId, headers);
        verify(transfersServiceHelper, times(1)).validateCommonFR(any(), any(), any(), any(), any());
        Assertions.assertNotNull(actualResponse.getReferenceNo());

        verify(frService, times(1)).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));
        Mockito.verify(transfersServiceHelper, times(1)).publishActivityCustomSlip(Mockito.any(CustomSlipCompleteActivityLog.class));
    }

    @Test
    void confirmWhenTransactionLimited() throws TMBCommonException {
        boolean isToOwnAccount = false;

        @Valid TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        V1TransferData transferData = getV1TransferData(isToOwnAccount);
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);
        Mockito.when(transfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(true);
        doNothing().when(oauthService).verifyPinCache(any(), any(), any(), any());

        TMBCommonException tmbCommonException = Assertions.assertThrows(TMBCommonException.class,
                () -> onUsTransferService.confirm(request, crmId, correlationId, headers));
        Assertions.assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), tmbCommonException.getErrorCode());
    }

    @NotNull
    private static V1TransferData getV1TransferData(boolean isToOwnAccount) {
        V1TransferData transferData = getTransferData(isToOwnAccount, "A", "1111", "222");
        transferData.setRequirePin(true);
        transferData.setIsRequireFr(true);
        return transferData;
    }

    @Test
    void confirm_NormalFlowWhenTransferToOtherTTBAndAppVersionMoreThanEqualsCommonAuthenRequireAppVersion_ShouldValidateWithCommonAuthen() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        ReflectionTestUtils.setField(onUsTransferService, "commonAuthenRequireAppVersion", "5.12.0");
        String appVersionMoreThanEqualsRequire = "5.12.0";
        boolean isRequireCommonAuthen = true;
        boolean isToOwnAccount = false;

        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();
        headers.add(APP_VERSION, appVersionMoreThanEqualsRequire);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        V1TransferData transferData = getV1TransferData(isRequireCommonAuthen, isToOwnAccount);
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);
        Mockito.when(transfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);
        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        eteFromAccount.setAvailBalance("100000");
        eteFromAccount.setAccountType(TransferServiceConstant.ACCOUNT_TYPE_SAVING);
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);
        Assertions.assertDoesNotThrow(() -> onUsTransferService.confirm(request, crmId, correlationId, headers));


        verify(oauthService, times(1)).verifyCommonAuthenWithPayload(any(HttpHeaders.class), any(CommonAuthenWithPayloadRequest.class));
        verify(transfersServiceHelper, times(1)).updateUsageAccumulation(anyString(), anyString(), any(), any(V1CrmProfile.class), anyBoolean());

        verify(oauthService, never()).verifyPinCache(any(), any(), any(), any());
        verify(transfersServiceHelper, never()).validateCommonFR(any(), any(), any(), any(), any());
        verify(frService, never()).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(), anyBoolean(), any());
        verify(transfersServiceHelper, never()).updateDailyUsage(anyString(), anyString(), any(), any(), anyBoolean());
    }

    @NotNull
    private static V1TransferData getV1TransferData(boolean isRequireCommonAuthen, boolean isToOwnAccount) {
        V1TransferData transferData = new V1TransferData();
        transferData.setRequireCommonAuthen(isRequireCommonAuthen);
        transferData.setCommonAuthenticationInformation(new CommonAuthenticationInformation());
        transferData.setTransactionReference("ref1");
        transferData.setIsToOwnAccount(isToOwnAccount);
        transferData.setIsRequireFr(Boolean.TRUE);
        transferData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setFeeFromETE(BigDecimal.ONE);
        transferData.setFlow("Home-Lander");
        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType("A");
        toAccount.setAccountNo("1111");
        transferData.setToAccount(toAccount);
        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo("222");
        fromAccount.setAccountType("B");
        transferData.setFromAccount(fromAccount);
        transferData.setAmount(BigDecimal.valueOf(10));
        transferData.setRequirePin(true);
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        transferData.setIsRequireFr(true);
        return transferData;
    }

    @Test
    void confirm_NormalFlowWhenTransferToOtherTTBAndAppVersionMoreThanEqualsCommonAuthenRequireAppVersionButNotRequireCommonAuthen_ShouldUpdatePinFreeCount() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        ReflectionTestUtils.setField(onUsTransferService, "commonAuthenRequireAppVersion", "5.12.0");
        String appVersionMoreThanEqualsRequire = "5.12.0";
        boolean isRequireCommonAuthen = false;
        boolean isToOwnAccount = false;

        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();
        headers.add(APP_VERSION, appVersionMoreThanEqualsRequire);

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        V1TransferData transferData = getV1TransferData(isRequireCommonAuthen, isToOwnAccount);
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);
        Mockito.when(transfersServiceHelper.checkTransactionLimited(any(), anyDouble())).thenReturn(false);
        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        eteFromAccount.setAvailBalance("100000");
        eteFromAccount.setAccountType(TransferServiceConstant.ACCOUNT_TYPE_SAVING);
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);
        Assertions.assertDoesNotThrow(() -> onUsTransferService.confirm(request, crmId, correlationId, headers));


        verify(transfersServiceHelper, times(1)).updatePinFreeCount(anyString(), anyString(), any(V1CrmProfile.class));
        verify(transfersServiceHelper, times(1)).updateUsageAccumulation(anyString(), anyString(), any(), any(V1CrmProfile.class), anyBoolean());

        verify(oauthService, never()).verifyCommonAuthenWithPayload(any(HttpHeaders.class), any(CommonAuthenWithPayloadRequest.class));
        verify(oauthService, never()).verifyPinCache(any(), any(), any(), any());
        verify(transfersServiceHelper, never()).validateCommonFR(any(), any(), any(), any(), any());
        verify(frService, never()).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(), anyBoolean(), any());
        verify(transfersServiceHelper, never()).updateDailyUsage(anyString(), anyString(), any(), any(), anyBoolean());
    }

    @Test
    void confirm_WhenTransferToTDShouldReturnRemainingBalanceTest() throws TMBCommonException, IOException, WriterException, ParseException, TMBCustomCommonExceptionWithResponse {
        boolean isToOwnAccount = true;

        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();

        V1TransferData transferData = getTransferData(isToOwnAccount, ACCOUNT_TYPE_TERM_DEPOSIT, "**********", "**********");
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);

        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        String ignoreNormalBalanceWhenTD = "0";
        eteFromAccount.setAvailBalance(ignoreNormalBalanceWhenTD);
        eteFromAccount.setAccountType(ACCOUNT_TYPE_TERM_DEPOSIT);
        eteFromAccount.setAccountNo("**********012");
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);

        TmbOneServiceResponse<TDResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus());
        tmbOneServiceResponse.getStatus().setCode(ResponseCode.SUCCESS.getCode());
        tmbOneServiceResponse.setData(new TDResponse());
        String accountBalanceFromTD = "9999999";
        tmbOneServiceResponse.getData().setAccountBalance(accountBalanceFromTD);
        ArgumentCaptor<TDRequest> tdRequestCaptor = ArgumentCaptor.forClass(TDRequest.class);
        Mockito.when(accountsServiceClient.fetchTermDepositDetail(eq(correlationId), tdRequestCaptor.capture())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        V1OnUsTransferConfirmResponse actualResponse = onUsTransferService.confirm(request, crmId, correlationId, headers);

        String expectAccountBalanceFromTDWithDecimal = new BigDecimal(accountBalanceFromTD).setScale(2, RoundingMode.DOWN).toString();
        Assertions.assertEquals(expectAccountBalanceFromTDWithDecimal, actualResponse.getRemainingBalance());
        Assertions.assertEquals(10, tdRequestCaptor.getValue().getAccountNo().length());
    }

    @NotNull
    private static V1TransferData getTransferData(boolean isToOwnAccount,
                                                  String accountTypeTermDeposit,
                                                  String toAccountNo,
                                                  String fromAccountNo) {
        V1TransferData transferData = new V1TransferData();
        transferData.setTransactionReference("ref1");
        transferData.setIsToOwnAccount(isToOwnAccount);
        transferData.setIsRequireFr(Boolean.TRUE);
        transferData.setPaymentAccuUsgAmt(BigDecimal.ONE);
        transferData.setFeeFromETE(BigDecimal.ONE);
        transferData.setFlow("Home-Lander");
        V1TransferAccount toAccount = new V1TransferAccount();
        toAccount.setAccountType(accountTypeTermDeposit);
        toAccount.setAccountNo(toAccountNo);
        transferData.setToAccount(toAccount);
        V1TransferAccount fromAccount = new V1TransferAccount();
        fromAccount.setAccountNo(fromAccountNo);
        fromAccount.setAccountType("B");
        transferData.setFromAccount(fromAccount);
        transferData.setAmount(BigDecimal.valueOf(10));
        transferData.setAmountTHB(BigDecimal.valueOf(10));
        return transferData;
    }

    @Test
    void confirm_WhenTransferToTDFeignExceptionShouldThrowsTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        boolean isToOwnAccount = true;
        Mockito.when(accountsServiceClient.fetchTermDepositDetail(any(String.class), any(TDRequest.class))).thenThrow(FeignException.class);

        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();

        V1TransferData transferData = getTransferData(isToOwnAccount, ACCOUNT_TYPE_TERM_DEPOSIT, "**********", "222");
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);

        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        String ignoreNormalBalanceWhenTD = "0";
        eteFromAccount.setAvailBalance(ignoreNormalBalanceWhenTD);
        eteFromAccount.setAccountType(ACCOUNT_TYPE_TERM_DEPOSIT);
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);

        assertThrows(TMBCommonException.class, () -> onUsTransferService.confirm(request, crmId, correlationId, headers));
    }

    @Test
    void confirm_WhenTransferToTDExceptionNullShouldThrowsTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        boolean isToOwnAccount = true;
        TmbOneServiceResponse<TDResponse> nullResponse = new TmbOneServiceResponse<>();
        nullResponse.setData(null);
        nullResponse.setStatus(new TmbStatus());
        nullResponse.getStatus().setCode(HttpStatus.OK.toString());
        Mockito.when(accountsServiceClient.fetchTermDepositDetail(any(String.class), any(TDRequest.class))).thenReturn(ResponseEntity.ok(nullResponse));

        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();

        V1TransferData transferData = getTransferData(isToOwnAccount, ACCOUNT_TYPE_TERM_DEPOSIT, "**********", "222");
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);

        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        String ignoreNormalBalanceWhenTD = "0";
        eteFromAccount.setAvailBalance(ignoreNormalBalanceWhenTD);
        eteFromAccount.setAccountType(ACCOUNT_TYPE_TERM_DEPOSIT);
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);

        assertThrows(TMBCommonException.class, () -> onUsTransferService.confirm(request, crmId, correlationId, headers));
    }

    @Test
    void confirm_TDFlowShouldSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();

        V1TransferData transferData = getTransferData(true, ACCOUNT_TYPE_TERM_DEPOSIT, BigDecimal.valueOf(10));
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTrLimit(500);
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        transferData.setRequireCommonAuthen(true);
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);
        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        eteFromAccount.setAvailBalance("100000");
        eteFromAccount.setAccountType(TransferServiceConstant.ACCOUNT_TYPE_SAVING);
        eteConfirmData.setFromAccount(eteFromAccount);
        Mockito.when(eteService.confirmTransfer(any())).thenReturn(eteConfirmData);

        Assertions.assertDoesNotThrow(() -> onUsTransferService.confirm(request, crmId, correlationId, headers));

        verify(oauthService, times(1)).verifyCommonAuthenWithPayloadForTermDepositOldVersion(any(HttpHeaders.class), any());
        verify(transfersServiceHelper, never()).checkTransactionLimited(any(), anyDouble());
        verify(frService, never()).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));
    }

    @ParameterizedTest
    @ValueSource(strings = {"Closed", "Frozen", "Locked"})
    void validateFcdFail(String status) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(1));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setDepositNo("**********");
        request.setAmount(BigDecimal.valueOf(5000));
        request.setFxTransId("FX_001********0000000000006192730_1de16635-f0f0-4acc-88fd-104bb8815ac2");
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber("**********");
        depositAccount.setProductNickname("MyAcc");
        depositAccount.setAccountName("testAccountName");
        depositAccount.setAcctCtl2("0003");
        depositAccount.setBalanceCurrency("EUR");
        depositAccount.setFinancialId("****************");
        request.setDepositAccount(depositAccount);
        String crmId = "123";
        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        String correlationId = "abc";

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeSeetingFlag("N");
        crmProfile.setPinFreeTrLimit(0.1);
        crmProfile.setPinFreeTrLimit(500);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("500.00"));
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);
        Mockito.when(transfersServiceHelper.generateTransactionRef(anyString(), anyInt())).thenReturn("ref1");
        Mockito.when(transfersServiceHelper.generateTransId(any())).thenReturn("trans1");
        EteTransferAccount eteAccount = new EteTransferAccount();
        eteAccount.setStatus(new AccountStatus(status));
        Mockito.when(eteService.getDepositAccount(anyString(), anyString(), any(), any(), any())).thenReturn(eteAccount);

        OwnTMBToFromAccount fromAccount = new OwnTMBToFromAccount();
        fromAccount.setAccountName("testSender");

        TMBDataSuccess eteData = new TMBDataSuccess();
        eteData.setFromAccount(fromAccount);
        eteData.setFeeAmount(10.0);

        FundTransferOwnTMBETESuccess eteResponse = new FundTransferOwnTMBETESuccess();
        eteResponse.setData(eteData);

        Mockito.when(eteService.validateFundTransferV3(anyString(), anyString(), anyString(), anyString(), anyString(),
                any(), anyString())).thenReturn(eteResponse);
        Mockito.when(customerService.getAllAccountsTransfer(any(), any())).thenReturn(List.of(depositAccount));

        VerifyTransactionResult verifyTransactionResult = getVerifyTransactionResult();
        Mockito.when(transfersServiceHelper.validateIsRequireVerifyTransaction
                (any(), any(), anyBoolean(), any())).thenReturn(verifyTransactionResult);

        FXCache fxCache = new FXCache();
        FXExchangeRate fxExchangeRate = new FXExchangeRate();
        fxExchangeRate.setRtCcy("EUR");
        fxExchangeRate.setUnitCurrency("1");
        fxExchangeRate.setBuyRate("1.00");
        fxCache.setFxExchangeRates(List.of(fxExchangeRate));
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(fxCache);

        Mockito.when(configurationService.getTransferConfiguration())
                .thenReturn(TransferModuleModel.builder().fcdTdMinimumAmount(BigDecimal.valueOf(5000)).build());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> onUsTransferService.validate(request, crmId, correlationId, headers));
        Assertions.assertEquals(ResponseCode.INVALID_ACCOUNT_STATUS.getCode(), exception.getErrorCode());
    }

    @ParameterizedTest
    @CsvSource({"EUR,35.00", "USD,30.00", "EUR,X"})
    void validateFcdTdMinimumAmount(String currency, String rate) throws TMBCommonException {

        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        request.setAmount(BigDecimal.valueOf(1));
        request.setFlow("Home-Transfer-Landing");
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setDepositNo("**********");
        request.setAmount(BigDecimal.valueOf(100));
        request.setFxTransId("FX_001********0000000000006192730_1de16635-f0f0-4acc-88fd-104bb8815ac2");
        DepositAccount fromDepositAccount = new DepositAccount();
        fromDepositAccount.setAccountNumber("**********");
        fromDepositAccount.setProductNickname("MyAcc");
        fromDepositAccount.setAccountName("testAccountName");
        fromDepositAccount.setAcctCtl2("0003");
        fromDepositAccount.setBalanceCurrency("EUR");
        fromDepositAccount.setFinancialId("****************");
        request.setDepositAccount(fromDepositAccount);
        String crmId = "123";
        HttpHeaders headers = new HttpHeaders();
        headers.set(APP_VERSION, "5.0.0");
        String correlationId = "abc";

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeSeetingFlag("N");
        crmProfile.setPinFreeTrLimit(0.1);
        crmProfile.setPinFreeTrLimit(500);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("500.00"));
        Mockito.when(customerService.getCrmProfile(any(), any())).thenReturn(crmProfile);

        DepositAccount toDepositAccount = new DepositAccount();
        toDepositAccount.setAccountNumber("**********");
        toDepositAccount.setProductNickname("MyAcc");
        toDepositAccount.setAccountName("testAccountName");
        toDepositAccount.setAcctCtl2("0003");
        toDepositAccount.setBalanceCurrency("EUR");
        toDepositAccount.setFinancialId("****************");
        Mockito.when(customerService.getAllAccountsTransfer(any(), any()))
                .thenReturn(List.of(fromDepositAccount, toDepositAccount));

        FXCache fxCache = new FXCache();
        FXExchangeRate fxExchangeRate = new FXExchangeRate();
        fxExchangeRate.setRtCcy(currency);
        fxExchangeRate.setUnitCurrency("1");
        fxExchangeRate.setBuyRate(rate);
        fxCache.setFxExchangeRates(List.of(fxExchangeRate));
        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(fxCache);

        Mockito.lenient().when(configurationService.getTransferConfiguration())
                .thenReturn(TransferModuleModel.builder().fcdTdMinimumAmount(BigDecimal.valueOf(5000)).build());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> onUsTransferService.validate(request, crmId, correlationId, headers));

        Assertions.assertEquals("EUR".equalsIgnoreCase(currency)
                && !"X".equalsIgnoreCase(rate) ?
                ResponseCode.AMOUNT_LESS_THEN_MINIMUM.getCode() :
                ResponseCode.FAILED.getCode(), exception.getErrorCode());

    }

    @Test
    void confirmFCD_NormalFlowWhenTransferToOwnAccountShouldReturnRefNo() throws TMBCommonException, IOException, WriterException, TMBCustomCommonExceptionWithResponse, ParseException {
        boolean isToOwnAccount = true;
        @Valid TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        request.setTransId("t1");
        request.setFrUuid("frUUid");
        String crmId = "123";
        String correlationId = "abc";
        HttpHeaders headers = new HttpHeaders();
        headers.set(IP_ADDRESS, "127.0.01");

        V1TransferData transferData = getTransferData(isToOwnAccount, "B", new BigDecimal("********.00"));
        transferData.getFromAccount().setAccountNo("**********");
        transferData.setAmountTHB(new BigDecimal("********.00"));

        Mockito.when(transfersServiceHelper.getTransferDraftData(anyString(), any())).thenReturn(transferData);

        TMBDataSuccess eteConfirmData = new TMBDataSuccess();
        OwnTMBToFromAccount eteFromAccount = new OwnTMBToFromAccount();
        eteFromAccount.setAvailBalance("100000");
        eteConfirmData.setFromAccount(eteFromAccount);
        eteFromAccount.setAccountType(TransferServiceConstant.ACCOUNT_TYPE_SAVING);
        ArgumentCaptor<TransferETERequest> transferETERequestArgumentCaptor = ArgumentCaptor.forClass(TransferETERequest.class);
        Mockito.when(eteService.confirmTransfer(transferETERequestArgumentCaptor.capture())).thenReturn(eteConfirmData);

        V1OnUsTransferConfirmResponse actualResponse = onUsTransferService.confirm(request, crmId, correlationId, headers);

        Assertions.assertNotNull(actualResponse.getReferenceNo());
        Assertions.assertEquals(new BigDecimal("********.00"), transferETERequestArgumentCaptor.getValue().getAmount());

        verify(transfersServiceHelper, times(1)).publishActivityCustomSlip(any());
        verify(frService, times(0)).updatePaymentAccumulateUsageAmount(anyString(), anyString(), any(BigDecimal.class), anyBoolean(), any(BigDecimal.class));
    }
}

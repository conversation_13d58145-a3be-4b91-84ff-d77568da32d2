package com.tmb.oneapp.transferservice.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

class CacheServiceTest {
    @InjectMocks
    CacheService cacheService;
    @Mock
    RedisTemplate redisTemplate;
    @Mock
    ValueOperations valueOperations;

    private String cacheKey = "banks_info";
    private String cacheValue = "{}";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void Should_set_Success() {
        assertDoesNotThrow(() -> {
            cacheService.set(cacheKey, cacheValue);
            cacheService.set(cacheKey, cacheValue, 1000L);
            cacheService.set(cacheKey, cacheValue, Duration.ofMillis(1000L));
        });
    }

    @Test
    void Should_get_Success() {
        Assertions.assertDoesNotThrow(() -> cacheService.get(cacheKey));
    }

    @Test
    void Should_delete_Success() {
        Assertions.assertDoesNotThrow(() -> cacheService.delete(cacheKey));
    }
}
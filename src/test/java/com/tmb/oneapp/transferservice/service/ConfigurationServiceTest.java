package com.tmb.oneapp.transferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.transferservice.model.CommonFee;
import com.tmb.oneapp.transferservice.model.CommonTime;
import com.tmb.oneapp.transferservice.model.SchedulePromtpay;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.repository.TransferModuleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.buffer.DataBufferLimitException;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ConfigurationServiceTest {

    @Mock
    private TransferModuleRepository transferModuleRepository;
    @InjectMocks
    private ConfigurationService configurationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getTransferConfigurationSuccess() throws TMBCommonException {
        TransferModuleModel transferModuleModel = new TransferModuleModel();
        transferModuleModel.setExchangeTransLimit("200");
        CommonFee fee = new CommonFee();
        fee.setFee("50");
        fee.setMax("100");
        fee.setMin("0");
        CommonTime time = new CommonTime();
        time.setEnd("20:00");
        time.setStart("15:00");
        SchedulePromtpay schedule = new SchedulePromtpay();
        schedule.setCitizen("***********");
        schedule.setMobile("**********");
        transferModuleModel.setPromptpayAccountFee(Collections.singletonList(fee));
        transferModuleModel.setWithdrawTdCutoffTime(time);
        transferModuleModel.setSchedulePromtpay(schedule);
        transferModuleModel.setFrTransferFlag(true);
        transferModuleModel.setFrTopupFlag(true);
        transferModuleModel.setFrScheduleFlag(true);
        transferModuleModel.setFrTransferOttFlag(true);
        transferModuleModel.setFrFinancialAccuAmount(new BigDecimal("200000.00"));
        when(transferModuleRepository.findById(any())).thenReturn(Optional.of(transferModuleModel));

        TransferModuleModel response = configurationService.getTransferConfiguration();
        assertEquals(response.getExchangeTransLimit(), "200");
    }

    @Test
    void getTransferConfigurationFailed() {
        when(transferModuleRepository.findById(any())).thenThrow(DataBufferLimitException.class);
        try {
            configurationService.getTransferConfiguration();
        } catch (TMBCommonException ex) {
            assertEquals(ex.getErrorCode(), "0100");
        }
    }

}

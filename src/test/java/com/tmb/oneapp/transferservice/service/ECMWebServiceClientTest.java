package com.tmb.oneapp.transferservice.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@ExtendWith(MockitoExtension.class)
class ECMWebServiceClientTest {
    @InjectMocks
    private ECMWebServiceClient ecmWebServiceClient;

    private String wsdlUrl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        wsdlUrl = "https://www.a.com";
    }

    @Test
    void Should_searchGetData_Success() {
        assertDoesNotThrow(() -> ecmWebServiceClient.searchGetData("", "", "", wsdlUrl));
    }

    @Test
    void Should_getOutputBytes_Success() {
        assertDoesNotThrow(() -> ecmWebServiceClient.getOutputBytes("", "", wsdlUrl, ""));
    }
}
package com.tmb.oneapp.transferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.PaymentAccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FOREIGNER_CUSTOMER_TYPE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TOP_UP_PROCESS_TYPE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_PROCESS_TYPE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class FaceRecognizeServiceTest {
    @InjectMocks
    FaceRecognizeService faceRecognizeService;
    @Mock
    private CustomerService customerService;
    String crmId;
    String correlationId;
    V1CrmProfile crmProfile = new V1CrmProfile();
    String processType;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(faceRecognizeService, "frFinancialAccuAmount", 200000);
        ReflectionTestUtils.setField(faceRecognizeService, "requireAppVersion", "4.3.0");
        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 50000);
        ReflectionTestUtils.setField(faceRecognizeService, "frTopUpFlag", true);
        ReflectionTestUtils.setField(faceRecognizeService, "frTransferFlag", true);

        crmId = "001100000000000000000012004012";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";

        crmProfile = new V1CrmProfile();
        crmProfile.setPaymentAccuUsgAmt(BigDecimal.ZERO);


        processType = "transfer";
    }

    private void mockFetchCrmProfile() throws TMBCommonException {
        when(customerService.getCrmProfile(anyString(), anyString())).thenReturn(crmProfile);
    }

    private void mockWhitelistAndForeignerSkip() throws TMBCommonException{
        FRWhitelistResult fr = new FRWhitelistResult();
        fr.setIsInFrWhitelist(false);

        CustomerKYCResponse kycResponse = new CustomerKYCResponse();
        kycResponse.setCustomerType("000");
        when(customerService.isFrWhitelistByCrmId(anyString(),anyString())).thenReturn(fr);
        when(customerService.getCustomerKyc(anyString(),anyString())).thenReturn(kycResponse);
    }

    private void mockWhitelistAndForeigner() throws TMBCommonException{
        FRWhitelistResult fr = new FRWhitelistResult();
        fr.setIsInFrWhitelist(false);

        CustomerKYCResponse kycResponse = new CustomerKYCResponse();
        kycResponse.setCustomerType(FOREIGNER_CUSTOMER_TYPE);
        when(customerService.isFrWhitelistByCrmId(anyString(),anyString())).thenReturn(fr);
        when(customerService.getCustomerKyc(anyString(),anyString())).thenReturn(kycResponse);
    }

    @Test
    void isRequireFaceRecognizeWhenProcessTypeTransferAndFrFlagTransferFalseShouldRequireFRFalseTest() throws TMBCommonException {
        processType = TRANSFER_PROCESS_TYPE;

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferFlag", false);

        mockFetchCrmProfile();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, new BigDecimal("100"),"5.0.0");
        Assertions.assertFalse(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());
    }

    @Test
    void isRequireFaceRecognizeWhenProcessTypeTopUpAndFrFlagTopUpFalseShouldRequireFRFalseTest() throws TMBCommonException {
        processType = TOP_UP_PROCESS_TYPE;
        ReflectionTestUtils.setField(faceRecognizeService, "frTopUpFlag", false);

        mockFetchCrmProfile();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, new BigDecimal("100"),"5.0.0");
        Assertions.assertFalse(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());
    }

    @Test
    void isRequireFaceRecognizeWhenAmountMoreThanFrTransferTransactionLimitShouldRequireFRFalseTest() throws TMBCommonException {
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        Assertions.assertTrue(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());
    }

    @Test
    void isRequireFaceRecognizeWhenForeignerCheck() throws TMBCommonException {
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();
        mockWhitelistAndForeigner();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        Assertions.assertFalse(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());
    }

    @Test
    void isRequireFaceRecognizeWhenAmountLowerThanTransactionLimitShouldRequireFRFalseTest() throws TMBCommonException {
        BigDecimal amount = new BigDecimal("300");

        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        Assertions.assertFalse(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());
    }



    @Test
    void isRequireFaceRecognizeWhenPaymentAccumulateUsageAmtMoreThanLimitShouldRequireFRTrueTest() throws TMBCommonException {
        BigDecimal amount = new BigDecimal("10000");

        ReflectionTestUtils.setField(faceRecognizeService, "frFinancialAccuAmount", 200000);

        crmProfile.setPaymentAccuUsgAmt(BigDecimal.valueOf(195000));
        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        Assertions.assertTrue(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(195000), actual.getPaymentAccuUsgAmt());
    }

    @Test
    void validateFaceRecognize_paymentAccuUsgAmt_empty_success() throws TMBCommonException {
        BigDecimal amount = new BigDecimal("10000");

        crmProfile.setPaymentAccuUsgAmt(null);
        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        Assertions.assertFalse(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());
    }

    @Test
    void updatePaymentAccumulateUsageAmountWhenSumAccumulateLowerThanFrFinancialAccumulateAmountShouldPaymentAccuUsgAmtIncreaseValueTest() throws TMBCommonException {
        boolean isRequireFr = true;
        BigDecimal paymentAccuUsgAmt = new BigDecimal(100000);
        BigDecimal amount = new BigDecimal(50);

        Assertions.assertDoesNotThrow(() -> faceRecognizeService.updatePaymentAccumulateUsageAmount(correlationId, crmId, amount, isRequireFr, paymentAccuUsgAmt));

        ArgumentCaptor<PaymentAccumulateUsageRequest> paymentAccumulateUsageRequestArgumentCaptor = ArgumentCaptor.forClass(PaymentAccumulateUsageRequest.class);
        Mockito.verify(customerService, Mockito.times(1))
                .updatePaymentAccumulateUsageAmount(eq(correlationId), eq(crmId), paymentAccumulateUsageRequestArgumentCaptor.capture());
        Assertions.assertEquals(BigDecimal.valueOf(100050), paymentAccumulateUsageRequestArgumentCaptor.getValue().getPaymentAccuUsgAmt());
    }

    @Test
    void updatePaymentAccumulateUsageAmountWhenSumAccumulateMoreThanFrFinancialAccumulateAmountShouldPaymentAccuUsgAmtZeroTest() throws TMBCommonException {
        boolean isRequireFr = true;
        BigDecimal paymentAccuUsgAmt = new BigDecimal(100000);
        BigDecimal amountMoreThanAccuUsg = new BigDecimal(2000000);

        Assertions.assertDoesNotThrow(() -> faceRecognizeService.updatePaymentAccumulateUsageAmount(correlationId, crmId, amountMoreThanAccuUsg, isRequireFr, paymentAccuUsgAmt));

        ArgumentCaptor<PaymentAccumulateUsageRequest> paymentAccumulateUsageRequestArgumentCaptor = ArgumentCaptor.forClass(PaymentAccumulateUsageRequest.class);
        Mockito.verify(customerService, Mockito.times(1))
                .updatePaymentAccumulateUsageAmount(eq(correlationId), eq(crmId), paymentAccumulateUsageRequestArgumentCaptor.capture());
        Assertions.assertEquals(BigDecimal.valueOf(0), paymentAccumulateUsageRequestArgumentCaptor.getValue().getPaymentAccuUsgAmt());
    }

    @Test
    void updatePaymentAccumulateUsageAmountWhenSumAccumulateMoreThanFrFinancialAccumulateAmountAndIsRequireFrFalseShouldPaymentAccuUsgAmtIncreaseValueTest() throws TMBCommonException {
        boolean isRequireFr = false;
        BigDecimal paymentAccuUsgAmt = new BigDecimal(100000);
        BigDecimal amountMoreThanAccuUsg = new BigDecimal(2000000);

        Assertions.assertDoesNotThrow(() -> faceRecognizeService.updatePaymentAccumulateUsageAmount(correlationId, crmId, amountMoreThanAccuUsg, isRequireFr, paymentAccuUsgAmt));

        ArgumentCaptor<PaymentAccumulateUsageRequest> paymentAccumulateUsageRequestArgumentCaptor = ArgumentCaptor.forClass(PaymentAccumulateUsageRequest.class);
        Mockito.verify(customerService, Mockito.times(1))
                .updatePaymentAccumulateUsageAmount(eq(correlationId), eq(crmId), paymentAccumulateUsageRequestArgumentCaptor.capture());
        Assertions.assertEquals(BigDecimal.valueOf(2100000), paymentAccumulateUsageRequestArgumentCaptor.getValue().getPaymentAccuUsgAmt());
    }

    @Test
    void updatePaymentAccumulateUsageAmountWhenExceptionShouldDoesNotThrowsTest() throws TMBCommonException {
        when(customerService.updatePaymentAccumulateUsageAmount(anyString(), anyString(), any())).thenThrow(FeignException.class);

        Assertions.assertDoesNotThrow(() -> faceRecognizeService.updatePaymentAccumulateUsageAmount(correlationId, crmId, new BigDecimal(2000000), true, new BigDecimal(100000)));
    }

    @Test
    void testWhitelistFeignException() throws TMBCommonException{
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();

        when(customerService.isFrWhitelistByCrmId(anyString(),anyString())).thenThrow(TMBCommonException.class);

        CustomerKYCResponse kycResponse = new CustomerKYCResponse();
        kycResponse.setCustomerType("000");
        when(customerService.getCustomerKyc(anyString(),anyString())).thenReturn(kycResponse);

        FaceRecognizeResponse actual = faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        Assertions.assertTrue(actual.getIsRequireFr());
        Assertions.assertEquals(BigDecimal.valueOf(0), actual.getPaymentAccuUsgAmt());

    }


    @Test
    void testForeignerNullValue() throws TMBCommonException{
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();
        mockWhitelistAndForeigner();
        CustomerKYCResponse kycResponse = new CustomerKYCResponse();
        when(customerService.getCustomerKyc(anyString(),anyString())).thenReturn(kycResponse);


        Assertions.assertThrows(TMBCommonException.class, () -> {
            faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"5.0.0");
        });

    }

    @Test
    void testAppVersionNull() throws TMBCommonException{
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();


        Assertions.assertThrows(TMBCommonException.class, () -> {
            faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,null);
        });

    }
    @Test
    void testAppVersionWrongFormat() throws TMBCommonException{
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();


        Assertions.assertThrows(TMBCommonException.class, () -> {
            faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"WRONGFORMAT");
        });

    }

    @Test
    void testAppVersionOld() throws TMBCommonException{
        BigDecimal amount = new BigDecimal("60000");

        ReflectionTestUtils.setField(faceRecognizeService, "frTransferTransLimit", 30000);

        mockFetchCrmProfile();
        mockWhitelistAndForeignerSkip();


        Assertions.assertThrows(TMBCommonException.class, () -> {
            faceRecognizeService.validateFaceRecognize(correlationId, crmId, processType, amount,"1.0.0");
        });

    }
}
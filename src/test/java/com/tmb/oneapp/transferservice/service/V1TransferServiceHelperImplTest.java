package com.tmb.oneapp.transferservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mongodb.MongoException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.formatter.Formatter;
import com.tmb.common.model.CommonData;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLogMapper;
import com.tmb.oneapp.transferservice.feature.activitylog.service.V1ActivityLogService;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthForceFR;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import com.tmb.oneapp.transferservice.feature.bank.service.BankService;
import com.tmb.oneapp.transferservice.feature.cache.service.CacheService;
import com.tmb.oneapp.transferservice.feature.common.service.CommonService;
import com.tmb.oneapp.transferservice.feature.customer.client.CustomerExpServiceClient;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.customerstransaction.service.CustomersTransactionService;
import com.tmb.oneapp.transferservice.feature.notification.service.V1TransferNotificationService;
import com.tmb.oneapp.transferservice.model.AccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.transferservice.model.CommonAuthenResult;
import com.tmb.oneapp.transferservice.model.CustomSlip;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.bank.BankInfoDataModel;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.PinFreeCountData;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.PaymentCacheData;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferAccount;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCEPT_LANGUAGE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.APP_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.COMMON_MODULE_CONSTANT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.EIGHT_INT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HOME_FAVORITE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HOME_TRANSFER_LANDING;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V1TransferServiceHelperImplTest {

    @Mock
    V1ActivityLogService v1ActivityLogService;
    @Mock
    private CacheService cacheService;
    @Mock
    private MongoTemplate mongoTemplate;
    @Spy
    @InjectMocks
    private V1TransferServiceHelperImpl transferServiceHelper;
    @Mock
    private BankService bankService;

    @Mock
    private CustomerService customerService;

    @Mock
    private V1TransferNotificationService transferNotificationService;

    @Mock
    CustomerExpServiceClient customerExpServiceClient;
    @Mock
    CustomersTransactionService customersTransactionService;
    @Mock
    private CommonService commonService;
    @Mock
    private FaceRecognizeService faceRecognizeService;
    @Mock
    private OauthService oauthService;

    Formatter formatter;

    @BeforeEach
    void init() {
        formatter = new Formatter();
        ReflectionTestUtils.setField(formatter, "mobileNoRegex", "(.{3})(.{3})(.{4})$");
        ReflectionTestUtils.setField(formatter, "mobileNoValue", "$1-$2-$3");
        ReflectionTestUtils.setField(formatter, "citizenIdRegex", "(.{1})(.{4})(.{5})(.{2})(.{1})$");
        ReflectionTestUtils.setField(formatter, "citizenIdValue", "$1-$2-$3-$4-$5");
        ReflectionTestUtils.setField(transferServiceHelper, "formatter", formatter);
    }

    @Test
    void getTransferDraftData_ShouldSupportDifferenceTypeOfCaching() throws TMBCommonException {
        String transId = "123";
        V1TransferData mockTransferData = new V1TransferData();
        mockTransferData.setMemo("Memo1");

        Mockito.doThrow(new IllegalArgumentException()).when(cacheService).get(transId, V1TransferData.class);
        Mockito.doThrow(new IllegalArgumentException()).when(cacheService).get(transId, TPromptPayVerifyETEResponse.class);
        when(mongoTemplate.findOne(any(), eq(V1TransferData.class), anyString())).thenReturn(mockTransferData);
        TPromptPayVerifyETEResponse mockPrompay = new TPromptPayVerifyETEResponse();
        mockPrompay.setReference1("prompayRef1");
        when(mongoTemplate.findOne(any(), eq(TPromptPayVerifyETEResponse.class), anyString())).thenReturn(mockPrompay);

        V1TransferData actualTransferData = (V1TransferData) transferServiceHelper.getTransferDraftData(transId, V1TransferData.class);
        TPromptPayVerifyETEResponse actualPromPayData = (TPromptPayVerifyETEResponse) transferServiceHelper.getTransferDraftData(transId, TPromptPayVerifyETEResponse.class);

        Assertions.assertNotNull(actualTransferData);
        Assertions.assertNotNull(actualPromPayData);
        Assertions.assertEquals(mockTransferData.getMemo(), actualTransferData.getMemo());
        Assertions.assertEquals(mockPrompay.getReference1(), actualPromPayData.getReference1());

    }

    @Test
    void getTransferDraftData_ShouldThrowTmbExceptionIfThereIsNoData() {
        String transId = "123";
        Mockito.doThrow(new IllegalArgumentException()).when(cacheService).get(transId, V1TransferData.class);
        when(mongoTemplate.findOne(any(), eq(V1TransferData.class), anyString())).thenReturn(null);

        assertThrows(TMBCommonException.class, () -> {
            transferServiceHelper.getTransferDraftData(transId, V1TransferData.class);
        });
    }

    @Test
    void getTransferDraftData_WithCache() throws TMBCommonException {
        String transId = "123";
        V1TransferData expectedDraft = V1TransferData.builder().amount(new BigDecimal(50)).transId(transId).build();
        when((cacheService).get(transId, V1TransferData.class)).thenReturn(expectedDraft);
        V1TransferData transferData = (V1TransferData) transferServiceHelper.getTransferDraftData(transId, V1TransferData.class);
        Assertions.assertEquals(expectedDraft.getAmount(), transferData.getAmount());
        Assertions.assertEquals(expectedDraft.getTransId(), transferData.getTransId());
    }

    @Test
    void validateDuplicate_WhenIsTransactionUsedFalse_ShouldDoesNotThrows() {
        V1TransferData cache = new V1TransferData();
        cache.setTransactionUsed(false);

        assertDoesNotThrow(() -> transferServiceHelper.validateDuplicateTransaction("transId", cache));

    }

    @Test
    void validateDuplicate_WhenIsTransactionUsedTrue_ShouldThrowsException() {
        V1TransferData cache = new V1TransferData();
        cache.setTransactionUsed(true);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> transferServiceHelper.validateDuplicateTransaction("transId", cache));

        Assertions.assertEquals(ResponseCode.DUPLICATE_TRANSACTION.getCode(), exception.getErrorCode());
    }

    @Test
    void checkTransactionLimited_ShouldReturnTrueIfRemainingIsMoreThanLimitedAmount() throws TMBCommonException {
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(100.0);
        crmProfile.setEbMaxLimitAmtCurrent(500.0);
        double remaining = 1000;

        boolean actualResult = transferServiceHelper.checkTransactionLimited(crmProfile, remaining);

        assertTrue(actualResult);
    }

    @Test
    void checkTransactionLimited_ShouldReturnFalseIfRemainingIsMoreThanLimitedAmount() throws TMBCommonException {
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(100.0);
        crmProfile.setEbMaxLimitAmtCurrent(500.0);
        double remaining = 50;

        boolean actualResult = transferServiceHelper.checkTransactionLimited(crmProfile, remaining);

        Assertions.assertFalse(actualResult);
    }

    @Test
    void shouldBeValidFormatTransactionRefId() {
        int lenExpected = "202303211214284648".length();
        Assertions.assertEquals(lenExpected, transferServiceHelper.generateTransactionRef(TRANSFER_REFERENCE_NUMBER_PREFIX, EIGHT_INT).length());
    }

    @Test
    void shouldBeValidFormatTerminalId() {
        int lenExpected = "I390781B011BTMBO".length();

        String actual = transferServiceHelper.generateTerminalId();
        Assertions.assertEquals(lenExpected, actual.length());
    }

    @Test
    void shouldBeValidFormatTransId() {
        int lenExpected = "TRANSFER_1234_1eef2d12-7a95-43ef-a589-0df3d75d9e56".length();
        String crmId = "1234";

        String actual = transferServiceHelper.generateTransId(crmId);

        Assertions.assertEquals(lenExpected, actual.length());
    }

    @Test
    void shouldBeSaveDraftToPrimary() throws JsonProcessingException {
        String transId = "1235";
        TPromptPayVerifyETEResponse data = TPromptPayVerifyETEResponse.builder().amount(new BigDecimal(50)).build();
        int ttl = 300;

        Mockito.doNothing().when(cacheService).set(transId, data, ttl);
        Assertions.assertDoesNotThrow(() -> {
            transferServiceHelper.saveDataToCache(transId, data);
        });
        Mockito.verify(cacheService, Mockito.times(1)).set(transId, data, ttl);
    }

    @Test
    void shouldBeSaveDraftToSecondary() throws JsonProcessingException {
        String transId = "1235";
        TPromptPayVerifyETEResponse data = TPromptPayVerifyETEResponse.builder().amount(new BigDecimal(50)).build();
        int ttl = 300;
        Mockito.doThrow(new IllegalArgumentException()).when(cacheService).set(transId, data, ttl);
        Assertions.assertDoesNotThrow(() -> {
            transferServiceHelper.saveDataToCache(transId, data);
        });
        Mockito.verify(cacheService, Mockito.times(1)).set(transId, data, ttl);
        Mockito.verify(mongoTemplate, Mockito.times(1)).save(any(), anyString());
    }

    @Test
    void shouldBeSaveDraftError() throws JsonProcessingException {
        String transId = "1235";
        TPromptPayVerifyETEResponse data = TPromptPayVerifyETEResponse.builder().amount(new BigDecimal(50)).paymentCacheData(new PaymentCacheData().setCategoryId("1")).build();

        int ttl = 300;

        Mockito.doThrow(new IllegalArgumentException()).when(cacheService).set(transId, data, ttl);
        Mockito.doThrow(new MongoException("Write fail")).when(mongoTemplate).save(any(), anyString());
        Assertions.assertThrows(TMBCommonException.class, () -> {
            transferServiceHelper.saveDataToCache(transId, data);
        });

        Mockito.verify(cacheService, Mockito.times(1)).set(transId, data, ttl);
        Mockito.verify(mongoTemplate, Mockito.times(1)).save(any(), anyString());
    }

    @Test
    void shouldBeGetBankInfo() throws TMBCommonException {
        String correlationId = "12355";
        String expected = "SCB";
        List<BankInfoDataModel> banks = new ArrayList<>();
        banks.add(BankInfoDataModel.builder().bankCd("14").bankShortname(expected).build());

        when(bankService.getBanks(correlationId)).thenReturn(banks);
        String actualShortName = transferServiceHelper.getBankShortName(correlationId, "14");
        Assertions.assertEquals(expected, actualShortName);
    }

    @Test
    void shouldBeEmptyBankInfo() throws TMBCommonException {
        String correlationId = "12355";
        String expected = "";

        Mockito.doThrow(new RuntimeException("Fail connection")).when(bankService).getBanks(correlationId);
        String actualShortName = transferServiceHelper.getBankShortName(correlationId, "14");
        Assertions.assertEquals(expected, actualShortName);
    }

    @Test
    void shouldBeEmptyBankInfoWithEmptyBankCode() throws TMBCommonException {
        String correlationId = "12355";
        String expected = "SCB";
        List<BankInfoDataModel> banks = new ArrayList<>();
        banks.add(BankInfoDataModel.builder().bankCd("14").bankShortname(expected).build());

        when(bankService.getBanks(correlationId)).thenReturn(banks);
        String actualShortName = transferServiceHelper.getBankShortName(correlationId, "");
        Assertions.assertNull(actualShortName);
    }

    @Test
    void sendNotification_NormalFlowShouldCallNotificationService() throws TMBCommonException {
        CategoryInfoDataModel c1 = new CategoryInfoDataModel();
        c1.setCategoryCd("1");
        List<CategoryInfoDataModel> categories = Arrays.asList(c1);
        when(bankService.getCategories(anyString())).thenReturn(categories);

        CustomerKYCResponse customerCyc = new CustomerKYCResponse();
        customerCyc.setEmail("<EMAIL>");
        when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerCyc);
        String correlationId = "abc";
        String crmId = "123";
        String refId = "ref1";

        V1TransferData transferData = new V1TransferData();
        transferData.setCategoryId("1");
        transferData.setFeeFromETE(BigDecimal.valueOf(1.0));
        transferData.setAmount(BigDecimal.valueOf(10));
        transferData.setToAccount(new V1TransferAccount());
        transferData.setFromAccount(new V1TransferAccount());
        String transactionDatetime = Long.toString(System.currentTimeMillis());
        transferServiceHelper.sendNotification(correlationId, crmId, refId, transferData, transactionDatetime);

        Mockito.verify(transferNotificationService).sendTransferNotification(any(), anyString());
    }

    @Test
    void deleteTransferDraftData_RedisUpShouldCallCacheService() throws TMBCommonException {
        transferServiceHelper.deleteTransferDraftData("tt1");
        Mockito.verify(cacheService).delete(anyString());
    }

    @Test
    void deleteTransferDraftData_RedisDownShouldCallMongoTemplate() throws TMBCommonException {
        when(cacheService.delete(anyString())).thenThrow(new RuntimeException());
        transferServiceHelper.deleteTransferDraftData("tt1");
        Mockito.verify(mongoTemplate).remove(any(), anyString());
    }

    @Test
    void validateIsRequireConfirmPin_NotOwnAccountPinFreeTxnCountMoreThanPinFeeTrLimitShouldReturnTrue() {
        String reqAmount = "500";
        boolean isTransferToOwnAcct = false;
        boolean isPreLogin = false;
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(5);
        crmProfile.setPinFreeTrLimit(1);
        crmProfile.setPinFreeSeetingFlag("N");
        CommonData commonConfig = new CommonData();
        commonConfig.setPinFreeMaxTrans("5");
        boolean actualResult = transferServiceHelper.validateIsRequireConfirmPin(reqAmount, isTransferToOwnAcct, isPreLogin, crmProfile, commonConfig);
        assertTrue(actualResult);
    }

    @Test
    void deleteAccountCacheShouldSuccess() {
        transferServiceHelper.deleteAccountCache(new HttpHeaders(), "correlationId", "crmId");
        Mockito.verify(customersTransactionService, Mockito.times(1)).clearDepositCache(anyString(), anyString());
    }

    @Test
    void deleteAccountCacheCustomHeaderShouldSuccess() {
        HttpHeaders headers = new HttpHeaders();
        headers.set(ACCEPT_LANGUAGE, "Th");
        headers.set(APP_VERSION, "5.0.0");
        transferServiceHelper.deleteAccountCache(headers, "correlationId", "crmId");
        Mockito.verify(customersTransactionService, Mockito.times(1)).clearDepositCache(anyString(), anyString());
    }

    @Test
    void deleteAccountCacheShouldSuccessFeignClientOnCustomerExpServiceClientDeleteDepositCache() {
        Mockito.doThrow(FeignException.class).when(customersTransactionService).clearDepositCache(anyString(), anyString());
        transferServiceHelper.deleteAccountCache(new HttpHeaders(), "correlationId", "crmId");
        Mockito.verify(customersTransactionService, Mockito.times(1)).clearDepositCache(anyString(), anyString());
    }

    @Test
    void deleteAccountCacheShouldSuccessFeignClientOnCustomerExpServiceClientDeleteLoanCache() {
        transferServiceHelper.deleteAccountCache(new HttpHeaders(), "correlationId", "crmId");
        Mockito.verify(customersTransactionService, Mockito.times(1)).clearDepositCache(anyString(), anyString());
    }

    @Test
    void updateDailyUsage_success() throws TMBCommonException {
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.0);
        crmProfile.setPinFreeTrLimit(1);
        transferServiceHelper.updateDailyUsage("", "", 10.00, crmProfile, false);
        Mockito.verify(customerService, Mockito.times(1)).updatePinFreeCount(anyString(), anyString(), any());
        Mockito.verify(customerService, Mockito.times(1)).updateDailyUsage(anyString(), anyString(), any());
    }

    @Test
    void updateDailyUsage_updatePinFreeCount_failed() throws TMBCommonException {
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.0);
        crmProfile.setPinFreeTrLimit(1);
        Mockito.doThrow(new RuntimeException("Fail connection")).when(customerService).updatePinFreeCount(anyString(), anyString(), any());

        assertDoesNotThrow(() -> {
            transferServiceHelper.updateDailyUsage("", "", 10.00, crmProfile, false);
        });
    }

    @Test
    void updateDailyUsage_updateDailyUsage_failed() throws TMBCommonException {
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1.0);
        crmProfile.setPinFreeTrLimit(1);
        Mockito.doThrow(new RuntimeException("Fail connection")).when(customerService).updateDailyUsage(anyString(), anyString(), any());
        transferServiceHelper.updateDailyUsage("", "", 10.00, crmProfile, false);
        Mockito.verify(customerService, Mockito.times(1)).updatePinFreeCount(anyString(), anyString(), any());
    }

    static Stream<Arguments> scenarioTransformRequest() {
        return Stream.of(
                Arguments.of("flow", "toFav", "flow"),
                Arguments.of("flow", "", "flow"),
                Arguments.of("", "toFav", HOME_FAVORITE),
                Arguments.of("", "", HOME_TRANSFER_LANDING)
        );
    }

    @ParameterizedTest
    @MethodSource("scenarioTransformRequest")
    void transformRequestShouldSuccess(String flow, String toFavorite, String expected) {
        V1OnUsValidateRequest v1OnUsValidateRequest = new V1OnUsValidateRequest();
        v1OnUsValidateRequest.setFlow(flow);
        v1OnUsValidateRequest.setToFavoriteName(toFavorite);

        TransferOtherBankValidateRequest transferOtherBankValidateRequest = new TransferOtherBankValidateRequest();
        transferOtherBankValidateRequest.setFlow(flow);
        transferOtherBankValidateRequest.setToFavoriteName(toFavorite);

        transferServiceHelper.transformRequest(v1OnUsValidateRequest);
        transferServiceHelper.transformRequest(transferOtherBankValidateRequest);

        Assertions.assertEquals(expected, v1OnUsValidateRequest.getFlow());
        Assertions.assertEquals(expected, transferOtherBankValidateRequest.getFlow());
    }

    @Test
    void validateCommonServiceWhenUuidExistedShouldSuccessWithOutException() throws TMBCommonException {
        when(customerService.isCommonFRExistedByUUID(anyString(), anyString(), any(), anyString())).thenReturn(true);
        Assertions.assertDoesNotThrow(() -> transferServiceHelper.validateCommonFR("frUUid0111-1123", "core-id", "crmId", "127.0.0.1", "Home-lander"));
    }

    @Test
    void validateCommonServiceWhenUuidNotExistedShouldFailedWithException() throws TMBCommonException {
        when(customerService.isCommonFRExistedByUUID(anyString(), anyString(), any(), anyString())).thenReturn(false);
        Assertions.assertThrows(TMBCommonException.class, () -> transferServiceHelper.validateCommonFR("frUUid0111-1123", "core-id", "crmId", "127.0.0.1", "Home-lander"));
    }

    @Test
    void formatMobileOrCitizenWhenInputMobileShouldReturnFormatMobileTest() throws TMBCommonException {
        String mobile = "**********";

        String actual = transferServiceHelper.formatMobileOrCitizen(mobile);

        String expected = "************";
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void formatMobileOrCitizenWhenInputCitizenShouldReturnFormatCitizenTest() throws TMBCommonException {
        String citizen = "1950600098724";

        String actual = transferServiceHelper.formatMobileOrCitizen(citizen);

        String expected = "1-9506-00098-72-4";
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void shouldPublishActivityTransaction() {
        Assertions.assertDoesNotThrow(() -> transferServiceHelper.publishActivityTransaction(TransferActivityLogMapper.INSTANCE.toTransferActivityLog(new HttpHeaders(), new BigDecimal("10"), new V1OnUsValidateRequest(), true, new VerifyTransactionResult(false, new FaceRecognizeResponse(), new CommonAuthenResult()))));
    }

    @Test
    void shouldPublishActivityCustomSlip() {
        Assertions.assertDoesNotThrow(() -> transferServiceHelper.publishActivityCustomSlip(new CustomSlipCompleteActivityLog(new HttpHeaders(), new CustomSlip())));

    }

    @Test
    void updatePinFreeCountShouldSuccess() throws TMBCommonException {
        String correlationId = "correlationId";
        String crmId = "crmId";
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(10);

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.updatePinFreeCount(correlationId, crmId, crmProfile));

        ArgumentCaptor<PinFreeCountData> captor = ArgumentCaptor.forClass(PinFreeCountData.class);
        verify(customerService, times(1)).updatePinFreeCount(eq(correlationId), eq(crmId), captor.capture());

        int expectedCount = crmProfile.getPinFreeTxnCount() + 1;
        Assertions.assertEquals(expectedCount, captor.getValue().getPinFreeCount());
    }

    @Test
    void updatePinFreeCountWhenFailedFeignExceptionShouldDoesNotThrows() throws TMBCommonException {
        String correlationId = "correlationId";
        String crmId = "crmId";
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(10);

        doThrow(FeignException.class).when(customerService).updatePinFreeCount(eq(correlationId), eq(crmId), any());

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.updatePinFreeCount(correlationId, crmId, crmProfile));
    }

    @Test
    void updateUsageAccumulationShouldSuccess() throws TMBCommonException {
        String correlationId = "correlationId";
        String crmId = "crmId";
        BigDecimal amount = new BigDecimal("500.00");
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1000.00);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("1000.00"));
        boolean isRequireCommonAuthen = false;

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.updateUsageAccumulation(correlationId, crmId, amount, crmProfile, isRequireCommonAuthen));

        ArgumentCaptor<AccumulateUsageRequest> captor = ArgumentCaptor.forClass(AccumulateUsageRequest.class);
        verify(customerService, times(1)).updateUsageAccumulation(eq(correlationId), eq(crmId), captor.capture());

        BigDecimal expectedDailyAccumulate = BigDecimal.valueOf(crmProfile.getEbAccuUsgAmtDaily()).add(amount);
        BigDecimal expectedPaymentAccumulate = crmProfile.getPaymentAccuUsgAmt().add(amount);
        Assertions.assertEquals(expectedDailyAccumulate, captor.getValue().getDailyAccumulateUsageAmount());
        Assertions.assertEquals(expectedPaymentAccumulate, captor.getValue().getPaymentAccumulateUsageAmount());
    }

    @Test
    void updateUsageAccumulationWhenRequireCommonAuthenAndPaymentAccumulateUsageExceedLimitShouldSetZeroSuccess() throws TMBCommonException {
        int limit = 200000;
        ReflectionTestUtils.setField(transferServiceHelper, "totalPaymentAccumulateUsageLimit", limit);
        String correlationId = "correlationId";
        String crmId = "crmId";
        BigDecimal amount = new BigDecimal("500.00");
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1000.00);
        crmProfile.setPaymentAccuUsgAmt(BigDecimal.valueOf(limit));
        boolean isRequireCommonAuthen = true;

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.updateUsageAccumulation(correlationId, crmId, amount, crmProfile, isRequireCommonAuthen));

        ArgumentCaptor<AccumulateUsageRequest> captor = ArgumentCaptor.forClass(AccumulateUsageRequest.class);
        verify(customerService, times(1)).updateUsageAccumulation(eq(correlationId), eq(crmId), captor.capture());

        BigDecimal expectedDailyAccumulate = BigDecimal.valueOf(crmProfile.getEbAccuUsgAmtDaily()).add(amount);
        Assertions.assertEquals(expectedDailyAccumulate, captor.getValue().getDailyAccumulateUsageAmount());
        Assertions.assertEquals(BigDecimal.ZERO, captor.getValue().getPaymentAccumulateUsageAmount());
    }

    @Test
    void updateUsageAccumulationWhenFailedFeignExceptionShouldDoesNotThrows() throws TMBCommonException {
        String correlationId = "correlationId";
        String crmId = "crmId";
        BigDecimal amount = new BigDecimal("500.00");
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setEbAccuUsgAmtDaily(1000.00);
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("500.00"));
        boolean isRequireCommonAuthen = false;

        when(customerService.updateUsageAccumulation(eq(correlationId), eq(crmId), any())).thenThrow(FeignException.class);

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.updateUsageAccumulation(correlationId, crmId, amount, crmProfile, isRequireCommonAuthen));
    }

    @Test
    void validateIsRequireVerifyTransaction_WhenVersionBelowThanCommonAuthenRequireAppVersion_ShouldValidatePinAndFRSuccess() throws TMBCommonException {
        String requireVersion = "5.12.0";
        String appVersionBelowThanRequire = "5.10.0";
        ReflectionTestUtils.setField(transferServiceHelper, "commonAuthenRequireAppVersion", requireVersion);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, appVersionBelowThanRequire);
        BigDecimal reqAmount = new BigDecimal("500.00");
        boolean isTransferToOwnAcct = false;
        V1CrmProfile crmProfile = new V1CrmProfile();

        when(commonService.getCommonConfiguration(anyString(), eq(COMMON_MODULE_CONSTANT))).thenReturn(List.of(new CommonData()));
        doReturn(true).when(transferServiceHelper).validateIsRequireConfirmPin(anyString(), anyBoolean(), anyBoolean(), any(), any());

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.validateIsRequireVerifyTransaction(headers, reqAmount, isTransferToOwnAcct, crmProfile));

        verify(transferServiceHelper, times(1)).validateIsRequireConfirmPin(anyString(), anyBoolean(), anyBoolean(), any(), any());
        verify(faceRecognizeService, times(1)).validateFaceRecognize(anyString(), anyString(), anyString(), any(), anyString());
    }

    @Test
    void validateIsRequireVerifyTransaction_WhenVersionMoreThanCommonAuthenRequireAppVersion_ShouldValidateWithCommonAuthenSuccess() throws TMBCommonException {
        String requireVersion = "5.12.0";
        String appVersionBelowThanRequire = "5.12.0";
        ReflectionTestUtils.setField(transferServiceHelper, "commonAuthenRequireAppVersion", requireVersion);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, appVersionBelowThanRequire);
        BigDecimal reqAmount = new BigDecimal("500.00");
        boolean isTransferToOwnAcct = false;
        V1CrmProfile crmProfile = new V1CrmProfile();

        doReturn(new CommonAuthenResult()).when(transferServiceHelper).validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any());

        Assertions.assertDoesNotThrow(() -> transferServiceHelper.validateIsRequireVerifyTransaction(headers, reqAmount, isTransferToOwnAcct, crmProfile));

        verify(transferServiceHelper, times(1)).validateIsRequireCommonAuthen(any(), anyString(), anyBoolean(), any());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenPreLogin_ShouldRequireCommonAuthen() throws TMBCommonException {
        String preLogin = "true";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, "5.12.0");
        headers.add("pre-login", preLogin);

        String amount = "500.00";
        boolean isTransferToOwnAccount = false;
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(10);
        crmProfile.setPinFreeTrLimit(20);
        crmProfile.setPinFreeSeetingFlag("N");

        CommonAuthenResult actual = transferServiceHelper.validateIsRequireCommonAuthen(headers, amount, isTransferToOwnAccount, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
        Assertions.assertFalse(actual.isPinFree());
        Assertions.assertNull(actual.getIsForceFr());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenIsOwn_ShouldNotRequireCommonAuthen() throws TMBCommonException {
        boolean isTransferToOwnAccount = true;
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, "5.12.0");
        String amount = "500.00";
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(10);
        crmProfile.setPinFreeTrLimit(20);
        crmProfile.setPinFreeSeetingFlag("N");

        CommonAuthenResult actual = transferServiceHelper.validateIsRequireCommonAuthen(headers, amount, isTransferToOwnAccount, crmProfile);

        Assertions.assertFalse(actual.isRequireCommonAuthen());
        Assertions.assertFalse(actual.isPinFree());
        Assertions.assertNull(actual.getIsForceFr());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenDisablePinFreeSettingAndAmountMoreThanLimit_ShouldRequireCommonAuthen() throws TMBCommonException {
        String disablePinFreeSetting = "N";
        Integer limit = 400;
        String amount = "500.00";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, "5.12.0");
        boolean isTransferToOwnAccount = false;
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(10);
        crmProfile.setPinFreeTrLimit(limit);
        crmProfile.setPinFreeSeetingFlag(disablePinFreeSetting);

        CommonAuthenResult actual = transferServiceHelper.validateIsRequireCommonAuthen(headers, amount, isTransferToOwnAccount, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
        Assertions.assertFalse(actual.isPinFree());
        Assertions.assertNull(actual.getIsForceFr());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenIsNotPinFree_ShouldRequireCommonAuthen() throws TMBCommonException {
        int TxnCount = 5;
        String maxCount = "5";

        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, "5.12.0");
        String amount = "500.00";
        boolean isTransferToOwnAccount = false;
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(TxnCount);
        crmProfile.setPinFreeTrLimit(50000);
        crmProfile.setPinFreeSeetingFlag("Y");

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans(maxCount);
        when(commonService.getCommonConfiguration(anyString(), eq(COMMON_MODULE_CONSTANT))).thenReturn(List.of(commonData));

        CommonAuthenResult actual = transferServiceHelper.validateIsRequireCommonAuthen(headers, amount, isTransferToOwnAccount, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
        Assertions.assertFalse(actual.isPinFree());
        Assertions.assertNull(actual.getIsForceFr());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenTotalUsageMoreThanEqualsLimit_ShouldRequireCommonAuthen() throws TMBCommonException {
        String amount = "500.00";
        BigDecimal usage = new BigDecimal("199999.00");
        Integer usageLimit = 200000;
        ReflectionTestUtils.setField(transferServiceHelper, "totalPaymentAccumulateUsageLimit", usageLimit);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, "5.12.0");
        boolean isTransferToOwnAccount = false;
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPaymentAccuUsgAmt(usage);
        crmProfile.setPinFreeTxnCount(10);
        crmProfile.setPinFreeTrLimit(2000);
        crmProfile.setPinFreeSeetingFlag("Y");

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("10000");
        when(commonService.getCommonConfiguration(anyString(), eq(COMMON_MODULE_CONSTANT))).thenReturn(List.of(commonData));

        CommonAuthenResult actual = transferServiceHelper.validateIsRequireCommonAuthen(headers, amount, isTransferToOwnAccount, crmProfile);

        assertTrue(actual.isRequireCommonAuthen());
        assertTrue(actual.isPinFree());
        Assertions.assertFalse(actual.getIsForceFr());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenTotalUsageLowerThanLimit_ShouldNotRequireCommonAuthen() throws TMBCommonException {
        String amount = "500.00";
        BigDecimal usage = new BigDecimal("150000.00");
        Integer usageLimit = 200000;
        ReflectionTestUtils.setField(transferServiceHelper, "totalPaymentAccumulateUsageLimit", usageLimit);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "crm-id");
        headers.add(HEADER_CORRELATION_ID, "correlationId");
        headers.add(APP_VERSION, "5.12.0");
        boolean isTransferToOwnAccount = false;
        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPaymentAccuUsgAmt(usage);
        crmProfile.setPinFreeTxnCount(10);
        crmProfile.setPinFreeTrLimit(2000);
        crmProfile.setPinFreeSeetingFlag("Y");

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("10000");
        when(commonService.getCommonConfiguration(anyString(), eq(COMMON_MODULE_CONSTANT))).thenReturn(List.of(commonData));

        CommonAuthenResult actual = transferServiceHelper.validateIsRequireCommonAuthen(headers, amount, isTransferToOwnAccount, crmProfile);

        Assertions.assertFalse(actual.isRequireCommonAuthen());
        assertTrue(actual.isPinFree());
        Assertions.assertFalse(actual.getIsForceFr());
    }

    @Test
    void validateIsRequireCommonAuthen_WhenValidateCrmIDInDDPIsTrue_ShouldRequireCommonAuthen() throws TMBCommonException {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, "testCrmId");
        headers.add(HEADER_CORRELATION_ID, "testCorrelationId");
        headers.add("pre-login", "false");

        String reqAmount = "1000.00";
        boolean isTransferToOwnAcct = false;

        V1CrmProfile crmProfile = new V1CrmProfile();
        crmProfile.setPinFreeTxnCount(5);
        crmProfile.setPinFreeTrLimit(2000.0);
        crmProfile.setPinFreeSeetingFlag("Y");
        crmProfile.setPaymentAccuUsgAmt(new BigDecimal("5000.00"));

        CommonAuthForceFR commonAuthForceFRResult = new CommonAuthForceFR();
        commonAuthForceFRResult.setIsForce(true);

        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxTrans("10");
        List<CommonData> commonDataList = List.of(commonData);
        when(commonService.getCommonConfiguration(anyString(), eq(COMMON_MODULE_CONSTANT))).thenReturn(commonDataList);

        when(oauthService.getCommonAuthForceFR(eq("testCorrelationId"), eq("testCrmId"))).thenReturn(commonAuthForceFRResult);

        CommonAuthenResult result = transferServiceHelper.validateIsRequireCommonAuthen(headers, reqAmount, isTransferToOwnAcct, crmProfile);

        assertTrue(result.isRequireCommonAuthen());
        assertTrue(result.isPinFree());
        assertTrue(result.getIsForceFr());

        verify(oauthService, times(1)).getCommonAuthForceFR("testCorrelationId", "testCrmId");
        verify(commonService, times(1)).getCommonConfiguration(anyString(), eq(COMMON_MODULE_CONSTANT));
    }
}

package com.tmb.oneapp.transferservice.service;

import com.tmb.oneapp.transferservice.model.ECMDocument;
import com.tmb.oneapp.transferservice.model.ECMDocumentRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.transferservice.utils.EcmServiceUtils.TF_DOCTYPE_EXIMDOC;
import static com.tmb.oneapp.transferservice.utils.EcmServiceUtils.TF_DOCTYPE_SWIFT;
import static com.tmb.oneapp.transferservice.utils.EcmServiceUtils.TF_DOCTYPE_SWIFT_EXIMDOC;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CallECMAppServiceTest {

    @InjectMocks
    private CallECMAppService callECMAppService;
    @Mock
    private ECMWebServiceClient ecmWebServiceClient;

    private ECMDocumentRequest ecmDocumentRequest;
    private List<ECMDocument> ecmDocumentList;
    private ECMDocument ecmDoc;
    private byte[] byteContent;

    @BeforeEach
    void setUp() {
        byteContent = new byte[10];
    }

    @ParameterizedTest
    @ValueSource(strings = {TF_DOCTYPE_EXIMDOC, TF_DOCTYPE_SWIFT, TF_DOCTYPE_SWIFT_EXIMDOC})
    void Should_SuccessToGetAttachFile(String tfDocType) {
        ecmDocumentRequest = new ECMDocumentRequest();
        ecmDocumentRequest.setRefId("NT1700000465647800");
        ecmDocumentRequest.setSwiftOp(true);
        ecmDocumentRequest.setDebitOp(true);
        ecmDocumentRequest.setTxnDateTime("020921-155646");

        ecmDocumentList = new ArrayList<>();
        ecmDoc = new ECMDocument();
        ecmDoc.setIds("idd_D5E2AC00-B13E-465C-A93D-52D877C3DEE7");
        ecmDoc.setDocumentTitle("SWIFT");
        ecmDoc.setAttachmentName("SWIFT_DETAIL_020921-155646.pdf");
        ecmDoc.setContentBase64("[B@6bb31c4c");

        switch (tfDocType) {
            case TF_DOCTYPE_EXIMDOC: {
                ecmDocumentRequest.setDebitOp(true);
                ecmDocumentRequest.setSwiftOp(false);

                ecmDoc.setDocumentTitle(TF_DOCTYPE_EXIMDOC);
                ecmDoc.setAttachmentName("DEBIT_ADVICE_020921-155646.pdf");
                break;
            }
            case TF_DOCTYPE_SWIFT: {
                ecmDocumentRequest.setSwiftOp(true);
                ecmDocumentRequest.setDebitOp(false);

                ecmDoc.setDocumentTitle(TF_DOCTYPE_SWIFT);
                ecmDoc.setAttachmentName("SWIFT_DETAIL_020921-155646.pdf");
                break;
            }
            case TF_DOCTYPE_SWIFT_EXIMDOC: {
                ecmDocumentRequest.setSwiftOp(true);
                ecmDocumentRequest.setDebitOp(true);

                ecmDoc.setDocumentTitle(TF_DOCTYPE_SWIFT_EXIMDOC);
                ecmDoc.setAttachmentName("SWIFT_DETAIL_020921-155646.pdf");
                break;
            }
        }

        ecmDocumentList.add(ecmDoc);

        when(ecmWebServiceClient.searchGetData(any(), any(), any(), any()))
                .thenReturn(ecmDocumentList);
        when(ecmWebServiceClient.getOutputBytes(any(), any(), any(), any()))
                .thenReturn(byteContent);

        List<ECMDocument> response = callECMAppService.getAttachFile(ecmDocumentRequest);

        switch (tfDocType) {
            case TF_DOCTYPE_EXIMDOC: {
                Assertions.assertEquals("DEBIT_ADVICE_020921-155646.pdf", response.get(0).getAttachmentName());
                break;
            }
            case TF_DOCTYPE_SWIFT:
            case TF_DOCTYPE_SWIFT_EXIMDOC: {
                Assertions.assertEquals("SWIFT_DETAIL_020921-155646.pdf", response.get(0).getAttachmentName());
                break;
            }
        }
    }
}
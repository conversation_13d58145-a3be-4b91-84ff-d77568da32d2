package com.tmb.oneapp.transferservice.controller.v1;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.service.ConfigurationService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConfigurationControllerTest {

    @InjectMocks
    private ConfigurationController configurationController;
    @Mock
    private ConfigurationService configurationService;

    @Test
    public void getAccountConfiguration_success() throws Exception {
        when(configurationService.getTransferConfiguration()).thenReturn(new TransferModuleModel());
        ResponseEntity<TmbOneServiceResponse<TransferModuleModel>> response = configurationController.getAccountConfiguration(new HttpHeaders());
        assertEquals("0000", response.getBody().getStatus().getCode());
    }
}

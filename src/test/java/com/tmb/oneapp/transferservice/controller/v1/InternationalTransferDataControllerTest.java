package com.tmb.oneapp.transferservice.controller.v1;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.model.ECMDocument;
import com.tmb.oneapp.transferservice.model.ECMDocumentRequest;
import com.tmb.oneapp.transferservice.model.InternationalTransferPurpose;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.model.InterestRateResponse;
import com.tmb.oneapp.transferservice.service.CacheService;
import com.tmb.common.model.internationaltransfer.OTTCountry;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.service.CallECMAppService;
import com.tmb.oneapp.transferservice.service.InternationalTransferDataService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.io.IOException;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InternationalTransferDataControllerTest {
    @Mock
    InternationalTransferDataService internationalTransferDataService;
    @Mock
    CallECMAppService callECMAppService;

    @InjectMocks
    InternationalTransferDataController internationalTransferDataController;

    @Test
    void testFetchPurposeMasterDataThenSuccess() throws TMBCommonException {
        InternationalTransferPurpose purpose = new InternationalTransferPurpose();
        purpose.setTransferPurposeCode("001");

        when(internationalTransferDataService.fetchPurposeMasterData()).thenReturn(List.of(purpose));

        ResponseEntity<TmbOneServiceResponse<List<InternationalTransferPurpose>>> response = internationalTransferDataController
                .getPurposeMasterData("32fbd3b2-3f97-4a89-ae39-b4f628fbc8da");

        Assertions.assertEquals("001", response.getBody().getData().get(0).getTransferPurposeCode());
    }

    @Test
    void testFetchOttCountryMasterDataThenSuccess() throws TMBCommonException {
        OTTCountry country = new OTTCountry();
        country.setClCode("US");

        when(internationalTransferDataService.fetchOttCountryMasterData()).thenReturn(List.of(country));

        ResponseEntity<TmbOneServiceResponse<List<OTTCountry>>> response = internationalTransferDataController
                .getOttCountryMaster("32fbd3b2-3f97-4a89-ae39-b4f628fbc8da");

        Assertions.assertEquals("US", response.getBody().getData().get(0).getClCode());
    }

    @Test
    public void getInterestRateODSSuccess() throws TMBCommonException, IOException {
        when(internationalTransferDataService.getInterestRate(anyString())).thenReturn(new InterestRateResponse());
        ResponseEntity<TmbOneServiceResponse<InterestRateResponse>> actual = internationalTransferDataController.getInterestRateODS("");

        Assertions.assertEquals(ResponseCode.SUCCESS.getCode(), actual.getBody().getStatus().getCode());
    }

    @Test
    public void getInterestRateODSFailure() throws TMBCommonException, IOException {
        when(internationalTransferDataService.getInterestRate(anyString())).thenThrow(NullPointerException.class);
        ResponseEntity<TmbOneServiceResponse<InterestRateResponse>> actual = internationalTransferDataController.getInterestRateODS("");

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), actual.getBody().getStatus().getCode());
    }

    @Test
    public void testDownloadDocumentThenSuccess() throws TMBCommonException {
        ECMDocument ecmDoc = new ECMDocument();
        ecmDoc.setIds("123456");

        ECMDocumentRequest ecmReq = new ECMDocumentRequest();
        ecmReq.setRefId("123456");
        ecmReq.setDebitOp(true);
        ecmReq.setSwiftOp(true);
        ecmReq.setTxnDateTime("030921-1830");

        when(callECMAppService.getAttachFile(ecmReq)).thenReturn(List.of(ecmDoc));

        ResponseEntity<TmbOneServiceResponse<List<ECMDocument>>> response = internationalTransferDataController
                .downloadDocument(ecmReq);

        Assertions.assertEquals("123456", response.getBody().getData().get(0).getIds());
    }

    @Test
    public void downloadDocumentFailure() {
        when(callECMAppService.getAttachFile(new ECMDocumentRequest())).thenThrow(NullPointerException.class);
        ResponseEntity<TmbOneServiceResponse<List<ECMDocument>>> actual = internationalTransferDataController.downloadDocument(new ECMDocumentRequest());

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), actual.getBody().getStatus().getCode());
    }
}
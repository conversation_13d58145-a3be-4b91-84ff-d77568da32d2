package com.tmb.oneapp.transferservice.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.controller.v1.V1TransferController;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.model.request.TransferOnUsConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankConfirmResponse;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferConfirmResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.service.V1OnUsTransferService;
import com.tmb.oneapp.transferservice.service.V1TransferOtherBankService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS_PIN_LOCK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransferControllerTest {
  @InjectMocks V1TransferController v1TransferController;
  @Mock V1TransferOtherBankService v1TransferOtherBankService;
  @Mock V1OnUsTransferService v1OnUsTransferService;
  @Mock CustomerService customerService;
  String crmId;
  List<JsonNode> data;
  ObjectMapper mapper;
  Map<String, String> reqHeader;
  String correlationId;
  HttpHeaders header;
  String deviceId;
  @BeforeEach
  void setUp() {
    data = new ArrayList<JsonNode>();
    mapper = new ObjectMapper();
    reqHeader = new HashMap<String, String>();
    reqHeader.put("timestamp", "11-11-20");
    reqHeader.put("x-forward-for", "123456");
    reqHeader.put("os-version", "android");
    reqHeader.put("channel", "channel");
    reqHeader.put("app-version", "1.00");
    reqHeader.put("x-crmid", "12345");
    reqHeader.put("device-id", "123");
    reqHeader.put("flow-name", "mobile");
    reqHeader.put("device-model", "sam");
    reqHeader.put("device-nickname", "sam");
    reqHeader.put("x-correlation-id", "123");

    crmId = "12345";
    correlationId = "54321";
    header = new HttpHeaders();
    deviceId = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76";
  }

  @Test
  void transOtherBankValidateSuccessTest()
      throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
    TransferOtherBankValidateRequest dataReq = new TransferOtherBankValidateRequest();
    String crmId = "5555";
    String correlationId = "555";
    when(v1TransferOtherBankService.transferOtherBankValidate(
            dataReq, crmId, correlationId, header))
        .thenReturn(new TransferOtherBankValidateResponse());
    ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> data =
        v1TransferController.transOtherBankValidate(correlationId, crmId, dataReq, header);

    assertEquals(200, data.getStatusCodeValue());
  }

  @Test
  void transOtherBankValidateThrowTest()
      throws NumberFormatException, TMBCommonException, TMBCustomCommonExceptionWithResponse {
    when(v1TransferOtherBankService.transferOtherBankValidate(
            any(), anyString(), anyString(), any()))
        .thenThrow(TMBCommonException.class);

    assertThrows(
        TMBCommonException.class,
        () ->
            v1TransferController.transOtherBankValidate(
                correlationId, crmId, new TransferOtherBankValidateRequest(), header));
  }

  @Test
  void transOtherBankAndPromptPayConfirmSuccessTest()
      throws IOException,
          TMBCommonException,
          TMBCustomCommonExceptionWithResponse,
          WriterException {
    TransferOtherBankConfirmRequest dataReq =
        TransferOtherBankConfirmRequest.builder().transId("1234").build();

    when(v1TransferOtherBankService.transferOtherBankConfirm(dataReq, crmId, correlationId, header))
        .thenReturn(new TransferOtherBankConfirmResponse());

    ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> data =
        v1TransferController.transOtherBankAndPromptPayConfirm(
            correlationId, crmId, dataReq, header);

    assertEquals(200, data.getStatusCodeValue());
  }

  @Test
  void transOtherBankAndPromptPayConfirmThrowTest()
          throws IOException,
          TMBCommonException,
          WriterException, TMBCustomCommonExceptionWithResponse {
    TransferOtherBankConfirmRequest dataReq =
        TransferOtherBankConfirmRequest.builder().transId("1234").build();

    when(v1TransferOtherBankService.transferOtherBankConfirm(dataReq, crmId, correlationId, header))
        .thenThrow(TMBCommonException.class);
    assertThrows(
        TMBCommonException.class,
        () ->
            v1TransferController.transOtherBankAndPromptPayConfirm(
                correlationId, crmId, dataReq, header));
  }

  @Test
  void testTransferOnUsValidateSuccessTest()
      throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
    V1OnUsValidateRequest onUsValidateRequest = new V1OnUsValidateRequest();
    when(v1OnUsTransferService.validate(onUsValidateRequest, crmId, correlationId, header))
        .thenReturn(new V1OnUsTransferValidateResponse());
    ResponseEntity<TmbOneServiceResponse<V1OnUsTransferValidateResponse>> data =
        v1TransferController.transferOnUsValidate(
            correlationId, crmId, onUsValidateRequest, header);
    assertEquals(200, data.getStatusCodeValue());
  }

  @Test
  void transferOnUsValidateThrowTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
    V1OnUsValidateRequest onUsValidateRequest = new V1OnUsValidateRequest();
    when(v1OnUsTransferService.validate(onUsValidateRequest, crmId, correlationId, header))
        .thenThrow(TMBCommonException.class);
    assertThrows(
        TMBCommonException.class,
        () ->
            v1TransferController.transferOnUsValidate(
                correlationId, crmId, onUsValidateRequest, header));
  }

  @Test
  void transferOnUsConfirmationSuccessTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException, ParseException {
    TransferOnUsConfirmRequest transferOnUsConfirmRequest = new TransferOnUsConfirmRequest();
    when(v1OnUsTransferService.confirm(transferOnUsConfirmRequest, crmId, correlationId, header))
        .thenReturn(new V1OnUsTransferConfirmResponse());
    ResponseEntity<TmbOneServiceResponse<V1OnUsTransferConfirmResponse>> data =
        v1TransferController.transferOnUsConfirmation(
            transferOnUsConfirmRequest, crmId, correlationId, header);
    assertEquals(200, data.getStatusCodeValue());
  }

  @Test
  void transferOnUsConfirmationThrowTest() throws TMBCommonException, IOException, WriterException, ParseException, TMBCustomCommonExceptionWithResponse {
    TransferOnUsConfirmRequest transferOnUsConfirmRequest = new TransferOnUsConfirmRequest();
    when(v1OnUsTransferService.confirm(transferOnUsConfirmRequest, crmId, correlationId, header))
        .thenThrow(TMBCommonException.class);
    assertThrows(
        TMBCommonException.class,
        () ->
            v1TransferController.transferOnUsConfirmation(
                transferOnUsConfirmRequest, crmId, correlationId, header));
  }

  @Test
  void transferPromptPayValidateSuccessTest()
      throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
    TransferOtherBankValidateRequest transferOtherBankValidateRequest =
        new TransferOtherBankValidateRequest();
    when(v1TransferOtherBankService.transferPromptPayValidate(
            transferOtherBankValidateRequest, crmId, correlationId, header))
        .thenReturn(new TransferOtherBankValidateResponse());
    ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>>
        tmbOneServiceResponseResponseEntity =
            v1TransferController.transferPromptPayValidate(
                correlationId, crmId, deviceId, transferOtherBankValidateRequest, header);
    assertEquals(200, tmbOneServiceResponseResponseEntity.getStatusCodeValue());
  }

  @Test
  void transferPromptPayValidateThrowTest()
      throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
    TMBCommonException exceptionBadRequestFromETE = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);

    TransferOtherBankValidateRequest transferOtherBankValidateRequest =
        new TransferOtherBankValidateRequest();

    when(v1TransferOtherBankService.transferPromptPayValidate(
            transferOtherBankValidateRequest, crmId, correlationId, header))
        .thenThrow(exceptionBadRequestFromETE);

    TMBCommonException exception = assertThrows(
            TMBCommonException.class,
            () ->
                    v1TransferController.transferPromptPayValidate(
                            correlationId, crmId, deviceId, transferOtherBankValidateRequest, header));

    Assertions.assertEquals(HttpStatus.OK.value(), exception.getStatus().value());
  }

  @Test
  void transferPromptPayConfirmSuccessTest()
      throws TMBCommonException,
          IOException,
          WriterException,
          TMBCustomCommonExceptionWithResponse {
    TransferOtherBankConfirmRequest confirmRequest = new TransferOtherBankConfirmRequest();
    when(v1TransferOtherBankService.transferOtherBankConfirm(
            confirmRequest, crmId, correlationId, header))
        .thenReturn(new TransferOtherBankConfirmResponse());
    ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> response =
        v1TransferController.transferPromptPayConfirm(
            correlationId, crmId, deviceId, confirmRequest, header);
    assertEquals(200, response.getStatusCodeValue());
  }

  @Test
  void transferPromptPayConfirmThrowTest()
          throws TMBCommonException,
          IOException,
          WriterException, TMBCustomCommonExceptionWithResponse {
    TransferOtherBankConfirmRequest confirmRequest = new TransferOtherBankConfirmRequest();
    when(v1TransferOtherBankService.transferOtherBankConfirm(
            confirmRequest, crmId, correlationId, header))
        .thenThrow(TMBCommonException.class);
    assertThrows(
        TMBCommonException.class,
        () ->
            v1TransferController.transferPromptPayConfirm(
                correlationId, crmId, deviceId, confirmRequest, header));
  }

  @Test
  void transferPromptPayConfirmThrowPinErrorLockedCauseTest()
      throws TMBCommonException {
		CustomerProfileStatus customerProfileStatus = new CustomerProfileStatus();
		customerProfileStatus.setMbUserStatusId(MB_CUSTOMER_STATUS_PIN_LOCK);
		when(customerService
				.getCustomerProfileByDeviceId(deviceId)).thenReturn(customerProfileStatus);
		TransferOtherBankConfirmRequest confirmRequest = new TransferOtherBankConfirmRequest();
		assertThrows(
				TMBCommonException.class,
				() ->
						v1TransferController.transferPromptPayConfirm(
								correlationId, "", deviceId, confirmRequest, header));
	}
	@Test
	void transferPromptPayConfirmThrowMbCustomerStatusTest()
			throws TMBCommonException {
		CustomerProfileStatus customerProfileStatus = new CustomerProfileStatus();
		customerProfileStatus.setMbUserStatusId(MB_CUSTOMER_STATUS);
		customerProfileStatus.setEbCustomerStatusId("");
		when(customerService
				.getCustomerProfileByDeviceId(deviceId)).thenReturn(customerProfileStatus);
		TransferOtherBankConfirmRequest confirmRequest = new TransferOtherBankConfirmRequest();
		assertThrows(
				TMBCommonException.class,
				() ->
						v1TransferController.transferPromptPayConfirm(
								correlationId, "", deviceId, confirmRequest, header));
	}
}

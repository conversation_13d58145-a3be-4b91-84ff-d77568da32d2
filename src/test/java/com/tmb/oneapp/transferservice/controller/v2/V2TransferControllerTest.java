package com.tmb.oneapp.transferservice.controller.v2;

import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.model.request.TransferOnUsConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankConfirmResponse;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferConfirmResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.service.V1OnUsTransferService;
import com.tmb.oneapp.transferservice.service.V1TransferOtherBankService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.text.ParseException;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.EB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS_PIN_LOCK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V2TransferControllerTest {

    @Mock
    private V1OnUsTransferService v1OnUsTransferService;

    @Mock
    private V1TransferOtherBankService v1TransferOtherBankService;

    @Mock
    private CustomerService customerService;

    @InjectMocks
    private V2TransferController v2TransferController;

    private HttpHeaders requestHeaders;
    private String correlationId;
    private String crmId;
    private String deviceId;

    @BeforeEach
    void setUp() {
        correlationId = "test-correlation-id";
        crmId = "test-crmId";
        deviceId = "test-deviceId";

        requestHeaders = new HttpHeaders();
        requestHeaders.add(HEADER_CORRELATION_ID, correlationId);
        requestHeaders.add(HEADER_CRM_ID, crmId);
        requestHeaders.add(DEVICE_ID, deviceId);
    }
    
    @Test
    void testValidRequestWhenValidateOnUsThenReturnSuccessResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        V1OnUsTransferValidateResponse expectedResponse = new V1OnUsTransferValidateResponse();
        when(v1OnUsTransferService.validate(request, crmId, correlationId, requestHeaders))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<V1OnUsTransferValidateResponse>> response =
                v2TransferController.validateOnUs(correlationId, crmId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testExceptionWhenValidateOnUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        TMBCommonException throwHttpBadRequest = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        when(v1OnUsTransferService.validate(request, crmId, correlationId, requestHeaders))
                .thenThrow(throwHttpBadRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.validateOnUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testNullPointerExceptionWhenValidateOnUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        V1OnUsValidateRequest request = new V1OnUsValidateRequest();
        when(v1OnUsTransferService.validate(request, crmId, correlationId, requestHeaders))
                .thenThrow(new NullPointerException(""));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.validateOnUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }


    @Test
    void testValidRequestWhenConfirmOnUsThenReturnSuccessResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, ParseException, WriterException {
        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        V1OnUsTransferConfirmResponse expectedResponse = new V1OnUsTransferConfirmResponse();
        when(v1OnUsTransferService.confirm(request, crmId, correlationId, requestHeaders))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<V1OnUsTransferConfirmResponse>> response =
                v2TransferController.confirmOnUs(correlationId, crmId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testNullPointerExceptionWhenConfirmOnUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, ParseException, WriterException {
        TransferOnUsConfirmRequest request = new TransferOnUsConfirmRequest();
        when(v1OnUsTransferService.confirm(request, crmId, correlationId, requestHeaders))
                .thenThrow(new NullPointerException(""));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.confirmOnUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }


    @Test
    void testValidRequestWhenValidateOffUsThenReturnSuccessResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        TransferOtherBankValidateResponse expectedResponse = new TransferOtherBankValidateResponse();
        when(v1TransferOtherBankService.transferOtherBankValidate(request, crmId, correlationId, requestHeaders))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> response =
                v2TransferController.validateOffUs(correlationId, crmId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testExceptionWhenValidateOffUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TMBCommonException throwHttpBadRequest = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        when(v1TransferOtherBankService.transferOtherBankValidate(request, crmId, correlationId, requestHeaders))
                .thenThrow(throwHttpBadRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.validateOffUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testNullPointerExceptionWhenValidateOffUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        when(v1TransferOtherBankService.transferOtherBankValidate(request, crmId, correlationId, requestHeaders))
                .thenThrow(new NullPointerException(""));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.validateOffUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidRequestWhenConfirmOffUsThenReturnSuccessResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        TransferOtherBankConfirmResponse expectedResponse = new TransferOtherBankConfirmResponse();
        when(v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, requestHeaders))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> response =
                v2TransferController.confirmOffUs(correlationId, crmId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testExceptionWhenConfirmOffUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TMBCommonException throwHttpBadRequest = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        when(v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, requestHeaders))
                .thenThrow(throwHttpBadRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.confirmOffUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testNullPointerExceptionWhenConfirmOffUsThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        when(v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, requestHeaders))
                .thenThrow(new NullPointerException(""));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.confirmOffUs(correlationId, crmId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidRequestWhenValidatePromptPayThenReturnSuccessResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        TransferOtherBankValidateResponse expectedResponse = new TransferOtherBankValidateResponse();
        when(v1TransferOtherBankService.transferPromptPayValidate(request, crmId, correlationId, requestHeaders))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> response =
                v2TransferController.validatePromptPay(correlationId, crmId, deviceId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testExceptionWhenValidatePromptPayThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TMBCommonException throwHttpBadRequest = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        when(v1TransferOtherBankService.transferPromptPayValidate(request, crmId, correlationId, requestHeaders))
                .thenThrow(throwHttpBadRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.validatePromptPay(correlationId, crmId, deviceId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testNullPointerExceptionWhenValidatePromptPayThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        when(v1TransferOtherBankService.transferPromptPayValidate(request, crmId, correlationId, requestHeaders))
                .thenThrow(new NullPointerException());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.validatePromptPay(correlationId, crmId, deviceId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidRequestWhenConfirmPromptPayThenReturnSuccessResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        TransferOtherBankConfirmResponse expectedResponse = new TransferOtherBankConfirmResponse();
        when(v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, requestHeaders))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> response =
                v2TransferController.confirmPromptPay(correlationId, crmId, deviceId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testExceptionWhenConfirmPromptPayThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TMBCommonException throwHttpBadRequest = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);

        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        when(v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, requestHeaders))
                .thenThrow(throwHttpBadRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.confirmPromptPay(correlationId, crmId, deviceId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testNullPointerExceptionWhenConfirmPromptPayThenThrowException() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        when(v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, requestHeaders))
                .thenThrow(new NullPointerException(""));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                v2TransferController.confirmPromptPay(correlationId, crmId, deviceId, request, requestHeaders));

        assertEquals(HttpStatus.OK, exception.getStatus());
        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testPreLoginIdWhenValidatePromptPayThenGetCrmIdFromDeviceId() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        TransferOtherBankValidateResponse expectedResponse = new TransferOtherBankValidateResponse();
        CustomerProfileStatus profileStatus = new CustomerProfileStatus();
        profileStatus.setCrmId(crmId);
        profileStatus.setMbUserStatusId(MB_CUSTOMER_STATUS);
        profileStatus.setEbCustomerStatusId(EB_CUSTOMER_STATUS);
        
        when(customerService.getCustomerProfileByDeviceId(deviceId)).thenReturn(profileStatus);
        when(v1TransferOtherBankService.transferPromptPayValidate(eq(request), eq(crmId), eq(correlationId), any()))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> response =
                v2TransferController.validatePromptPay(correlationId, "", deviceId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testPreLoginIdWhenConfirmPromptPayThenGetCrmIdFromDeviceId() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        TransferOtherBankConfirmRequest request = new TransferOtherBankConfirmRequest();
        TransferOtherBankConfirmResponse expectedResponse = new TransferOtherBankConfirmResponse();
        CustomerProfileStatus profileStatus = new CustomerProfileStatus();
        profileStatus.setCrmId(crmId);
        profileStatus.setMbUserStatusId(MB_CUSTOMER_STATUS);
        profileStatus.setEbCustomerStatusId(EB_CUSTOMER_STATUS);
        
        when(customerService.getCustomerProfileByDeviceId(deviceId)).thenReturn(profileStatus);
        when(v1TransferOtherBankService.transferOtherBankConfirm(eq(request), eq(crmId), eq(correlationId), any()))
                .thenReturn(expectedResponse);

        ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> response =
                v2TransferController.confirmPromptPay(correlationId, "", deviceId, request, requestHeaders);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody().getData());
    }

    @Test
    void testPinLockedStatusWhenGetCrmIdFromDeviceIdThenThrowException() throws TMBCommonException {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        CustomerProfileStatus profileStatus = new CustomerProfileStatus();
        profileStatus.setMbUserStatusId(MB_CUSTOMER_STATUS_PIN_LOCK);
        
        when(customerService.getCustomerProfileByDeviceId(deviceId)).thenReturn(profileStatus);

        assertThrows(TMBCommonException.class, () ->
                v2TransferController.validatePromptPay(correlationId, "", deviceId, request, requestHeaders));
    }

    @Test
    void testInactiveCustomerStatusWhenGetCrmIdFromDeviceIdThenThrowException() throws TMBCommonException {
        TransferOtherBankValidateRequest request = new TransferOtherBankValidateRequest();
        CustomerProfileStatus profileStatus = new CustomerProfileStatus();
        profileStatus.setMbUserStatusId(MB_CUSTOMER_STATUS);
        profileStatus.setEbCustomerStatusId("INACTIVE");
        
        when(customerService.getCustomerProfileByDeviceId(deviceId)).thenReturn(profileStatus);

        assertThrows(TMBCommonException.class, () ->
                v2TransferController.validatePromptPay(correlationId, "", deviceId, request, requestHeaders));
    }
}
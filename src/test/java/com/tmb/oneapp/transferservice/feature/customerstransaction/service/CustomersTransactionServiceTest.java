package com.tmb.oneapp.transferservice.feature.customerstransaction.service;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.customerstransaction.client.CustomersTransactionClient;
import com.tmb.oneapp.transferservice.feature.customerstransaction.model.TriggerCacheRequest;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CustomersTransactionServiceTest {
    @InjectMocks
    CustomersTransactionService customersTransactionService;

    @Mock
    CustomersTransactionClient customersTransactionClient;
    @Captor
    ArgumentCaptor<TriggerCacheRequest> triggerCacheRequestArgumentCaptor;

    String crmId;
    String correlationId;
    @BeforeEach
    void setup() {
        crmId = "0001010203021301230";
        correlationId = "20202392392319023";
    }

    @Test
    void testCacheDepositWhenSuccessShouldNotThrowsException() {
        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        mockTmbStatusSuccess(tmbStatus);

        tmbOneServiceResponse.setData(ResponseCode.SUCCESS.getMessage());
        tmbOneServiceResponse.setStatus(tmbStatus);

        when(customersTransactionClient.triggerClearCache(anyString(), triggerCacheRequestArgumentCaptor.capture())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        assertDoesNotThrow(()-> customersTransactionService.clearDepositCache(correlationId, crmId));

        assertEquals(TransferServiceConstant.GROUP_TYPE_DEPOSIT, triggerCacheRequestArgumentCaptor.getValue().getProductGroup());
        assertEquals(crmId, triggerCacheRequestArgumentCaptor.getValue().getCrmId());
    }
    @Test
    void testCacheCreditCardWhenSuccessShouldNotThrowsException() {
        TmbOneServiceResponse<String> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus();
        mockTmbStatusSuccess(tmbStatus);

        tmbOneServiceResponse.setData(ResponseCode.SUCCESS.getMessage());
        tmbOneServiceResponse.setStatus(tmbStatus);

        when(customersTransactionClient.triggerClearCache(anyString(), triggerCacheRequestArgumentCaptor.capture())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        assertDoesNotThrow(()-> customersTransactionService.clearCreditCardCache(correlationId, crmId));
        assertEquals(TransferServiceConstant.GROUP_TYPE_CREDIT_CARD, triggerCacheRequestArgumentCaptor.getValue().getProductGroup());
        assertEquals(crmId, triggerCacheRequestArgumentCaptor.getValue().getCrmId());
    }

    @Test
    void testCacheDepositWhenGotErrorShouldNotThrowsException() {
        when(customersTransactionClient.triggerClearCache(anyString(), triggerCacheRequestArgumentCaptor.capture())).thenThrow(FeignException.FeignClientException.class);

        assertDoesNotThrow(()-> customersTransactionService.clearDepositCache(correlationId, crmId));
        assertEquals(TransferServiceConstant.GROUP_TYPE_DEPOSIT, triggerCacheRequestArgumentCaptor.getValue().getProductGroup());
        assertEquals(crmId, triggerCacheRequestArgumentCaptor.getValue().getCrmId());
    }

    @Test
    void testCacheCreditCardWhenGotErrorShouldNotThrowsException() {
        when(customersTransactionClient.triggerClearCache(anyString(), triggerCacheRequestArgumentCaptor.capture())).thenThrow(FeignException.FeignClientException.class);

        assertDoesNotThrow(()-> customersTransactionService.clearCreditCardCache(correlationId, crmId));
        assertEquals(TransferServiceConstant.GROUP_TYPE_CREDIT_CARD, triggerCacheRequestArgumentCaptor.getValue().getProductGroup());
        assertEquals(crmId, triggerCacheRequestArgumentCaptor.getValue().getCrmId());
    }

    private static void mockTmbStatusSuccess(TmbStatus tmbStatus) {
        tmbStatus.setMessage(ResponseCode.SUCCESS.getMessage());
        tmbStatus.setCode(ResponseCode.SUCCESS.getCode());
        tmbStatus.setDescription(ResponseCode.SUCCESS.getDescription());
    }
}

package com.tmb.oneapp.transferservice.feature.financiallog.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.financiallog.model.FinRequest;
import com.tmb.oneapp.transferservice.feature.financiallog.model.FinRequestOtherBankLog;
import com.tmb.oneapp.transferservice.feature.financiallog.model.OtherBankTransferTransactionLog;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.transfer.TransferActivities;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.TestPropertySource;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@TestPropertySource(properties = {
        "oneapp.customer.financial-log.topic-name=finance",
        "oneapp.customer.transaction-log.topic-name=trans"
})
@ExtendWith(MockitoExtension.class)
public class FinancialLogServiceTest {
    @Mock
    KafkaProducerService kafkaProducerService;

    @Mock
    CustomerService customerService;

    @InjectMocks
    FinancialLogService financialLogService;

    @Test
    void saveLogFinancialAndTransactionEventTest(){

        doNothing().when(kafkaProducerService).sendMessageAsync(any(),any());

        assertDoesNotThrow(() -> {
            financialLogService.saveLogFinancialAndTransactionEvent("1234",new FinRequestOtherBankLog(),new OtherBankTransferTransactionLog());
        });
    }

    @Test
    void testSaveLogFinancialAndTransactionEvent_withExchangeLog() throws JsonProcessingException, TMBCommonException {
        FinRequest finRequest = new FinRequest();
        finRequest.setReferenceID("ref456");
        finRequest.setActivityTypeIdNew("act002");
        finRequest.setTxnAmount("1000.00");
        finRequest.setFinFlexValues3("35000.00");
        finRequest.setFromAccType("01");
        finRequest.setToAccType("02");
        finRequest.setFromAccNo("**********");
        finRequest.setToAccNo("**********");
        finRequest.setFromAccName("John");
        finRequest.setToAccName("Doe");
        finRequest.setCrmId("crm123");
        finRequest.setFinFlexValues1("THB");
        finRequest.setFinFlexValues2("USD");
        finRequest.setFromAccType("SDA");
        finRequest.setTxnAmount("100.00");

        V1TransferData cacheOnUs = new V1TransferData();
        cacheOnUs.setFxRate("1000.00");
        cacheOnUs.setUnitCurrency("1000.00");
        cacheOnUs.setAmountTHB(BigDecimal.valueOf(100.00));

        TransferActivities activities = new TransferActivities();
        activities.setTransactionDate(String.valueOf(new Date().getTime()));

        CustomerKYCResponse customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setCustomerFirstNameTh("test");
        customerKYCResponse.setCustomerLastNameTh("test");
        customerKYCResponse.setCustomerTitleTh("test");
        when(customerService.getCustomerKyc(any(), any())).thenReturn(customerKYCResponse);

        assertDoesNotThrow(() -> {
            financialLogService.saveLogFinancialAndTransactionEvent("corId456", finRequest, activities,
                    true, cacheOnUs, "Y");
        });
    }

    @Test
    void testMapAccountTypeForTransactionLog() throws Exception {
        Method method = FinancialLogService.class
                .getDeclaredMethod("mapAccountTypeForTransactionLog", String.class);
        method.setAccessible(true);

        assertEquals("SA", method.invoke(financialLogService, "SDA")); // SAVING
        assertEquals("CA", method.invoke(financialLogService, "DDA")); // CURRENT
        assertEquals("TD", method.invoke(financialLogService, "CDA")); // TERM DEPOSIT
        assertNull(method.invoke(financialLogService, "99"));         // Unknown
        assertNull(method.invoke(financialLogService, ""));           // Empty
    }

}

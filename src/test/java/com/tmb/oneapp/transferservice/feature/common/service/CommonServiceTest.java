package com.tmb.oneapp.transferservice.feature.common.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.common.client.CommonServiceClient;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class CommonServiceTest {

    @Mock
    private CommonServiceClient commonServiceClient;
    @InjectMocks
    private CommonService commonServiceImp;

    @Test
    void getConfiguration_NormalFlowShouldReturnResponse() throws TMBCommonException {
        TmbOneServiceResponse<List<CommonData>> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(new ArrayList<>());
        ResponseEntity<TmbOneServiceResponse<List<CommonData>>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(commonServiceClient.getCommonConfiguration(any(), any())).thenReturn(httpOneAppCommonConfigResponse);
        List<CommonData> actualResponse = commonServiceImp.getCommonConfiguration("corelationId", "common_module");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getConfiguration_NotSuccessCodeShouldThrowsTmbException() {
        TmbOneServiceResponse<List<CommonData>> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(commonServiceClient.getCommonConfiguration(any(), any())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            commonServiceImp.getCommonConfiguration("corelationId", "common_module");
        });
    }
}
package com.tmb.oneapp.transferservice.feature.notification.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.model.response.notification.NotificationResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.notification.client.NotificationServiceClient;
import com.tmb.oneapp.transferservice.feature.notification.model.V1ENotificationSettingResponse;
import com.tmb.oneapp.transferservice.feature.notification.model.V1TransferNotification;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
class V1TransferNotificationServiceTest {

    @Mock
    private CustomerService customerService;

    @Mock
    private NotificationServiceClient notificationServiceClient;

    @Spy
    private ObjectMapper objectMapper = new ObjectMapper();
    @InjectMocks
    private V1TransferNotificationService transferNotificationService;

    @Test
    void sendTransferNotification_NormalFlowShouldCallNotificationService() throws TMBCommonException {
        V1ENotificationSettingResponse eNotiSettingResponse = new V1ENotificationSettingResponse();
        eNotiSettingResponse.setTransactionNotification(true);
        Mockito.when(customerService.getENotificationSetting(anyString(), anyString())).thenReturn(eNotiSettingResponse);

        CustomerKYCResponse customerKycResponse = new CustomerKYCResponse();
        Mockito.when(customerService.getCustomerKyc(anyString(), anyString())).thenReturn(customerKycResponse);

        TmbOneServiceResponse<NotificationResponse> notificationResponse = new TmbOneServiceResponse<>();
        notificationResponse.setData(new NotificationResponse());
        notificationResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        ResponseEntity<TmbOneServiceResponse<NotificationResponse>> httpNotificationResponse = ResponseEntity.ok(notificationResponse);
        Mockito.when(notificationServiceClient.sendMessage(anyString(), any())).thenReturn(httpNotificationResponse);
        V1TransferNotification transferNotification = new V1TransferNotification();
        transferNotification.setCrmId("123");
        transferNotification.setXCorrelationId("abc");
        transferNotification.setTemplateName("oneapp-transfer-complete");
        String email = "<EMAIL>";
        transferNotificationService.sendTransferNotification(transferNotification, email);

        Mockito.verify(notificationServiceClient).sendMessage(anyString(), any());
    }
}
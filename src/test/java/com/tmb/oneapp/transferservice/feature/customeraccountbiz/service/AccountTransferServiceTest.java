package com.tmb.oneapp.transferservice.feature.customeraccountbiz.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.AccountSaving;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.DepositAccountTransfer;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AccountTransferServiceTest {

    @InjectMocks
    AccountTransferService accountTransferService;
    @Mock
    CustomerAccountBizService customerAccountBizService;

    String crmId;
    String correlationId;
    @BeforeEach
    void setup() {
        crmId = "0001010203021301230";
        correlationId = "*****************";
    }

    @Test
    void testGetDepositAccountListWhenDataIsExistedThenSuccess() throws TMBCommonException {
        AccountSaving accountSaving = new AccountSaving();
        List<DepositAccount> depositAccounts = mockDepositAccounts();

        accountSaving.setDepositAccountLists(depositAccounts);

        when(customerAccountBizService.getAccountList(correlationId, crmId, false, true)).thenReturn(accountSaving);

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        assertEquals(3, actual.size());
    }

    @Test
    void testGetDepositAccountListWhenCustomerAccountBizGotExceptionShouldThrowsTmbCommonException() throws TMBCommonException {
        when(customerAccountBizService.getAccountList(correlationId, crmId, false, true)).thenThrow(FeignException.FeignClientException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));

        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }
    @Test
    void testGetDepositAccountListWhenDataIsEmptyShouldThrowsTmbCommonException() throws TMBCommonException {
        AccountSaving accountSaving = new AccountSaving();
        List<DepositAccount> depositAccounts = Collections.emptyList();

        accountSaving.setDepositAccountLists(depositAccounts);

        when(customerAccountBizService.getAccountList(correlationId, crmId, false, true)).thenReturn(accountSaving);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));

        assertEquals(ResponseCode.ACCOUNT_NOT_ELIGIBLE.getCode(), exception.getErrorCode());
    }

    private static List<DepositAccount> mockDepositAccounts() {
        List<DepositAccount> depositAccounts = new ArrayList<>();

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountStatus("ACTIVE");
        depositAccount.setDisplayAccountStatus("01");
        depositAccount.setTransferOwnTTBMapCode("010");

        depositAccounts.add(depositAccount);

        depositAccount = new DepositAccount();
        depositAccount.setAccountStatus("INACTIVE");
        depositAccount.setDisplayAccountStatus("01");
        depositAccount.setTransferOtherTTBMapCode("010");

        depositAccounts.add(depositAccount);

        depositAccount = new DepositAccount();
        depositAccount.setAccountStatus("DORMANT");
        depositAccount.setDisplayAccountStatus("01");
        depositAccount.setTransferOwnTTBMapCode("010");
        depositAccount.setTransferOtherTTBMapCode("010");

        depositAccounts.add(depositAccount);

        depositAccount = new DepositAccount();
        depositAccount.setAccountStatus("INACTIVE");
        depositAccount.setDisplayAccountStatus("02");
        depositAccount.setTransferOwnTTBMapCode("010");
        depositAccount.setTransferOtherTTBMapCode("010");

        depositAccounts.add(depositAccount);
        return depositAccounts;
    }

    @Test
    void testGetDepositAllAccountListWhenDataIsExistedThenSuccess() throws TMBCommonException {
        AccountSaving accountSaving = new AccountSaving();
        List<DepositAccount> depositAccounts = mockDepositAccounts();

        accountSaving.setDepositAccountLists(depositAccounts);
        accountSaving.setFcdAccountLists(depositAccounts);

        when(customerAccountBizService.getAccountList(correlationId, crmId, false, true)).thenReturn(accountSaving);

        List<DepositAccountTransfer> actual = accountTransferService.getAllDepositAccountList(correlationId, crmId, false, true);

        assertEquals(6, actual.size());
    }

    @Test
    void testGetDepositAllAccountListWhenCustomerAccountBizGotExceptionShouldThrowsTmbCommonException() throws TMBCommonException {
        when(customerAccountBizService.getAccountList(correlationId, crmId, false, true)).thenThrow(FeignException.FeignClientException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> accountTransferService.getAllDepositAccountList(correlationId, crmId, false, true));

        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }
    @Test
    void testGetDepositAllAccountListWhenDataIsEmptyShouldThrowsTmbCommonException() throws TMBCommonException {
        AccountSaving accountSaving = new AccountSaving();
        List<DepositAccount> depositAccounts = Collections.emptyList();

        accountSaving.setDepositAccountLists(depositAccounts);
        accountSaving.setFcdAccountLists(depositAccounts);

        when(customerAccountBizService.getAccountList(correlationId, crmId, false, true)).thenReturn(accountSaving);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> accountTransferService.getAllDepositAccountList(correlationId, crmId, false, true));

        assertEquals(ResponseCode.ACCOUNT_NOT_ELIGIBLE.getCode(), exception.getErrorCode());
    }
}

package com.tmb.oneapp.transferservice.feature.bank.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.feature.bank.client.BankServiceClient;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.transferservice.model.bank.BankInfoDataModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;


@ExtendWith(MockitoExtension.class)
class BankServiceImpTest {

    @Mock
    private BankServiceClient bankServiceClient;

    @InjectMocks
    private BankService bankServiceImp;

    @Test
    void getCategories_NormalFlowShouldReturnResponse() throws TMBCommonException {
        TmbOneServiceResponse<List<CategoryInfoDataModel>> categoryResponse = new TmbOneServiceResponse<>();
        categoryResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        categoryResponse.setData(new ArrayList<>());
        ResponseEntity<TmbOneServiceResponse<List<CategoryInfoDataModel>>> httpCategoryResponse = ResponseEntity.ok(categoryResponse);
        Mockito.when(bankServiceClient.getAllCategory(anyString())).thenReturn(httpCategoryResponse);
        List<CategoryInfoDataModel> actualResponse = bankServiceImp.getCategories("abc");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getBanks_NormalFlowShouldReturnResponse() throws TMBCommonException {
        TmbOneServiceResponse<List<BankInfoDataModel>> bankResponse = new TmbOneServiceResponse<>();
        bankResponse.setData(new ArrayList<>());
        bankResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        ResponseEntity<TmbOneServiceResponse<List<BankInfoDataModel>>> httpBankResponse = ResponseEntity.ok(bankResponse);
        Mockito.when(bankServiceClient.getAllBankInfo(anyString())).thenReturn(httpBankResponse);
        List<BankInfoDataModel> actualResponse = bankServiceImp.getBanks("abc");
        Assertions.assertNotNull(actualResponse);
    }
}
package com.tmb.oneapp.transferservice.feature.activitylog.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.model.BaseEvent;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.ReflectionUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
class V1ActivityLogServiceTest {

    @Mock
    private KafkaProducerService kafkaProducerService;

    @InjectMocks
    private V1ActivityLogService activityLogService;
    @Test
    void logActivity_NormalFlowShouldCallKafka() {
        ReflectionTestUtils.setField(activityLogService, "topicName", "activity");
        BaseEvent actLog = new BaseEvent();
        activityLogService.logActivity(actLog);

        Mockito.verify(kafkaProducerService).sendMessageAsync(anyString(),anyString());
    }
}
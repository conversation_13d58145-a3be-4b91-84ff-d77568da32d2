package com.tmb.oneapp.transferservice.feature.customer.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.customer.client.CustomerExpServiceClient;
import com.tmb.oneapp.transferservice.feature.customer.client.CustomerServiceClient;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyRequest;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyResponse;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.DepositAccountTransfer;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.service.AccountTransferService;
import com.tmb.oneapp.transferservice.feature.notification.model.V1ENotificationSettingResponse;
import com.tmb.oneapp.transferservice.model.AccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.DailyUsageData;
import com.tmb.oneapp.transferservice.model.customer.PaymentAccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.customer.PinFreeCountData;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {
    @Mock
    private CustomerServiceClient customerServiceClient;
    @Mock
    private CustomerExpServiceClient customerExpServiceClient;
    @Mock
    private AccountTransferService accountTransferService;
    @InjectMocks
    private CustomerService customerService;

    String crmId = "";
    String correlationId = "";
    String ipAddress = "";

    @BeforeEach
    void setup() {
        crmId = "001100000000000000000000031310";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        ipAddress = "127.0.0.1";
    }

    @Test
    void getCrmProfile_success() throws TMBCommonException {
        TmbOneServiceResponse<V1CrmProfile> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(new V1CrmProfile());
        ResponseEntity<TmbOneServiceResponse<V1CrmProfile>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.fetchCustomerCrmProfile(anyString(), anyString())).thenReturn(httpOneAppCommonConfigResponse);
        V1CrmProfile actualResponse = customerService.getCrmProfile("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getCrmProfile_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<V1CrmProfile> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.fetchCustomerCrmProfile(anyString(), anyString())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        V1CrmProfile actualResponse = customerService.getCrmProfile("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getCrmProfile_throw_failed() {
        TmbOneServiceResponse<V1CrmProfile> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.fetchCustomerCrmProfile(anyString(), anyString())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.getCrmProfile("corelationId", "crmId");
        });
    }

    @Test
    void getAccountsTransfer_success() throws TMBCommonException {
        List<DepositAccountTransfer> depositAccountTransfers = new ArrayList<>();
        Mockito.when(accountTransferService.getDepositAccountList(anyString(), anyString(), any(), any())).thenReturn(depositAccountTransfers);
        List<DepositAccount> actualResponse = customerService.getAccountsTransfer("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getAccountsTransfer_return_empty_failed() throws TMBCommonException {
        List<DepositAccountTransfer> depositAccountTransfers = Collections.emptyList();
        Mockito.when(accountTransferService.getDepositAccountList(anyString(), anyString(), any(), any())).thenReturn(depositAccountTransfers);
        List<DepositAccount> actualResponse = customerService.getAccountsTransfer("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getAccountsTransfer_throw_failed() throws TMBCommonException {
        TmbOneServiceResponse<List<DepositAccount>> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(accountTransferService.getDepositAccountList(anyString(), anyString(), any(), any())).thenThrow(TMBCommonException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.getAccountsTransfer("corelationId", "crmId");
        });
    }

    @Test
    void getCustomerKyc_success() throws TMBCommonException {
        TmbOneServiceResponse<CustomerKYCResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(new CustomerKYCResponse());
        ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(httpOneAppCommonConfigResponse);
        CustomerKYCResponse actualResponse = customerService.getCustomerKyc("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getCustomerKyc_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<CustomerKYCResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        CustomerKYCResponse actualResponse = customerService.getCustomerKyc("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getCustomerKyc_throw_failed() {
        TmbOneServiceResponse<CustomerKYCResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.getCustomerKyc("corelationId", "crmId");
        });
    }

    @Test
    void updatePinFreeCount_success() throws TMBCommonException {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData("Test");
        ResponseEntity<TmbOneServiceResponse<String>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.updatePinFreeCount(anyString(), anyString(), any())).thenReturn(httpOneAppCommonConfigResponse);
        customerService.updatePinFreeCount("corelationId", "crmId", new PinFreeCountData());
        Mockito.verify(customerServiceClient, Mockito.times(1)).updatePinFreeCount(any(), anyString(), any());
    }

    @Test
    void updatePinFreeCount_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.updatePinFreeCount(anyString(), anyString(), any())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        customerService.updatePinFreeCount("corelationId", "crmId", new PinFreeCountData());
        Mockito.verify(customerServiceClient, Mockito.times(1)).updatePinFreeCount(any(), anyString(), any());
    }

    @Test
    void updatePinFreeCount_throw_failed() {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.updatePinFreeCount(anyString(), anyString(), any())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.updatePinFreeCount("corelationId", "crmId", new PinFreeCountData());
        });
    }

    @Test
    void updateDailyUsage_success() throws TMBCommonException {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData("Test");
        ResponseEntity<TmbOneServiceResponse<String>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.updateDailyUsage(anyString(), anyString(), any())).thenReturn(httpOneAppCommonConfigResponse);
        customerService.updateDailyUsage("corelationId", "crmId", new DailyUsageData());
        Mockito.verify(customerServiceClient, Mockito.times(1)).updateDailyUsage(any(), anyString(), any());
    }

    @Test
    void updateDailyUsage_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.updateDailyUsage(anyString(), anyString(), any())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        customerService.updateDailyUsage("corelationId", "crmId", new DailyUsageData());
        Mockito.verify(customerServiceClient, Mockito.times(1)).updateDailyUsage(any(), anyString(), any());
    }

    @Test
    void updateDailyUsage_throw_failed() {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.updateDailyUsage(anyString(), anyString(), any())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.updateDailyUsage("corelationId", "crmId", new DailyUsageData());
        });
    }

    @Test
    void getCustomerProfileByDeviceId_success() throws TMBCommonException {
        TmbOneServiceResponse<CustomerProfileStatus> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(new CustomerProfileStatus());
        ResponseEntity<TmbOneServiceResponse<CustomerProfileStatus>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.getCustomerProfileByDeviceId(anyString())).thenReturn(httpOneAppCommonConfigResponse);
        CustomerProfileStatus actualResponse = customerService.getCustomerProfileByDeviceId("deviceId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getCustomerProfileByDeviceId_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<CustomerProfileStatus> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.getCustomerProfileByDeviceId(anyString())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        CustomerProfileStatus actualResponse = customerService.getCustomerProfileByDeviceId("deviceId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getCustomerProfileByDeviceId_throw_failed() {
        TmbOneServiceResponse<CustomerProfileStatus> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.getCustomerProfileByDeviceId(anyString())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.getCustomerProfileByDeviceId("deviceId");
        });
    }

    @Test
    void getENotificationSetting_success() throws TMBCommonException {
        TmbOneServiceResponse<V1ENotificationSettingResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(new V1ENotificationSettingResponse());
        ResponseEntity<TmbOneServiceResponse<V1ENotificationSettingResponse>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.getENotificationSetting(anyString(), anyString())).thenReturn(httpOneAppCommonConfigResponse);
        V1ENotificationSettingResponse actualResponse = customerService.getENotificationSetting("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getENotificationSetting_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<V1ENotificationSettingResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.getENotificationSetting(anyString(), anyString())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        V1ENotificationSettingResponse actualResponse = customerService.getENotificationSetting("corelationId", "crmId");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getENotificationSetting_throw_failed() {
        TmbOneServiceResponse<V1ENotificationSettingResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.getENotificationSetting(anyString(), anyString())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.getENotificationSetting("corelationId", "crmId");
        });
    }

    @Test
    void updatePaymentAccumulateUsageAmount_success() throws TMBCommonException {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData("Test");
        ResponseEntity<TmbOneServiceResponse<String>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerServiceClient.updatePaymentAccumulateUsageAmount(anyString(), anyString(), any())).thenReturn(httpOneAppCommonConfigResponse);
        String actualResponse = customerService.updatePaymentAccumulateUsageAmount("corelationId", "crmId", new PaymentAccumulateUsageRequest());
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void updatePaymentAccumulateUsageAmount_return_empty_failed() throws TMBCommonException {
        TmbOneServiceResponse<String> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.updatePaymentAccumulateUsageAmount(anyString(), anyString(), any())).thenReturn(ResponseEntity.ok(oneAppCommonConfigResponse));
        String actualResponse = customerService.updatePaymentAccumulateUsageAmount("corelationId", "crmId", new PaymentAccumulateUsageRequest());
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void updatePaymentAccumulateUsageAmount_throw_failed() {
        TmbOneServiceResponse<V1ENotificationSettingResponse> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), null, null, null));
        Mockito.when(customerServiceClient.updatePaymentAccumulateUsageAmount(anyString(), anyString(), any())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.updatePaymentAccumulateUsageAmount("corelationId", "crmId", new PaymentAccumulateUsageRequest());
        });
    }

    @Test
    void getFRWhiteList_NormalFlowShouldReturnResponse() throws TMBCommonException {
        TmbOneServiceResponse<FRWhitelistResult> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(new FRWhitelistResult());
        ResponseEntity<TmbOneServiceResponse<FRWhitelistResult>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerExpServiceClient.getFrWhitelistResult(any(), any())).thenReturn(httpOneAppCommonConfigResponse);
        FRWhitelistResult actualResponse = customerService.isFrWhitelistByCrmId("corelationId", "common_module");
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getFRWhiteList_NotSuccessCodeShouldThrowsTmbException() {
        Mockito.when(customerExpServiceClient.getFrWhitelistResult(any(), any())).thenThrow(FeignException.FeignClientException.class);
        assertThrows(TMBCommonException.class, () -> {
            customerService.isFrWhitelistByCrmId("corelationId", "common_module");
        });
    }

    @Test
    void getFRWhiteList_ShouldReturnNull() throws TMBCommonException {
        TmbOneServiceResponse<FRWhitelistResult> oneAppCommonConfigResponse = new TmbOneServiceResponse<>();
        oneAppCommonConfigResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), null, null, null));
        oneAppCommonConfigResponse.setData(null);
        ResponseEntity<TmbOneServiceResponse<FRWhitelistResult>> httpOneAppCommonConfigResponse = ResponseEntity.ok(oneAppCommonConfigResponse);
        Mockito.when(customerExpServiceClient.getFrWhitelistResult(any(), any())).thenReturn(httpOneAppCommonConfigResponse);
        FRWhitelistResult actualResponse = customerService.isFrWhitelistByCrmId("corelationId", "common_module");
        Assertions.assertEquals(actualResponse, null);
    }


    @Test
    void testGetCommonFRByUUID_WhenExistedShouldReturnSuccess() throws TMBCommonException {
        String frUuid = UUID.randomUUID().toString();
        CommonFRVerifyResponse commonFRVerifyResponse = new CommonFRVerifyResponse();
        commonFRVerifyResponse.setUuid(frUuid);
        commonFRVerifyResponse.setCommonfrSuccess(true);
        commonFRVerifyResponse.setCreateDate("2023-05-09");
        TmbServiceResponse<CommonFRVerifyResponse> response = new TmbServiceResponse<>();
        response.setData(commonFRVerifyResponse);
        CommonFRVerifyRequest commonFRVerifyRequest = CommonFRVerifyRequest.builder()
                .uuid(frUuid)
                .flow("FLOW")
                .featureId(1001).build();
        when(customerServiceClient.getCommonFRByUUID(eq(correlationId), eq(crmId), eq(ipAddress), any())).thenReturn(ResponseEntity.ok(response));
        CommonFRVerifyResponse actual = customerService.getCommonFRByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress);
        assertTrue(actual.getCommonfrSuccess());
    }

    @Test
    void testGetCommonFRByUUID_WhenNotExistedShouldReturnSuccessWithNullAllData() throws TMBCommonException {
        String frUuid = UUID.randomUUID().toString();
        CommonFRVerifyResponse commonFRVerifyResponse = new CommonFRVerifyResponse();
        commonFRVerifyResponse.setUuid(null);
        commonFRVerifyResponse.setCommonfrSuccess(null);
        commonFRVerifyResponse.setCreateDate(null);
        TmbServiceResponse<CommonFRVerifyResponse> response = new TmbServiceResponse<>();
        response.setData(commonFRVerifyResponse);
        CommonFRVerifyRequest commonFRVerifyRequest = CommonFRVerifyRequest.builder()
                .uuid(frUuid)
                .flow("FLOW")
                .featureId(1001).build();
        when(customerServiceClient.getCommonFRByUUID(eq(correlationId), eq(crmId), eq(ipAddress), any())).thenReturn(ResponseEntity.ok(response));
        CommonFRVerifyResponse actual = customerService.getCommonFRByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress);
        assertTrue(Objects.isNull(actual.getCommonfrSuccess()));
        assertTrue(Objects.isNull(actual.getUuid()));
        assertTrue(Objects.isNull(actual.getCreateDate()));
    }

    @Test
    void testCheckIsCommonFRExistedByUUID_whenExistedShouldReturnTrue() throws TMBCommonException {
        String frUuid = UUID.randomUUID().toString();
        CommonFRVerifyResponse commonFRVerifyResponse = new CommonFRVerifyResponse();
        commonFRVerifyResponse.setUuid(frUuid);
        commonFRVerifyResponse.setCommonfrSuccess(true);
        commonFRVerifyResponse.setCreateDate("2023-05-09");
        TmbServiceResponse<CommonFRVerifyResponse> response = new TmbServiceResponse<>();
        response.setData(commonFRVerifyResponse);
        CommonFRVerifyRequest commonFRVerifyRequest = CommonFRVerifyRequest.builder()
                .uuid(frUuid)
                .flow("FLOW")
                .featureId(1001).build();
        when(customerServiceClient.getCommonFRByUUID(eq(correlationId), eq(crmId), anyString(), any())).thenReturn(ResponseEntity.ok(response));
        boolean actual = customerService.isCommonFRExistedByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress);
        assertTrue(actual);
    }

    @Test
    void testCheckIsCommonFRExistedByUUID_whenNotExistedShouldReturnFalse() throws TMBCommonException {
        String frUuid = UUID.randomUUID().toString();
        CommonFRVerifyResponse commonFRVerifyResponse = new CommonFRVerifyResponse();
        TmbServiceResponse<CommonFRVerifyResponse> response = new TmbServiceResponse<>();
        response.setData(commonFRVerifyResponse);
        CommonFRVerifyRequest commonFRVerifyRequest = CommonFRVerifyRequest.builder()
                .uuid(frUuid)
                .flow("FLOW")
                .featureId(1001).build();
        when(customerServiceClient.getCommonFRByUUID(eq(correlationId), eq(crmId), eq(ipAddress), any())).thenReturn(ResponseEntity.ok(response));
        boolean actual = customerService.isCommonFRExistedByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress);
        assertFalse(actual);
    }

    @Test
    void testGetCommonFRByUUID_whenHasErrorShouldThrowsTMBCommonException() {
        String frUuid = UUID.randomUUID().toString();
        CommonFRVerifyRequest commonFRVerifyRequest = CommonFRVerifyRequest.builder()
                .uuid(frUuid)
                .flow("FLOW")
                .featureId(1001).build();
        when(customerServiceClient.getCommonFRByUUID(anyString(), anyString(), anyString(), any())).thenThrow(FeignException.class);
        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> customerService.getCommonFRByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress));
        assertEquals(ResponseCode.FAILED.getCode(), actual.getErrorCode());
    }

    @Test
    void testCheckCommonFRWhite_WhenInWhitelistShouldReturnTrue() throws TMBCommonException {
        FRWhitelistResult frWhitelistResult = new FRWhitelistResult();
        frWhitelistResult.setIsInFrWhitelist(true);
        TmbOneServiceResponse<FRWhitelistResult> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
        tmbOneServiceResponse.setData(frWhitelistResult);
        when(customerExpServiceClient.getFrWhitelistResult(crmId, correlationId)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
        boolean actual = customerService.isFrWhitelistByCrmId(crmId, correlationId).getIsInFrWhitelist();
        assertTrue(actual);
    }

    @Test
    void testCheckCommonFRWhite_WhenNotInWhitelistShouldReturnFalse() throws TMBCommonException {
        FRWhitelistResult frWhitelistResult = new FRWhitelistResult();
        frWhitelistResult.setIsInFrWhitelist(false);
        TmbOneServiceResponse<FRWhitelistResult> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));
        tmbOneServiceResponse.setData(frWhitelistResult);
        when(customerExpServiceClient.getFrWhitelistResult(crmId, correlationId)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
        boolean actual = customerService.isFrWhitelistByCrmId(crmId, correlationId).getIsInFrWhitelist();
        assertFalse(actual);
    }

    @Test
    void testUpdateUsageAccumulation_Success() {
        TmbServiceResponse<String> tmbServiceResponse = new TmbServiceResponse<>();
        Status status = new Status();
        status.setCode(TransferServiceConstant.SUCCESS_CODE);
        tmbServiceResponse.setStatus(status);
        tmbServiceResponse.setData("success");
        when(customerServiceClient.updateUsageAccumulation(eq(correlationId), eq(crmId), any(AccumulateUsageRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> customerService.updateUsageAccumulation(correlationId, crmId, new AccumulateUsageRequest()));
    }

    @Test
    void testUpdateUsageAccumulation_WhenFailedFeignException_ShouldThrowTMBCommonException() {
        when(customerServiceClient.updateUsageAccumulation(eq(correlationId), eq(crmId), any(AccumulateUsageRequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> customerService.updateUsageAccumulation(correlationId, crmId, new AccumulateUsageRequest()));
    }
}
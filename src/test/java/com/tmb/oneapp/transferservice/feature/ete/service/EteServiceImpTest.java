package com.tmb.oneapp.transferservice.feature.ete.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.ete.client.EteDepositTermClient;
import com.tmb.oneapp.transferservice.feature.ete.client.EteGetAccountClient;
import com.tmb.oneapp.transferservice.feature.ete.client.EteTransferConfirmationClient;
import com.tmb.oneapp.transferservice.feature.ete.client.EteTransferValidationClient;
import com.tmb.oneapp.transferservice.feature.ete.client.PromptpayConfirmationFeignClient;
import com.tmb.oneapp.transferservice.feature.ete.client.PromptpayValidationFeignClient;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteError;
import com.tmb.oneapp.transferservice.feature.ete.model.EteStatus;
import com.tmb.oneapp.transferservice.feature.ete.model.EteTransferAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.feature.ete.model.Sender;
import com.tmb.oneapp.transferservice.feature.ete.model.TMBDataSuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferOtherBankETERequest;
import com.tmb.oneapp.transferservice.model.FeignAdditionalStatus;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.service.V1TransfersServiceHelper;
import feign.FeignException;
import feign.Request;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BLANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FAILED_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.STATUS_SUCCESS_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EteServiceImpTest {

    @Mock
    private EteTransferValidationClient eteTransferValidationClient;
    @Mock
    private EteGetAccountClient eteGetAccountClient;

    @Mock
    private EteDepositTermClient eteDepositTermClient;

    @Mock
    private EteTransferConfirmationClient eteTransferConfirmationClient;
    @Mock
    private PromptpayValidationFeignClient promptpayValidationFeignClient;
    @Mock
    private PromptpayConfirmationFeignClient promptpayConfirmationFeignClient;
    @Mock
    V1TransfersServiceHelper v1TransferServiceHelper;
    @Spy
    @InjectMocks
    private EteServiceImp eteServiceImp;

    @Mock
    ObjectMapper mapper;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(eteServiceImp, "bankCdBBL", "02");
        ReflectionTestUtils.setField(eteServiceImp, "bankCdKBANK", "04");
        ReflectionTestUtils.setField(eteServiceImp, "bankCdKTB", "06");
        ReflectionTestUtils.setField(eteServiceImp, "bankCdSCB", "14");
        ReflectionTestUtils.setField(eteServiceImp, "bankCdBAY", "25");
        ReflectionTestUtils.setField(eteServiceImp, "bankCdGSB", "30");
    }

    @Test
    void getFundTransferValidation_NormalFlowShouldReturnResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toAccountNo = "to123";
        String fromAccountNo = "from123";
        String depositNo = "from123";
        BigDecimal amount = new BigDecimal("10.0");
        String crrDate = "2022-04-04";
        FundTransferOwnTMBETESuccess eteValidateTransferResponse = new FundTransferOwnTMBETESuccess();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        eteValidateTransferResponse.setStatus(eteStatus);
        eteValidateTransferResponse.setData(new TMBDataSuccess());
        ResponseEntity<FundTransferOwnTMBETESuccess> httpValidateTransferResponse = ResponseEntity.ok(eteValidateTransferResponse);
        when(eteTransferValidationClient.getFundTransferValidation(anyString(), any())).thenReturn(httpValidateTransferResponse);
        FundTransferOwnTMBETESuccess actualResponse = eteServiceImp.validateFundTransfer(toAccountNo, fromAccountNo, depositNo, amount, crrDate);
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void validateFundTransfer_WhenServerStatusCodeBlankFromETEShouldThrowTmbCommonExceptionIncorrectToAccountTest() throws JsonProcessingException {
        FeignAdditionalStatus lastAdditionStatus = new FeignAdditionalStatus();
        lastAdditionStatus.setServerStatusCode(BLANK);

        FeignAdditionalStatus ignoreAdditionStatus = new FeignAdditionalStatus();
        ignoreAdditionStatus.setServerStatusCode("IGNORE THIS CODE");

        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(FAILED_CODE);

        FundTransferOwnTMBETESuccess eteValidateTransferResponse = new FundTransferOwnTMBETESuccess();
        eteValidateTransferResponse.setStatus(eteStatus);
        eteValidateTransferResponse.setAdditionalStatus(List.of(ignoreAdditionStatus, lastAdditionStatus));

        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, TMBUtils.convertJavaObjectToString(eteValidateTransferResponse).getBytes(), new HashMap<>());
        when(eteTransferValidationClient.getFundTransferValidation(anyString(), any())).thenThrow(badRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.validateFundTransfer("toAccountNo", "fromAccountNo", "001", new BigDecimal("10.0"), "2022-04-04"));

        Assertions.assertEquals(ResponseCode.INCORRECT_TO_ACCOUNT.getCode(), exception.getErrorCode());
        Assertions.assertEquals(ResponseCode.INCORRECT_TO_ACCOUNT.getMessage(), exception.getErrorMessage());
    }

    @Test
    void validateFundTransfer_WhenETEThrowsCallNotPermittedExceptionShouldThrowsTmbCommonExceptionTest() {
        when(eteTransferValidationClient.getFundTransferValidation(anyString(), any())).thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.validateFundTransfer("toAccountNo", "fromAccountNo", "001", new BigDecimal("10.0"), "2022-04-04"));

        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getMessage(), exception.getErrorMessage());
    }

    @Test
    void getDepositWithdrawalInfo_NormalFlowShouldReturnResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        EteDepositTermResponse eteWithDrawValidateResponse = new EteDepositTermResponse();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        eteWithDrawValidateResponse.setStatus(eteStatus);
        eteWithDrawValidateResponse.setAccount(new EteDepositTermResponse.Account());
        ResponseEntity<EteDepositTermResponse> httpEteWithDrawValidateResponse = ResponseEntity.ok(eteWithDrawValidateResponse);
        when(eteDepositTermClient.getDepositWithdrawalInfo(anyString(), any())).thenReturn(httpEteWithDrawValidateResponse);
        V1OnUsValidateRequest req = new V1OnUsValidateRequest();
        EteDepositTermResponse actualResponse = eteServiceImp.getDepositWithdrawalInfo(req);
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getDepositWithdrawalInfo_GotErrorFromETEShouldReturnCorrectErrorCodeTest() throws JsonProcessingException {
        EteDepositTermResponse eteWithDrawValidateResponse = new EteDepositTermResponse();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(FAILED_CODE);

        EteError ignoreFirstError = new EteError();
        ignoreFirstError.setMessage("ignore first error");

        EteError lastError = new EteError();
        lastError.setCode("1234");
        lastError.setMessage("Focus this error");
        lastError.setCode("abc");

        eteWithDrawValidateResponse.setErrors(List.of(ignoreFirstError, lastError));
        eteWithDrawValidateResponse.setStatus(eteStatus);

        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, TMBUtils.convertJavaObjectToString(eteWithDrawValidateResponse).getBytes(), new HashMap<>());
        when(eteDepositTermClient.getDepositWithdrawalInfo(anyString(), any())).thenThrow(badRequest);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.getDepositWithdrawalInfo(new V1OnUsValidateRequest()));

        String expectedErrorCode = String.format("%s_%s", lastError.getNamespace(), lastError.getCode());
        String exceptedErrorMessage = lastError.getMessage();
        Assertions.assertEquals(expectedErrorCode, exception.getErrorCode());
        Assertions.assertEquals(exceptedErrorMessage, exception.getErrorMessage());
    }

    @Test
    void getDepositAccount_NormalFlowShouldReturnResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        EteDepositAccount eteGetAccountResponse = new EteDepositAccount();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        eteGetAccountResponse.setAccount(new EteTransferAccount());
        eteGetAccountResponse.setStatus(eteStatus);
        ResponseEntity<EteDepositAccount> httpEteGetAccountResponse = ResponseEntity.ok(eteGetAccountResponse);
        when(eteGetAccountClient.getDepositAccount(any(), anyString())).thenReturn(httpEteGetAccountResponse);
        String toAccountNo = "**********";
        String crmId = "123";
        String correlationId = "abc";
        String toAccType = "Type1";

        EteTransferAccount actualResponse = eteServiceImp.getDepositAccount(toAccountNo, crmId, correlationId, toAccType);
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getDepositAccount_GotErrorFromETEShouldReturnCorrectErrorCodeTest() throws JsonProcessingException {
        EteDepositAccount eteGetAccountResponse = new EteDepositAccount();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(FAILED_CODE);

        EteError ignoreEteError = new EteError();
        ignoreEteError.setCode("ignoreThisObject");

        EteError lastEteError = new EteError();
        lastEteError.setCode("007");
        lastEteError.setNamespace("ete");
        lastEteError.setMessage("ete cannot proceed this time, please try again later");

        eteGetAccountResponse.setStatus(eteStatus);
        eteGetAccountResponse.setErrors(List.of(ignoreEteError, lastEteError));

        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, TMBUtils.convertJavaObjectToString(eteGetAccountResponse).getBytes(), new HashMap<>());
        when(eteGetAccountClient.getDepositAccount(any(), anyString())).thenThrow(badRequest);

        String toAccountNo = "**********";
        String crmId = "123";
        String correlationId = "abc";
        String toAccType = "Type1";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.getDepositAccount(toAccountNo, crmId, correlationId, toAccType));

        String expectedErrorCode = String.format("%s_%s", lastEteError.getNamespace(), lastEteError.getCode());
        String expectedErrorMessage = lastEteError.getMessage();
        Assertions.assertEquals(expectedErrorCode, exception.getErrorCode());
        Assertions.assertEquals(expectedErrorMessage, exception.getErrorMessage());
    }

    @Test
    void getDepositAccount_WhenETEGotCallNotPermittedExceptionShouldThrowsTMBCommonExceptionTest() {
        when(eteGetAccountClient.getDepositAccount(any(), anyString())).thenThrow(CallNotPermittedException.class);

        String toAccountNo = "**********";
        String crmId = "123";
        String correlationId = "abc";
        String toAccType = "Type1";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.getDepositAccount(toAccountNo, crmId, correlationId, toAccType));

        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getMessage(), exception.getErrorMessage());
    }

    @Test
    void confirmTransfer_NormalFlowShouldReturnResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        FundTransferOwnTMBETESuccess eteTransferConfirmationResponse = new FundTransferOwnTMBETESuccess();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        eteTransferConfirmationResponse.setStatus(eteStatus);
        eteTransferConfirmationResponse.setData(new TMBDataSuccess());
        ResponseEntity<FundTransferOwnTMBETESuccess> httpEteTransferConfirmResponse = ResponseEntity.ok(eteTransferConfirmationResponse);
        when(eteTransferConfirmationClient
                .confirmTransfer(any(), any())).thenReturn(httpEteTransferConfirmResponse);
        TransferETERequest req = new TransferETERequest();
        TMBDataSuccess actualResponse = eteServiceImp.confirmTransfer(req);
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void eteBasicService_EteServiceDownShouldThrowException() {
        when(eteTransferConfirmationClient
                .confirmTransfer(any(), any())).thenThrow(FeignException.FeignClientException.ServiceUnavailable.class);
        TransferETERequest req = new TransferETERequest();
        assertThrows(TMBCommonException.class, () -> {
            eteServiceImp.confirmTransfer(req);
        });
    }

    @Test
    void eteBasicService_EteServiceReturnNotSuccessShouldThrowException() {
        FundTransferOwnTMBETESuccess eteTransferConfirmationResponse = new FundTransferOwnTMBETESuccess();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode("400");
        eteTransferConfirmationResponse.setStatus(eteStatus);
        eteTransferConfirmationResponse.setData(new TMBDataSuccess());
        ResponseEntity<FundTransferOwnTMBETESuccess> httpEteTransferConfirmResponse = ResponseEntity.ok(eteTransferConfirmationResponse);
        when(eteTransferConfirmationClient
                .confirmTransfer(any(), any())).thenReturn(httpEteTransferConfirmResponse);
        TransferETERequest req = new TransferETERequest();
        assertThrows(TMBCommonException.class, () -> {
            eteServiceImp.confirmTransfer(req);
        });
    }

    @Test
    void eteBasicService_EteServiceReturnNotSuccessWithBadRequestShouldThrowException() throws JsonProcessingException {
        String eteServerStatusCode = "12345";

        FundTransferOwnTMBETESuccess response = new FundTransferOwnTMBETESuccess();
        FeignAdditionalStatus fe = new FeignAdditionalStatus();
        fe.setServerStatusCode(eteServerStatusCode);
        response.setAdditionalStatus(List.of(fe));
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode("400");
        response.setStatus(eteStatus);

        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, TMBUtils.convertJavaObjectToString(response).getBytes(), new HashMap<>());

        when(eteTransferConfirmationClient
                .confirmTransfer(any(), any())).thenThrow(badRequest);
        TransferETERequest req = new TransferETERequest();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            eteServiceImp.confirmTransfer(req);
        });
        String prefix = "cbs_";
        String expectedErrorCode = prefix + eteServerStatusCode;
        assertEquals(expectedErrorCode, exception.getErrorCode());
    }

    @Test
    void eteBasicService_UnknownResponseClassShouldThrowExceptionTest() {
        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, "{\"unknown:\": \"unknown\"}".getBytes(), new HashMap<>());
        when(eteTransferConfirmationClient.confirmTransfer(any(), any())).thenThrow(badRequest);

        Object unknownResponseClass = new Object();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.request(
                () -> eteTransferConfirmationClient.confirmTransfer(new TransferETERequest(), new HttpHeaders())
                , null
                , unknownResponseClass)
        );

        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void validateTransferPromptPay_BBL_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("02");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptPayValidateToBBL(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_KBank_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("04");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptPayValidateToKBANK(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_KTB_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("06");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptPayValidateToKTB(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_SCB_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("14");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptPayValidateToSCB(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_BAY_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("25");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptPayValidateToBAY(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_GSB_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("30");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptPayValidateToGSB(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_Other_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("00");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayValidationFeignClient.transferPromptpayValidationToOther(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.validateTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void validateTransferPromptPay_WhenGotErrorCodeB247048FromETEShouldThrowExceptionPromptPayNotRegisteredTest() throws TMBCommonException, JsonProcessingException {
        String mobileNo = "**********";

        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("00");
        receiver.setProxyValue(mobileNo);
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        tPromptPayVerifyETEResponse.setReceiver(new Receiver());
        tPromptPayVerifyETEResponse.getReceiver().setProxyValue(mobileNo);

        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(FAILED_CODE);
        eteStatus.setAdditionalStatus(new FeignAdditionalStatus());
        eteStatus.getAdditionalStatus().setServerStatusCode("B247048");

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        tPromptPayETEResponse.setStatus(eteStatus);
        //tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, TMBUtils.convertJavaObjectToString(tPromptPayETEResponse).getBytes(), new HashMap<>());
        when(promptpayValidationFeignClient.transferPromptpayValidationToOther(any(), any())).thenThrow(badRequest);

        when(v1TransferServiceHelper.formatMobileOrCitizen(mobileNo)).thenReturn("************");

        TMBCustomCommonExceptionWithResponse exception = assertThrows(TMBCustomCommonExceptionWithResponse.class, () -> eteServiceImp.validateTransferPromptPay(request));

        String errorMessage = "The Promptpay no. ************ is not registered to allow transfering money. Please ask your recipient to register their mobile no./ citizen ID for receieve money with their own bank.";
        Assertions.assertEquals(ResponseCode.PROMPT_PAY_NOT_REGISTERED.getCode(), exception.getErrorCode());
        Assertions.assertEquals(errorMessage, exception.getErrorMessage());
    }

    @Test
    void validateTransferPromptPay_WhenETEGotCallNotPermittedExceptionShouldThrowsTMBCommonExceptionTest() {
        TransferOtherBankETERequest request = new TransferOtherBankETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("00");
        receiver.setProxyValue("**********");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);
        when(promptpayValidationFeignClient.transferPromptpayValidationToOther(any(), any())).thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.validateTransferPromptPay(request));

        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getMessage(), exception.getErrorMessage());
    }

    @Test
    void confirmTransferPromptPay_BBL_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("02");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToBBL(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_KBank_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("04");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToKBANK(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_KTB_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("06");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToKTB(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_SCB_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("14");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToSCB(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_BAY_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("25");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToBAY(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_GSB_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("30");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToGSB(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_Other_success() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("20");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToOther(any(), any())).thenReturn(ResponseEntity.ok(tPromptPayETEResponse));
        TPromptPayVerifyETEResponse response = eteServiceImp.confirmTransferPromptPay(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void confirmTransferPromptPay_Failed() {
        TPromptPayETERequest request = new TPromptPayETERequest();
        Receiver receiver = new Receiver();
        receiver.setBankCode("20");
        request.setReceiver(receiver);
        Sender sender = new Sender();
        sender.setAccountName("accountName");
        request.setSender(sender);

        TPromptPayETEResponse tPromptPayETEResponse = new TPromptPayETEResponse();
        TPromptPayVerifyETEResponse tPromptPayVerifyETEResponse = new TPromptPayVerifyETEResponse();
        tPromptPayVerifyETEResponse.setSender(sender);
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        tPromptPayETEResponse.setStatus(eteStatus);
        tPromptPayETEResponse.setData(tPromptPayVerifyETEResponse);

        String eteErrorCode = "400";
        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, ("{\"status\":{\"code\":\"" + eteErrorCode + "\", \"additionalStatus\":{\"serverStatusCode\":\"B247088\",\"statusDesc\": \"TO ACCOUNT INACTIVE\"}}}").getBytes(), new HashMap<>());
        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToOther(any(), any())).thenThrow(badRequest);
        assertThrows(Exception.class, () -> {
            eteServiceImp.confirmTransferPromptPay(request);
        });
    }

    @Test
    void confirmTransfer_WhenETEGotCallNotPermittedExceptionShouldThrowsTMBCommonExceptionTest() {
        when(eteTransferConfirmationClient.confirmTransfer(any(), any())).thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.confirmTransfer(new TransferETERequest()));

        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getMessage(), exception.getErrorMessage());
    }

    @Test
    void confirmTransferPromptPay_WhenETEGotCallNotPermittedExceptionShouldThrowsTMBCommonExceptionTest() {
        when(promptpayConfirmationFeignClient.transferPromptpayConfirmationToOther(any(), any())).thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            TPromptPayETERequest eteRequest = new TPromptPayETERequest();
            eteRequest.setReceiver(new Receiver());
            eteRequest.getReceiver().setBankCode(TTB_BANK_CODE);

            eteServiceImp.confirmTransferPromptPay(eteRequest);
        });

        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getCode(), exception.getErrorCode());
    }


    @Test
    void getDepositAccountFcd_NormalFlowShouldReturnResponse() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        EteDepositAccount eteGetAccountResponse = new EteDepositAccount();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(STATUS_SUCCESS_CODE);
        eteGetAccountResponse.setAccount(new EteTransferAccount());
        eteGetAccountResponse.setStatus(eteStatus);
        ResponseEntity<EteDepositAccount> httpEteGetAccountResponse = ResponseEntity.ok(eteGetAccountResponse);
        when(eteGetAccountClient.getDepositAccount(any(), anyString())).thenReturn(httpEteGetAccountResponse);
        String toAccountNo = "**********";
        String crmId = "123";
        String correlationId = "abc";
        String toAccType = "Type1";
        String financialId = "****************";

        EteTransferAccount actualResponse = eteServiceImp.getDepositAccount(toAccountNo, crmId, correlationId, toAccType, financialId);
        Assertions.assertNotNull(actualResponse);
    }

    @Test
    void getDepositAccountFcd_GotErrorFromETEShouldReturnCorrectErrorCodeTest() throws JsonProcessingException {
        EteDepositAccount eteGetAccountResponse = new EteDepositAccount();
        EteStatus eteStatus = new EteStatus();
        eteStatus.setCode(FAILED_CODE);

        EteError ignoreEteError = new EteError();
        ignoreEteError.setCode("ignoreThisObject");

        EteError lastEteError = new EteError();
        lastEteError.setCode("007");
        lastEteError.setNamespace("ete");
        lastEteError.setMessage("ete cannot proceed this time, please try again later");

        eteGetAccountResponse.setStatus(eteStatus);
        eteGetAccountResponse.setErrors(List.of(ignoreEteError, lastEteError));

        Request feignRequest = Request.create(Request.HttpMethod.POST, "", new HashMap<>(), null, null, null);
        FeignException.BadRequest badRequest = new FeignException.BadRequest("failed", feignRequest, TMBUtils.convertJavaObjectToString(eteGetAccountResponse).getBytes(), new HashMap<>());
        when(eteGetAccountClient.getDepositAccount(any(), anyString())).thenThrow(badRequest);

        String toAccountNo = "**********";
        String crmId = "123";
        String correlationId = "abc";
        String toAccType = "Type1";
        String financialId = "****************";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.getDepositAccount(toAccountNo, crmId, correlationId, toAccType, financialId));

        String expectedErrorCode = String.format("%s_%s", lastEteError.getNamespace(), lastEteError.getCode());
        String expectedErrorMessage = lastEteError.getMessage();
        Assertions.assertEquals(expectedErrorCode, exception.getErrorCode());
        Assertions.assertEquals(expectedErrorMessage, exception.getErrorMessage());
    }

    @Test
    void getDepositAccountFcd_WhenETEGotCallNotPermittedExceptionShouldThrowsTMBCommonExceptionTest() {
        when(eteGetAccountClient.getDepositAccount(any(), anyString())).thenThrow(CallNotPermittedException.class);

        String toAccountNo = "**********";
        String crmId = "123";
        String correlationId = "abc";
        String toAccType = "Type1";
        String financialId = "****************";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eteServiceImp.getDepositAccount(toAccountNo, crmId, correlationId, toAccType, financialId));

        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(ResponseCode.CIRCUIT_BREAKER_ERROR.getMessage(), exception.getErrorMessage());
    }

}
package com.tmb.oneapp.transferservice.feature.customeraccountbiz.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.client.CustomerAccountBizClient;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.AccountSaving;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CustomerAccountBizServiceTest {
    @InjectMocks
    CustomerAccountBizService customerAccountBizService;
    @Mock
    CustomerAccountBizClient customerAccountBizClient;

    String crmId;
    String correlationId;
    @BeforeEach
    void setup() {
        crmId = "0001010203021301230";
        correlationId = "*****************";
    }

    @Test
    void testGetAccountListWhenExistedShouldReturnAccountSaving() throws TMBCommonException {

        AccountSaving accountSaving = new AccountSaving();
        List<DepositAccount> depositAccounts = new ArrayList<>();

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountName("TEST");

        depositAccounts.add(depositAccount);

        accountSaving.setDepositAccountLists(depositAccounts);

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService());
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(accountSaving);

        when(customerAccountBizClient.getAccountList(anyString(), anyString(), anyBoolean(), anyBoolean())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        AccountSaving actual = customerAccountBizService.getAccountList(correlationId, crmId, false, false);

        assertNotNull(actual);
    }
    @Test
    void testGetAccountListWhenErrorShouldThrowsTmbCommonException() throws TMBCommonException {

        when(customerAccountBizClient.getAccountList(anyString(), anyString(), anyBoolean(), anyBoolean())).thenThrow(FeignException.FeignClientException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> customerAccountBizService.getAccountList(correlationId, crmId, false, false));

        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }
}

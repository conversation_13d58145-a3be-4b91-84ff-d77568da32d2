oneapp.ocp.domain=dev3-oneapp.svc
spring.application.name=transfer-service
spring.application.description=Transfer-Service

server.port=8080

spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER

springdoc.api-docs.path=/oneapp/transfer-service/api-docs
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.swagger-ui.use-root-path=false
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.urls[0].name=transfer-service
springdoc.swagger-ui.urls[0].url=/oneapp/transfer-service/api-docs
swagger.host=https://apis-portal.oneapp.tmbbank.local,http://localhost:${server.port}

log.feign.enable.all.api=true
feign.metrics.enabled=true

#Disable auto mongo db config to allow multiple mongo databases
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration, \
  org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration

#Loging
#logging.config=classpath:logback.xml
logging.level.com.tmb.commonservice.feign.*=DEBUG

# Hikari Connection Pool configuration
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.idle-timeout=60000
spring.datasource.hikari.max-lifetime=180000
spring.datasource.hikari.minimum-idle=3
spring.datasource.hikari.maximum-pool-size=3
spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG

#Mongo db details
jasypt.encryptor.password=RTHQB4FG92

spring.data.mongodb.uri=mongodb://***********:27017/transfer?authsource=transfer
#spring.data.mongodb.uri=mongodb://127.0.0.1:27019/financial?authsource=transfer
spring.data.mongodb.username=transferusr
spring.data.mongodb.password=ENC(v6UQmoYOOVvT7FnBZSqGrA1vCJBKReTt)
spring.data.mongodb.dbname=transfer
#**************************************************************************************************************************************************
#spring.data.mongodb.uri=mongodb://***********:27017/transfer?authsource=transfer
#spring.data.mongodb.username=transferusr
#spring.data.mongodb.password=DeV1_Tran23
#spring.data.mongodb.dbname=transfer

#OneApp datasource
#spring.datasource.url=*****************************************
#spring.datasource.username=custcrmusr
#spring.datasource.password=CrmPwV1t_2022
#spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
#spring.jpa.show-sql=true
#spring.jpa.database=oracle
#spring.jpa.properties.hibernate.format_sql=true

#CRM Datasource
crm.datasource.url=*****************************************
crm.datasource.username=CRMADM
crm.datasource.password=ENC(5mgdqSSyNt2Cl95tnt/O8u2717GOmF5T)
crm.datasource.driver-class-name=oracle.jdbc.OracleDriver

#Redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.mode=standalone

#Redis Library Feature Configuration
redis.cache.ttl.default=-1
redis.cache.enabled=true
redis.cache.custom.ttl=true
redis.template.enabled=true
redis.prefix.environment.name=${envconfig.oneapp.redis.prefix-default}
redis.prefix.cache-name.enabled=false
redis.cache.ttl.transfer_module_config=86400

#interceptor
feign.client.config.default.requestInterceptors=com.tmb.common.interceptor.FeignRequestInterceptor

#Cache: ttl unit: seconds
cache.transfermodule.ttl=2592000

utility.common.service.endpoint=common-service-https-internal-oneapp-dev.apps.ddid1.tmbcps.com

management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=health,info,prometheus

notification-service.e-noti.default.channel.th=\u0E17\u0E35\u0E17\u0E35\u0E1A\u0E35 \u0E17\u0E31\u0E0A
notification-service.e-noti.default.channel.en=ttb touch

#Account condition

amlo.amount.validate=700000

customer-service.url=https://apis-portal.oneapp.tmbbank.local
bank-service.url=https://apis-portal.oneapp.tmbbank.local
notification-service.url=https://apis-portal.oneapp.tmbbank.local

#promptpay
ete.domain=stmetead1.tmbbank.local
ete.promptpay.validate.url=https://${ete.domain}:15146
ete.promptpay.validate.path=/v1.0/credittransfer/promptpay/validation

ete.promptpay.confirmation.url=https://${ete.domain}:15148
ete.promptpay.confirmation.path=/v1.0/credittransfer/promptpay/confirmation



#Kafka
spring.kafka.jaas.options.username=appusr
spring.kafka.jaas.options.password=P@ssw0rd@1
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092
spring.kafka.consumer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092

#Acitivity Log
kafka.prefix.topic=
oneapp.customer.activity-log.topic-name=${kafka.prefix.topic}activity
oneapp.customer.financial-log.topic-name=${kafka.prefix.topic}financial_log
oneapp.customer.transaction-log.topic-name=${kafka.prefix.topic}transaction_log
oneapp.customer.exchange-transaction-log-name=${kafka.prefix.topic}exchange_transaction_log

ete.transfer.validation.url=https://${ete.domain}:15408
ete.deposit.term.url=https://${ete.domain}:15788
ete.deposit.url=https://${ete.domain}:15784

common-service.name=common-service
common-service.url=http://common-service.${oneapp.ocp.domain}
customers-exp-service.url=http://customers-exp-service.${oneapp.ocp.domain}


#AccountService
account-service.name=account-service
account-service.endpoint=http://accounts-service.${oneapp.ocp.domain}

#CacheConfig
cache.endpoint=http://cache-service-http-internal-dev1-oneapp.apps.ddid1.tmbcps.com
cache.name=cache-service

#Customer account biz
feign.customers.account.biz.service.name=customer-account-biz
feign.customers.account.biz.service.url=http://customer-account-biz.${oneapp.ocp.domain}

#Customers Transaction Service
feign.customers.transaction.service.name=customers-transaction-service
feign.customers.transaction.service.url=http://customers-transaction-service.${oneapp.ocp.domain}

#DDP service // please change this when implement the feature
feign.ddp.service.name=ddp-service
feign.ddp.service.url=http://ddp-service.${oneapp.ocp.domain}

#ods-interest-rate-service
ods-interest-rate-service-url=https://**************:19200

#ECM
ecm-username=fncmismibsit
ecm-password=P@ss12345
ecm-repo-id=TMB_SIT
ecm-host-port=https://cmis-sit-ecm.apps.ecm-bankd1.tmbcps.com:443/openfncmis_wlp/services11?wsdl

#FR Validate logic
validate.fr.require.app.version=4.3.0
validate.fr.financial.accu.amount=200000
validate.fr.transfer.trans.limit=50000
validate.fr.topup.flag=true
validate.fr.transfer.flag=true

#Common-authen Validate logic
common.authen.require.app.version=5.12.0

#Validate TD backward version
td.require.app.version=6.0.0

#circuit breaker by bank_cd
bank.cd-bbl=02
bank.cd-kbank=04
bank.cd-ktb=06
bank.cd-scb=14
bank.cd-bay=25
bank.cd-gsb=30

############### Circuit Breaker Resilience4j ###########################################################################
circuitbreaker.enabled=false

#default
resilience4j.circuitbreaker.instances.defaultCircuitBreaker.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.sliding-window-type=time_based
resilience4j.circuitbreaker.configs.defaultCircuitBreaker.sliding-window-size=60

#/v1.0/internal/fund-transfer/validation
resilience4j.circuitbreaker.instances.fundTransferValidation.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.fundTransferValidation.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.FundTransferValidationPredicate
circuitbreaker.instances.fundTransferValidation.enabled=true

#/v3.0/internal/deposit/get-account
resilience4j.circuitbreaker.instances.depositGetAccount.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.depositGetAccount.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.DepositGetAccountPredicate
circuitbreaker.instances.depositGetAccount.enabled=true

#/v1.0/internal/fund-transfer/confirmation
resilience4j.circuitbreaker.instances.fundTransferConfirmation.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.fundTransferConfirmation.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.FundTransferConfirmationPredicate
circuitbreaker.instances.fundTransferConfirmation.enabled=true

#validation bank_cd=Other
resilience4j.circuitbreaker.instances.transferPromptpayValidationToOther.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToOther.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToOther.enabled=true

#validation bank_cd=BBL
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBBL.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBBL.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToBBL.enabled=true

#validation bank_cd=KBANK
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKBANK.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKBANK.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToKBANK.enabled=true

#validation bank_cd=KTB
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKTB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToKTB.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToKTB.enabled=true

#validation bank_cd=SCB
resilience4j.circuitbreaker.instances.transferPromptpayValidationToSCB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToSCB.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToSCB.enabled=true

#validation bank_cd=BAY
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBAY.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToBAY.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToBAY.enabled=true

#validation bank_cd=GSB
resilience4j.circuitbreaker.instances.transferPromptpayValidationToGSB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayValidationToGSB.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayValidationPredicate
circuitbreaker.instances.transferPromptpayValidationToGSB.enabled=true

#confirmation bank_cd=Other
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToOther.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToOther.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToOther.enabled=true

#confirmation bank_cd=BBL
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToBBL.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToBBL.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToBBL.enabled=true

#confirmation bank_cd=KBANK
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToKBANK.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToKBANK.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToKBANK.enabled=true

#confirmation bank_cd=KTB
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToKTB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToKTB.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToKTB.enabled=true

#confirmation bank_cd=SCB
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToSCB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToSCB.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToSCB.enabled=true

#confirmation bank_cd=BAY
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToBAY.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToBAY.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToBAY.enabled=true

#confirmation bank_cd=GSB
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToGSB.base-config=defaultCircuitBreaker
resilience4j.circuitbreaker.instances.transferPromptpayConfirmationToGSB.record-failure-predicate=com.tmb.oneapp.transferservice.predicate.TransferPromptpayConfirmationPredicate
circuitbreaker.instances.transferPromptpayConfirmationToGSB.enabled=true

#circuit breaker instances list
circuitbreaker.instances.list=defaultCircuitBreaker,fundTransferValidation,depositGetAccount,fundTransferConfirmation,transferPromptpayValidationToOther,\
  transferPromptpayValidationToBBL,transferPromptpayValidationToKBANK,transferPromptpayValidationToKTB,transferPromptpayValidationToSCB,transferPromptpayValidationToBAY,transferPromptpayValidationToGSB,\
  transferPromptpayConfirmationToOther,transferPromptpayConfirmationToBBL,transferPromptpayConfirmationToKBANK,transferPromptpayConfirmationToKTB,transferPromptpayConfirmationToSCB,transferPromptpayConfirmationToBAY,transferPromptpayConfirmationToGSB

#oauth service
oauth.name=oneapp-auth-service
oauth.endpoint=http://${oauth.name}.${oneapp.ocp.domain}

#Add Inbound/OutBound Log
app.api.logging.enable=true
app.api.logging.max-length=10000
app.api.logging.url-patterns=*
app.api.logging.feign.enable=true
app.api.logging.feign.exclude-url-patterns=

#RegexforFormattingMasking
mobile_format_regex=(.{3})(.{3})(.{4})$
mobile_format_value=$1-$2-$3
citizen_format_regex=(.{1})(.{4})(.{5})(.{2})(.{1})$
citizen_format_value=$1-$2-$3-$4-$5
account_mask_regex=(.{3})(.{1})(.{5})(.{1})$
account_mask_value=xxx-x-$3-x
mobile_mask_regex=(.{3})(.{3})(.{4})$
mobile_mask_value=$1-xxx-$3
citizen_mask_regex=(.{9})(.{1})(.{2})(.{1})$
citizen_mask_value=x-xxxx-xxxx$2-$3-$4

# Async
app.async.enabled=true
thread.core-pool-size=5
thread.max-pool-size=20
thread.keep-alive-seconds=60
thread.queue-capacity=500
thread.name-prefix=AsyncTask-
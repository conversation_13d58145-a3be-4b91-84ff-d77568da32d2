server.port=8080
jasypt.encryptor.password=RTHQB4FG92

#Loging
logging.config=classpath:logback.xml
logging.level.com.tmb.commonservice.feign.*=DEBUG

#Mongo db details
spring.data.mongodb.uri=mongodb://***********:27017/transfer?authsource=transfer
spring.data.mongodb.username=
spring.data.mongodb.password=
spring.data.mongodb.dbname=

#OCP Internal domain
ete.domain=ete.tmbbank.local

spring.jpa.properties.hibernate.format_sql=true
spring.api.description = bank service

#CategoryEvent

#Redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
redis.ttl.accesstoken=300
redis.ttl.refreshtoken=300
redis.ttl.touch.accesstoken=1800
spring.redis.read-from=replicaPreferred
spring.redis.adaptive-refresh-trigger-timeout=15
spring.redis.periodic-refresh=15


feign.httpclient.enabled=true

#Kafka
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092
topicName=activity

oneapp.ocp.domain=dev3-oneapp.svc
bank-service.url=http://bank-service.${oneapp.ocp.domain}
customer-service.url=http://customers-service.${oneapp.ocp.domain}
notification-service.url=http://notification-service.${oneapp.ocp.domain}
common-service.name=common-service
common-service.url=http://common-service.${oneapp.ocp.domain}
customers-exp-service.url=http://customers-exp-service.${oneapp.ocp.domain}

#CacheConfig
cache.endpoint=http://cache-service.${oneapp.ocp.domain}
cache.name=cache-service

#ods-interest-rate-service
ods-interest-rate-service-url=https://${ete.domain}:19200

#FR Validate logic
validate.fr.require.app.version=4.3.0

#oauth service
oauth.name=oneapp-auth-service
oauth.endpoint=http://${oauth.name}.${oneapp.ocp.domain}

#RegexforFormattingMasking
mobile_format_regex=(.{3})(.{3})(.{4})$
mobile_format_value=$1-$2-$3
citizen_format_regex=(.{1})(.{4})(.{5})(.{2})(.{1})$
citizen_format_value=$1-$2-$3-$4-$5
account_mask_regex=(.{3})(.{1})(.{5})(.{1})$
account_mask_value=xxx-x-$3-x
mobile_mask_regex=(.{3})(.{3})(.{4})$
mobile_mask_value=$1-xxx-$3
citizen_mask_regex=(.{9})(.{1})(.{2})(.{1})$
citizen_mask_value=x-xxxx-xxxx$2-$3-$4
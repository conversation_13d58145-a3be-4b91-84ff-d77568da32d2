package com.tmb.oneapp.transferservice.feature.customeraccountbiz.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.client.CustomerAccountBizClient;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.AccountSaving;
import com.tmb.oneapp.transferservice.utils.TransferUtils;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CustomerAccountBizService {
    private static final TMBLogger<CustomerAccountBizService> logger = new TMBLogger<>(CustomerAccountBizService.class);

    private final CustomerAccountBizClient customerAccountBizClient;

    @LogAround
    public AccountSaving getAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag) throws TMBCommonException {
        AccountSaving accountSaving;
        try {
            accountSaving = Optional.ofNullable(customerAccountBizClient.getAccountList(correlationId, crmId, refreshFlag, allAccountFlag))
                    .map(HttpEntity::getBody)
                    .map(TmbOneServiceResponse::getData)
                    .orElse(new AccountSaving());
        } catch (FeignException.FeignClientException e) {
            throw TransferUtils.failException(ResponseCode.FAILED);
        }
        return accountSaving;
    }
}

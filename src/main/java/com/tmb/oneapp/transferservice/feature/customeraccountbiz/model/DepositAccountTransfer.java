package com.tmb.oneapp.transferservice.feature.customeraccountbiz.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DepositAccountTransfer extends DepositAccount {
    private String allowTransferOwnTtbDda;
    private String allowTransferOwnTtbSda;
    private String allowTransferOwnTtbCda;
    private String allowTransferOtherTtbDda;
    private String allowTransferOtherTtbSda;
    private String allowTransferOtherTtbCda;
}

package com.tmb.oneapp.transferservice.feature.ete.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "ete-transfer-validation-client", url = "${ete.transfer.validation.url}",configuration = FeignCommonConfig.class)
public interface EteTransferValidationClient {

    @CircuitBreaker(name = "fundTransferValidation")
    @PostMapping(value = "/v1.0/internal/fund-transfer/validation", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<FundTransferOwnTMBETESuccess> getFundTransferValidation(@RequestBody String reqTemplate,
                                                                           @RequestHeader Map<String, String> headers);

    @CircuitBreaker(name = "fundTransferValidation")
    @PostMapping(value = "/v3.0/internal/fund-transfer/validation", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<FundTransferOwnTMBETESuccess> getFundTransferValidationV3(@RequestBody String reqTemplate,
                                                                           @RequestHeader Map<String, String> headers);

}

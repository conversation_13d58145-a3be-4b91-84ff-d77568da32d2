package com.tmb.oneapp.transferservice.feature.financiallog.model;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import org.apache.commons.lang3.StringUtils;

import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BLANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CLEARING_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.D00;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FAILURE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PAYMENT_QR_PROMPT_PAY;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_MOBILE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_TRANSFER_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SMART_FLAG_TRANSFER_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.THAI_QR_FIN_FLEX_VALUES1;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TXN_TYPE_TRANSFER;

public class FinRequestOtherBankLog extends FinRequest{
    public FinRequestOtherBankLog(){

    }

    public FinRequestOtherBankLog(TPromptPayVerifyETEResponse dataCache,
                                  String crmId,
                                  String activityTypeId,
                                  String refId,
                                  String correlationId,
                                  String activityFinAndTransId,
                                  String transactionDateTime){
        setUpFields(dataCache,crmId,activityTypeId,refId,correlationId,activityFinAndTransId,transactionDateTime);
    }

    private void setUpFields(TPromptPayVerifyETEResponse dataCache, String crmId, String activityTypeId, String refId, String correlationId, String activityFinAndTransId, String transactionDateTime){
        setCrmId(crmId);
        setActivityTypeIdNew(activityTypeId);
        setActivityId(activityFinAndTransId);
        setReferenceID(refId);
        setActivityRefId(correlationId);
        setTxnDt(transactionDateTime);

        setChannelId(MB);
        setClearingStatus(CLEARING_STATUS);
        setTxnType(TXN_TYPE_TRANSFER);

        setFromAccNo(dataCache.getSender().getAccountId());
        setFromAccType(dataCache.getSender().getAccountType());
        setFromAccNickName(dataCache.getPaymentCacheData().getFromAccountNickname());
        setFromAccName(dataCache.getSender().getAccountName());

        setToAccNo(dataCache.getReceiver().getAccountId());

        setToAccName(dataCache.getReceiver().getAccountDisplayName());
        setToAccType(getToAccountTypeByActivityTypeId(dataCache.getReceiver().getAccountType(), activityTypeId));
        setToAccNickName(dataCache.getPaymentCacheData().getToFavoriteNickname());
        setProxyValue(dataCache.getReceiver().getProxyValue());
        setProxyId(dataCache.getReceiver().getProxyType());
        setSmartFlag(getSmartFlagByProxyType(dataCache.getReceiver().getProxyType()));

        String fee = dataCache.getFee() == null ? "0.00" : dataCache.getFee().toString();
        setTxnFee(fee);
        setMemo(dataCache.getPaymentCacheData().getNote());
        setTxnAmount(String.valueOf(dataCache.getAmount()));
        setReferenceID(dataCache.getTransactionReference());
        setBankCode(dataCache.getPaymentCacheData().getBankCode());
        setCategoryId(dataCache.getPaymentCacheData().getCategoryId());
        setFinFlexValues1((StringUtils.equals(dataCache.getPaymentCacheData().getQr(), PAYMENT_QR_PROMPT_PAY)) ? THAI_QR_FIN_FLEX_VALUES1 : null);
        setFinLinkageId(dataCache.getTerminal().getId());
        setTxnStatus(SUCCESS);
    }

    private String getToAccountTypeByActivityTypeId(String toAccountType, String activityTypeId) {
        switch (activityTypeId) {
            case ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID:
            case ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID:
            case ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID:
            case ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID:
            case ACTIVITY_LOG_CONFIRM_OTHER_BANK_ACTIVITY_ID:
                return D00;
            default:
                return toAccountType;
        }
    }

    private String getSmartFlagByProxyType(String toProxyType) {
        switch (toProxyType) {
            case PROXY_TYPE_TRANSFER_OTHER_BANK:
                return SMART_FLAG_TRANSFER_OTHER_BANK;
            case PROXY_TYPE_PROMPTPAY_BY_CITIZEN:
            case PROXY_TYPE_PROMPTPAY_BY_MOBILE:
            default:
                return BLANK;
        }
    }

}

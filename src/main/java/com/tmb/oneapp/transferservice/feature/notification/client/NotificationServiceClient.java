package com.tmb.oneapp.transferservice.feature.notification.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.common.model.response.notification.NotificationResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "notification-service", url = "${notification-service.url}",configuration = FeignCommonConfig.class)
public interface NotificationServiceClient {

    @PostMapping(value = "/apis/notification/e-noti/sendmessage")
    ResponseEntity<TmbOneServiceResponse<NotificationResponse>> sendMessage(
            @RequestHeader(value = TransferServiceConstant.HEADER_CORRELATION_ID) final String xCorrelationId,
            NotificationRequest request);
}


package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Setter
@Getter
@ToString
@JsonPropertyOrder({
    "accountId",
    "accountType",
    "accountName",
    "accountLength",
    "taxId"
})
public class Sender {
    @JsonProperty("accountId")
    private String accountId;
    @JsonProperty("accountType")
    private String accountType;
    @JsonProperty("accountName")
    private String accountName;
    @JsonProperty("accountLength")
    private Integer accountLength;
    @JsonProperty("taxId")
    private String taxId;
    @JsonProperty("bankCode")
    private String bankCode;
    private String bankName;
    private String customerTypeFlag;
    private String customerId;
}

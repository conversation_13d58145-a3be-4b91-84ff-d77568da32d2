package com.tmb.oneapp.transferservice.feature.activitylog.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.BaseEvent;
import com.tmb.common.util.TMBUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class V1ActivityLogService {
    private static final TMBLogger<V1ActivityLogService> logger = new TMBLogger<>(V1ActivityLogService.class);

    @Value("${oneapp.customer.activity-log.topic-name}")

    private String topicName;
    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Async
    @LogAround
    public void logActivity(BaseEvent activityLog) {
        try {
            String output = TMBUtils.convertJavaObjectToString(activityLog);
            kafkaProducerService.sendMessageAsync(topicName, output);
        } catch (Exception e) {
            logger.info("logActivity got exception:", e);
        }
    }
}

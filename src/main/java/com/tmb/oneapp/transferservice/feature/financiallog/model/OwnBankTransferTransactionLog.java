package com.tmb.oneapp.transferservice.feature.financiallog.model;

import com.tmb.oneapp.transferservice.model.transfer.TransferActivities;
import com.tmb.oneapp.transferservice.model.transfer.TransferConfirmCacheETE;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CONSTANTTWO;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.REFERENCE_ACTIVITY_ID_VALUE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS;

public class OwnBankTransferTransactionLog extends TransferActivities {
    public OwnBankTransferTransactionLog(String crmId, String refId, V1TransferData transferCache, String activityFinAndTransId, String transactionDateTime) {
        setUpFields(crmId, refId,transferCache, activityFinAndTransId, transactionDateTime);
    }

    private void setUpFields(String crmId, String refId, V1TransferData transferCache, String activityFinAndTransId, String transactionDateTime) {
        setCrmId(crmId);
        setFinancialTransferRefId(refId);
        setActivityId(activityFinAndTransId);
        setTransactionDate(transactionDateTime);

        setFromAccountNo(transferCache.getFromAccount().getAccountNo());
        setFromAccountNickname(transferCache.getFromAccountNickname());

        setToAccountNo(transferCache.getToAccount().getAccountNo());
        setToAccountName(transferCache.getToAccountName());
        setToAccountNickname(transferCache.getToAccountNickname());

        setFinancialTransferAmount(transferCache.getAmount().toString());
        setFinancialTransferMemo(transferCache.getMemo());

        setReferenceActivityTypeId(REFERENCE_ACTIVITY_ID_VALUE);
        setChannelId(MB);
        setTransactionStatus(SUCCESS);
        setFinancialTransferCRDR(CONSTANTTWO);

        setProxyType("");
        setProxyValue("");
        setBillerCompCode("");
        setBillerRef1("");
        setBillerRef2("");
        setBillerNameEn("");
        setBillerNameTh("");
        setLabelRef1En("");
        setLabelRef1Th("");
        setLabelRef2En("");
        setLabelRef2Th("");
    }
}

package com.tmb.oneapp.transferservice.feature.authen.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@SuperBuilder
public class CommonAuthenWithPayloadRequest extends CommonAuthenVerifyRefRequest {
    private String amount;
    private String dailyAmount;
    private String bankCode;
    private String featureId;
}

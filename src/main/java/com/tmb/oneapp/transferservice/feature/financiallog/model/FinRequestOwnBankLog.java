package com.tmb.oneapp.transferservice.feature.financiallog.model;

import com.tmb.oneapp.transferservice.model.transfer.V1TermDeposit;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import com.tmb.oneapp.transferservice.utils.DateUtils;

import java.text.ParseException;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CLEARING_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SMART_FLAG_TRANSFER_TTB_OR_PROMPT_PAY;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.STATUS_SUCCESS_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TXN_TYPE_TRANSFER;

public class FinRequestOwnBankLog extends FinRequest{



    public FinRequestOwnBankLog(String correlationId, String crmId, String refId, V1TransferData transferCache, String activityFinAndTransId, String activityTypeId, String transactionDateTime)
            throws ParseException {
        setUpFields(correlationId,crmId,refId,transferCache,activityFinAndTransId,activityTypeId,transactionDateTime);
    }


    private void setUpFields(String correlationId, String crmId, String refId, V1TransferData transferCache, String activityFinAndTransId, String activityTypeId, String transactionDateTime) throws ParseException {
        setCrmId(crmId);
        setReferenceID(refId);
        setActivityId(activityFinAndTransId);
        setActivityTypeIdNew(activityTypeId);
        setActivityRefId(correlationId);
        setTxnDt(transactionDateTime);

        setFromAccNickName(transferCache.getFromAccountNickname());
        setFromAccNo(transferCache.getFromAccount().getAccountNo());
        setFromAccType(transferCache.getFromAccount().getAccountType());
        setFromAccName(transferCache.getFromAccountName());

        if (ACCOUNT_TYPE_TERM_DEPOSIT.equals(transferCache.getFromAccount().getAccountType()) && transferCache.getTermDeposit() != null) {
            V1TermDeposit termDepositCache = transferCache.getTermDeposit();
            setTdInterestAmount(termDepositCache.getTdInterestAmount());
            setTdTaxAmount(termDepositCache.getTdTaxAmount());
            setTdPenaltyAmount(termDepositCache.getPenaltyAmount());
            setTdNetAmount(termDepositCache.getTdNetAmount());

            String tdMaturityDate = (termDepositCache.getTdMaturityDate() != null) ? DateUtils.convertHumanDateToEpoch(termDepositCache.getTdMaturityDate()) : null;
            setTdMaturityDate(tdMaturityDate);
        }

        setToAccNo(transferCache.getToAccount().getAccountNo());
        setToAccName(transferCache.getToAccountName());
        setToAccType(transferCache.getToAccount().getAccountType());
        setToAccNickName(transferCache.getToAccountNickname());

        setErrorCd(STATUS_SUCCESS_CODE);
        setTxnStatus(SUCCESS);
        setChannelId(MB);
        setSmartFlag(SMART_FLAG_TRANSFER_TTB_OR_PROMPT_PAY);
        setClearingStatus(CLEARING_STATUS);
        setTxnType(TXN_TYPE_TRANSFER);
        setBankCode(TTB_BANK_CODE);

        setCategoryId(transferCache.getCategoryId());
        setMemo(transferCache.getMemo());
        setTxnAmount(transferCache.getAmount().toString());
        setTxnFee(transferCache.getFeeFromETE().toString());
    }

}

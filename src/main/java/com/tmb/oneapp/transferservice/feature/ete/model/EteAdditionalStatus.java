package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "status_code",
        "server_status_code",
        "severity",
        "status_desc"
})
public class EteAdditionalStatus {

    @JsonProperty("status_code")
    @JsonAlias("code")
    private String statusCode;
    @JsonProperty("server_status_code")
    @JsonAlias("server_code")
    private String serverStatusCode;
    @JsonProperty("severity")
    private String severity;
    @JsonProperty("status_desc")
    @JsonAlias("description")
    private String statusDesc;

}

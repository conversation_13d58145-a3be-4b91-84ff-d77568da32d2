package com.tmb.oneapp.transferservice.feature.account.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.account.model.TDRequest;
import com.tmb.oneapp.transferservice.feature.account.model.TDResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

@FeignClient(name = "${account-service.name}", url = "${account-service.endpoint}", configuration = FeignCommonConfig.class)
public interface AccountsServiceClient {

    @PostMapping(value = "/apis/accounts/details", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    ResponseEntity<TmbOneServiceResponse<TDResponse>> fetchTermDepositDetail(
            @RequestHeader(TransferServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
            @RequestBody TDRequest body);
}

package com.tmb.oneapp.transferservice.feature.cache.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

@Service
public class RedisCacheService implements CacheService {
    private static final TMBLogger<RedisCacheService> logger = new TMBLogger<>(RedisCacheService.class);
    private final String START_SET_CACHE_MESSAGE_TEMPLATE = "Start Set key: {} , value: {} to Redis";
    private final String SUCCESS_SET_CACHE_MESSAGE_TEMPLATE = "Success Set key: {} , value: {} to Redis";
    @Autowired
    private RedisTemplate<String, String>  redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void set(String key, Object value, int ttlSecond) throws JsonProcessingException {
        try {
            logger.info(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, TMBUtils.convertJavaObjectToString(value), Duration.ofSeconds(ttlSecond));
            logger.info(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("exception while setting data to Redis: ", e);
            throw e;
        }
    }

    @Override
    public void set(String key, Object value) throws JsonProcessingException {
        try {
            logger.info(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, TMBUtils.convertJavaObjectToString(value));
            logger.info(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("exception while setting data to Redis: ", e);
            throw e;
        }
    }

    @Override
    public <T> Object get(String key, Class<T> typeValue) {
        try {
            String rawData = (String) redisTemplate.opsForValue().get(key);
            return TMBUtils.convertStringToJavaObj(rawData, typeValue);
        } catch (JsonProcessingException e) {
            logger.warn("cannot parsing json from Redis: ", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            logger.warn("exception while fetching data from Redis: ", e);
            throw e;
        }
    }

    @Override
    public boolean delete(String key) {
        try {
            logger.info("Start delete key: {}", key);
            return redisTemplate.delete(key);
        } catch (Exception e) {
            logger.warn("exception while deleting to Redis: ", e);
            throw e;
        }
    }
}

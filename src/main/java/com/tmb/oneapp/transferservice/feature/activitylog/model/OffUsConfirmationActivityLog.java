package com.tmb.oneapp.transferservice.feature.activitylog.model;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BLANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PAYMENT_QR_PROMPT_PAY;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_MOBILE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_TRANSFER_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;

public class OffUsConfirmationActivityLog extends TransferActivityLog {

    protected OffUsConfirmationActivityLog(HttpHeaders headers, String correlationId) {
        super(headers, correlationId);
    }

    protected OffUsConfirmationActivityLog(HttpHeaders headers) {
        super(headers);
    }

    private String getActivityTypeId(String toProxyType, String toBankCode, String qrType) {
        String activityTypeId = BLANK;
        boolean transferToTTBAcct = StringUtils.equals(toBankCode, TTB_BANK_CODE);

        switch (toProxyType) {
            case PROXY_TYPE_TRANSFER_OTHER_BANK:
                activityTypeId = (transferToTTBAcct) ? ACTIVITY_LOG_CONFIRM_OTHER_TTB_ACCOUNT_ACTIVITY_ID : ACTIVITY_LOG_CONFIRM_OTHER_BANK_ACTIVITY_ID;
                break;
            case PROXY_TYPE_PROMPTPAY_BY_MOBILE:
                activityTypeId = getMobileTransferConfirmActivityId(transferToTTBAcct, qrType);
                break;
            case PROXY_TYPE_PROMPTPAY_BY_CITIZEN:
                activityTypeId = getCitizenTransferConfirmActivityId(transferToTTBAcct, qrType);
                break;
            default:
                break;
        }
        return activityTypeId;
    }

    private String getMobileTransferConfirmActivityId(boolean transferToTTB, String qrType) {
        if (transferToTTB) {
            if (StringUtils.equals(qrType, PAYMENT_QR_PROMPT_PAY)) {
                return ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
            }
            return ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
        }

        if (StringUtils.equals(qrType, PAYMENT_QR_PROMPT_PAY)) {
            return ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
        }

        return ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
    }

    private String getCitizenTransferConfirmActivityId(boolean transferToTTB, String qrType) {
        if (transferToTTB) {
            if (StringUtils.equals(qrType, PAYMENT_QR_PROMPT_PAY)) {
                return ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
            }
            return ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
        }

        if (StringUtils.equals(qrType, PAYMENT_QR_PROMPT_PAY)) {
            return ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
        }

        return ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
    }

    public void setFieldByProxyType(String toAccountNo, String toProxyType) {
        switch (toProxyType) {
            case PROXY_TYPE_TRANSFER_OTHER_BANK:
                setToAccount(toAccountNo);
                break;
            case PROXY_TYPE_PROMPTPAY_BY_MOBILE:
                setToMobile(toAccountNo);
                break;
            case PROXY_TYPE_PROMPTPAY_BY_CITIZEN:
                setToCitizenId(toAccountNo);
                break;
            default:
                break;
        }
    }

    public void setActivityTypeId(String proxyType, String bankCode, String qr) {
        setActivityTypeId(getActivityTypeId(proxyType, bankCode, qr));
    }
}

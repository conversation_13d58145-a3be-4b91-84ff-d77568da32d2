package com.tmb.oneapp.transferservice.feature.authen.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthForceFR;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenVerifyRefResponse;
import com.tmb.oneapp.transferservice.feature.authen.model.VerifyPinCacheRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${oauth.name}", url = "${oauth.endpoint}")
public interface OauthFeignClient {
    @PostMapping(value = "/apis/oauth/v2/verify-pin-ref", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> getVerifyPinCache(
            @RequestHeader(TransferServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
            @Valid @RequestBody VerifyPinCacheRequest body);

    @PostMapping(value = "/v1/oneapp-auth-service/oauth/common-authen/verify-ref", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CommonAuthenVerifyRefResponse>> verifyCommonAuthen(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody CommonAuthenVerifyRefRequest body);

    @GetMapping(value = "/v1/oneapp-auth/common-authen/force-fr", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CommonAuthForceFR>> getCommonAuthForceFR(
            @RequestHeader(TransferServiceConstant.HEADER_CORRELATION_ID) @NotEmpty String correlationId,
            @RequestHeader(TransferServiceConstant.HEADER_CRM_ID) @NotEmpty String crmId);

}

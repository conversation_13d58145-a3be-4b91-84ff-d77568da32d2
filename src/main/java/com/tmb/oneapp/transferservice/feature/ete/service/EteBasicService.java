package com.tmb.oneapp.transferservice.feature.ete.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.ete.model.EteBasicResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteError;
import com.tmb.oneapp.transferservice.feature.ete.model.EteStatus;
import com.tmb.oneapp.transferservice.feature.ete.model.EventLogTransfer;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.model.FeignAdditionalStatus;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETEResponse;
import com.tmb.oneapp.transferservice.service.V1TransfersServiceHelper;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_ERROR_TMBCBS_PREFIX;

public abstract class EteBasicService {
    private static final TMBLogger<EteBasicService> logger = new TMBLogger<>(EteBasicService.class);
    @Autowired
    V1TransfersServiceHelper v1TransferServiceHelper;

    private <T2> void tryToThrowWithEteErrorCode(FeignException feignException, EventLogTransfer eventLog, T2 eteResponseClass) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        Optional<ByteBuffer> byteBufferOptional = feignException.responseBody();
        if (byteBufferOptional.isEmpty()) {
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
        }

        ByteBuffer byteBuffer = byteBufferOptional.get();
        String s = StandardCharsets.UTF_8.decode(byteBuffer).toString();
        try {
            if (eteResponseClass.equals(EteDepositTermResponse.class)) {
                mapETEErrorDepositTermWithdrawalInquiry(s);
            } else if (eteResponseClass.equals(EteDepositAccount.class)) {
                mapETEErrorDepositGetAccount(s);
            } else if (eteResponseClass.equals(FundTransferOwnTMBETESuccess.class)) {
                mapETEErrorFundTransferValidate(s);
            } else if (eteResponseClass.equals(TPromptPayETEResponse.class)) {
                mapETEErrorPromptPayOtherBank(eventLog, s);
            } else {
                logger.error("Cannot map Error from ETE, Unknown this class. [responseClassName = {}]", eteResponseClass);
                throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public <T1 extends EteBasicResponse, T2> T1 request(Supplier<ResponseEntity<? extends EteBasicResponse>> supplier, EventLogTransfer eventLog, T2 responseClassName) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        try {
            ResponseEntity<? extends EteBasicResponse> response = supplier.get();
            EteBasicResponse eteResponse = response.getBody();
            EteStatus eteStatus = Optional.ofNullable(eteResponse.getStatus()).orElse(new EteStatus());
            if (TransferServiceConstant.STATUS_SUCCESS_CODE.equals(eteStatus.getCode())) {
                return (T1) eteResponse;
            }
        } catch (FeignException e) {
            logger.error("calling ETE service got exception:", e);
            tryToThrowWithEteErrorCode(e, eventLog, responseClassName);
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
    }

    private void mapETEErrorDepositGetAccount(String s) throws JsonProcessingException, TMBCommonException {
        EteDepositAccount eteResponseError = (EteDepositAccount) TMBUtils.convertStringToJavaObj(s, EteDepositAccount.class);

        List<EteError> errors = eteResponseError.getErrors();
        EteError lastEteError = errors.get(errors.size() - 1);

        String errorCode = lastEteError.getNamespace() + "_" + lastEteError.getCode();
        String errorMessage = lastEteError.getMessage();
        throw failETEExceptionWithCodeAndMessage(errorCode, errorMessage);
    }

    private void mapETEErrorDepositTermWithdrawalInquiry(String s) throws JsonProcessingException, TMBCommonException {
        EteDepositTermResponse eteResponseError = (EteDepositTermResponse) TMBUtils.convertStringToJavaObj(s, EteDepositTermResponse.class);

        List<EteError> errors = eteResponseError.getErrors();
        EteError lastETEError = errors.get(errors.size() - 1);

        String errorCode = String.format("%s_%s", lastETEError.getNamespace(), lastETEError.getCode());
        String errorMessage = lastETEError.getMessage();
        throw failETEExceptionWithCodeAndMessage(errorCode, errorMessage);
    }

    private void mapETEErrorFundTransferValidate(String s) throws JsonProcessingException, TMBCommonException {
        FundTransferOwnTMBETESuccess eteResponseError = (FundTransferOwnTMBETESuccess) TMBUtils.convertStringToJavaObj(s, FundTransferOwnTMBETESuccess.class);

        FeignAdditionalStatus lastAdditionStatus = eteResponseError.getAdditionalStatus().get(eteResponseError.getAdditionalStatus().size() - 1);

        boolean isErrorIncorrectToAccount = StringUtils.isBlank(lastAdditionStatus.getServerStatusCode());
        if (isErrorIncorrectToAccount) {
            throw new TMBCommonException(ResponseCode.INCORRECT_TO_ACCOUNT.getCode(), ResponseCode.INCORRECT_TO_ACCOUNT.getMessage(), ResponseCode.INCORRECT_TO_ACCOUNT.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
        }

        String errorCode = TRANSFER_ERROR_TMBCBS_PREFIX + lastAdditionStatus.getServerStatusCode();

        throw failETEExceptionWithCodeAndMessage(errorCode, lastAdditionStatus.getStatusDesc());
    }

    private void mapETEErrorPromptPayOtherBank(EventLogTransfer eventLog, String s) throws JsonProcessingException, TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TPromptPayETEResponse eteBasicResponse = (TPromptPayETEResponse) TMBUtils.convertStringToJavaObj(s, TPromptPayETEResponse.class);
        EteStatus eteStatus = Optional.ofNullable(eteBasicResponse.getStatus()).orElse(new EteStatus());

        String prefix = TransferServiceConstant.TRANSFER_ERROR_PROMPTPAY_PREFIX;
        boolean isFromValidate = StringUtils.equals(eventLog.getEventName(), TransferServiceConstant.TRANSFER_VALIDATE_OTHER_BANK);
        boolean isFromConfirm = StringUtils.equals(eventLog.getEventName(), TransferServiceConstant.TRANSFER_CONFIRM_OTHER_BANK);
        if (isFromValidate || isFromConfirm) {
            FeignAdditionalStatus feignAdditionalStatus = Optional.ofNullable(eteStatus.getAdditionalStatus()).orElse(new FeignAdditionalStatus());
            eventLog.setStatusCode(prefix + Optional.ofNullable(feignAdditionalStatus.getServerStatusCode()).orElse("").toLowerCase());
            eventLog.setDescriptionFromEte(feignAdditionalStatus.getStatusDesc());
            eventLog.setDescription(TransferServiceConstant.FAILED_ETE);
        }

        String errorCode = prefix + eteStatus.getAdditionalStatus().getServerStatusCode();

        if (isFromValidate && errorCode.equalsIgnoreCase(ResponseCode.PROMPT_PAY_NOT_REGISTERED.getCode())) {
            String mobileOrCitizenId = eventLog.getFlexibleValue();

            throw promptPayNotRegisteredException(mobileOrCitizenId);
        }

        String errorMessage = eteStatus.getDescription();
        throw failETEExceptionWithCodeAndMessage(errorCode, errorMessage);
    }

    private TMBCustomCommonExceptionWithResponse promptPayNotRegisteredException(String mobileOrIdRequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String formatMobileOrIdRequest = v1TransferServiceHelper.formatMobileOrCitizen(mobileOrIdRequest);

        Map<String, String> errorMsg = new HashMap<>();
        errorMsg.put("<$PromtPayNumber>", formatMobileOrIdRequest);
        throw new TMBCustomCommonExceptionWithResponse(
                ResponseCode.PROMPT_PAY_NOT_REGISTERED.getCode(),
                String.format(ResponseCode.PROMPT_PAY_NOT_REGISTERED.getMessage(), formatMobileOrIdRequest),
                ResponseCode.PROMPT_PAY_NOT_REGISTERED.getService(),
                HttpStatus.OK,
                null,
                null,
                errorMsg
        );
    }

    private TMBCommonException failETEExceptionWithCodeAndMessage(String errorCode, String messages) {
        return new TMBCommonException(errorCode.toLowerCase(), messages, ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
    }

}

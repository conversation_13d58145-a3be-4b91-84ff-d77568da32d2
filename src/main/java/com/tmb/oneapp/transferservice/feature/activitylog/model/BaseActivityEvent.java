package com.tmb.oneapp.transferservice.feature.activitylog.model;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.BaseEvent;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.APP_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CHANNEL;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DEVICE_MODEL;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FAILURE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.OS_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;

public abstract class BaseActivityEvent extends BaseEvent {
    private static final TMBLogger<BaseActivityEvent> logger = new TMBLogger<>(BaseActivityEvent.class);


    protected BaseActivityEvent(HttpHeaders headers, String correlationId) {
        logger.info("validate Header :{}", headers);
        super.setActivityDate(Long.toString(System.currentTimeMillis()));
        super.setIpAddress(headers.getFirst(IP_ADDRESS));
        super.setOsVersion(headers.getFirst(OS_VERSION));
        super.setChannel(headers.getFirst(CHANNEL));
        super.setAppVersion(headers.getFirst(APP_VERSION));
        super.setDeviceId(headers.getFirst(DEVICE_ID));
        super.setDeviceModel(headers.getFirst(DEVICE_MODEL));
        super.setCrmId(headers.getFirst(HEADER_CRM_ID));
        super.setCorrelationId(correlationId);
        super.setActivityStatus(SUCCESS);
    }

    protected BaseActivityEvent(HttpHeaders headers) {
        if (headers == null) {
            return;
        }
        logger.info("validate Header :{}", headers);
        super.setActivityDate(Long.toString(System.currentTimeMillis()));
        super.setIpAddress(headers.getFirst(IP_ADDRESS));
        super.setOsVersion(headers.getFirst(OS_VERSION));
        super.setChannel(headers.getFirst(CHANNEL));
        super.setAppVersion(headers.getFirst(APP_VERSION));
        super.setDeviceId(headers.getFirst(DEVICE_ID));
        super.setDeviceModel(headers.getFirst(DEVICE_MODEL));
        super.setCrmId(headers.getFirst(HEADER_CRM_ID));
        super.setCorrelationId(headers.getFirst(HEADER_CORRELATION_ID));
        super.setActivityStatus(SUCCESS);
    }

    protected String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }

        DecimalFormat commasFormat = new DecimalFormat("#,##0.00");
        commasFormat.setRoundingMode(RoundingMode.DOWN);

        return commasFormat.format(number);
    }

    public void setFailureStatusWithReasonFromException(Exception e) {
        setActivityStatus(FAILURE);
        if (e instanceof TMBCommonException) {
            setFailReason(((TMBCommonException) e).getErrorCode() + " : " + ((TMBCommonException) e).getErrorMessage());
        } else if (e.getCause() instanceof TMBCommonException) {
            setFailReason(String.format("%s : %s", ((TMBCommonException) e.getCause()).getErrorCode(), ((TMBCommonException) e.getCause()).getErrorMessage()));
        } else {
            setFailReason(e.getClass().getSimpleName());
        }
    }
}

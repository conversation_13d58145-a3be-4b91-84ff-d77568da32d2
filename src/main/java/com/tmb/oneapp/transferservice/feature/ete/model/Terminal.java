
package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Setter
@Getter
@JsonPropertyOrder({ "id", "type", "branchId", "recordId", "pccTraceId", "traceId", "issuerRoutingId", "benRoutingId",
		"acquirerRoutingId", "reversalCode", "originalType", "responseCode", "pin", "state", "country", "issuerFee",
		"benFee", "chargeFee", "region", "trackToLength", "trackToData", "routeStat", "productInd", "dpcId",
		"releaseId", "switchDate", "chequeNo", "senderBranch", "receiverBranch", "senderReferenceNo", "channelId" })
public class Terminal {
	@JsonProperty("id")
	private String id;
	@JsonProperty("type")
	private String type;
	@JsonProperty("branchId")
	private String branchId;
	@JsonProperty("recordId")
	private String recordId;
	@JsonProperty("pccTraceId")
	private String pccTraceId;
	@JsonProperty("traceId")
	private String traceId;
	@JsonProperty("issuerRoutingId")
	private String issuerRoutingId;
	@JsonProperty("benRoutingId")
	private String benRoutingId;
	@JsonProperty("acquirerRoutingId")
	private String acquirerRoutingId;
	@JsonProperty("reversalCode")
	private String reversalCode;
	@JsonProperty("originalType")
	private String originalType;
	@JsonProperty("responseCode")
	private String responseCode;
	@JsonProperty("pin")
	private String pin;
	@JsonProperty("state")
	private String state;
	@JsonProperty("country")
	private String country;
	@JsonProperty("issuerFee")
	private Double issuerFee;
	@JsonProperty("benFee")
	private Double benFee;
	@JsonProperty("chargeFee")
	private Double chargeFee;
	@JsonProperty("region")
	private String region;
	@JsonProperty("trackToLength")
	private String trackToLength;
	@JsonProperty("trackToData")
	private String trackToData;
	@JsonProperty("routeStat")
	private String routeStat;
	@JsonProperty("productInd")
	private String productInd;
	@JsonProperty("dpcId")
	private String dpcId;
	@JsonProperty("releaseId")
	private String releaseId;
	@JsonProperty("switchDate")
	private String switchDate;
	@JsonProperty("chequeNo")
	private String chequeNo;
	@JsonProperty("senderBranch")
	private String senderBranch;
	@JsonProperty("receiverBranch")
	private String receiverBranch;
	@JsonProperty("senderReferenceNo")
	private String senderReferenceNo;
	@JsonProperty("channelId")
	private String channelId;
	private Double acquirerFee;
	private String receiverAccountIdLength;
	private String senderAccountIdLength;
}

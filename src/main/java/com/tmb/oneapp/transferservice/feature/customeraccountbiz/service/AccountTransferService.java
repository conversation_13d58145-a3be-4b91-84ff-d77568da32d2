package com.tmb.oneapp.transferservice.feature.customeraccountbiz.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.AccountSaving;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.DepositAccountTransfer;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import com.tmb.oneapp.transferservice.utils.TransferUtils;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

@Service
@RequiredArgsConstructor
public class AccountTransferService {
    private static final TMBLogger<AccountTransferService> logger = new TMBLogger<>(AccountTransferService.class);
    private final CustomerAccountBizService customerAccountBizService;

    private static final List<String> listOfDisplayStatusDeleteAndClose = Arrays.asList("02", "05");

    public List<DepositAccountTransfer> getDepositAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag) throws TMBCommonException {
        List<DepositAccount> depositAccountLists;
        try {
            AccountSaving accountSaving = customerAccountBizService.getAccountList(correlationId, crmId, refreshFlag, allAccountFlag);

            Predicate<DepositAccount> isAccountStatusInActiveOrActiveOrDormant = e -> (
                    e.getAccountStatus().equals("INACTIVE")
                            || e.getAccountStatus().equals("ACTIVE")
                            || e.getAccountStatus().equals("DORMANT"));

            Predicate<DepositAccount> displayStatusNotDeleteOrClose = e -> (
                    !listOfDisplayStatusDeleteAndClose.contains(e.getDisplayAccountStatus()));

            depositAccountLists = accountSaving.getDepositAccountLists()
                    .stream()
                    .filter(isAccountStatusInActiveOrActiveOrDormant.and(displayStatusNotDeleteOrClose))
                    .toList();
            if (ObjectUtils.isEmpty(depositAccountLists)) {
                throw TransferUtils.failException(ResponseCode.ACCOUNT_NOT_ELIGIBLE);
            }

        } catch (FeignException.FeignClientException feignClientException) {
            throw TransferUtils.failException(ResponseCode.FAILED);
        }
        return setAllowTransferOwnAndOtherTTB(depositAccountLists);
    }

    public List<DepositAccountTransfer> getAllDepositAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag) throws TMBCommonException {
        List<DepositAccount> depositAccountLists;
        try {
            AccountSaving accountSaving = customerAccountBizService.getAccountList(correlationId, crmId, refreshFlag, allAccountFlag);

            Predicate<DepositAccount> isAccountStatusInActiveOrActiveOrDormant = e -> (
                    e.getAccountStatus().equals("INACTIVE")
                            || e.getAccountStatus().equals("ACTIVE")
                            || e.getAccountStatus().equals("DORMANT"));

            Predicate<DepositAccount> displayStatusNotDeleteOrClose = e -> (
                    !listOfDisplayStatusDeleteAndClose.contains(e.getDisplayAccountStatus()));

            depositAccountLists = new ArrayList<>(accountSaving.getDepositAccountLists()
                    .stream()
                    .filter(isAccountStatusInActiveOrActiveOrDormant.and(displayStatusNotDeleteOrClose))
                    .toList());
            depositAccountLists.addAll(accountSaving.getFcdAccountLists()
                    .stream()
                    .filter(isAccountStatusInActiveOrActiveOrDormant.and(displayStatusNotDeleteOrClose))
                    .toList());
            if (ObjectUtils.isEmpty(depositAccountLists)) {
                throw TransferUtils.failException(ResponseCode.ACCOUNT_NOT_ELIGIBLE);
            }

        } catch (FeignException.FeignClientException feignClientException) {
            throw TransferUtils.failException(ResponseCode.FAILED);
        }
        return setAllowTransferOwnAndOtherTTB(depositAccountLists);
    }

    private List<DepositAccountTransfer> setAllowTransferOwnAndOtherTTB(List<DepositAccount> depositAccounts) {
        List<DepositAccountTransfer> depositAccountTransfers = new ArrayList<>();

        depositAccounts.forEach(depositAccount -> {
            DepositAccountTransfer depositAccountTransfer = new DepositAccountTransfer();
            BeanUtils.copyProperties(depositAccount, depositAccountTransfer);

            if (depositAccountTransfer.getTransferOwnTTBMapCode() != null) {
                String[] ownMapList = depositAccountTransfer.getTransferOwnTTBMapCode().split("");
                depositAccountTransfer.setAllowTransferOwnTtbDda(ownMapList[0]);
                depositAccountTransfer.setAllowTransferOwnTtbSda(ownMapList[1]);
                depositAccountTransfer.setAllowTransferOwnTtbCda(ownMapList[2]);
            }
            if (depositAccountTransfer.getTransferOtherTTBMapCode() != null) {
                String[] otherMapList = depositAccountTransfer.getTransferOtherTTBMapCode().split("");
                depositAccountTransfer.setAllowTransferOtherTtbDda(otherMapList[0]);
                depositAccountTransfer.setAllowTransferOtherTtbSda(otherMapList[1]);
                depositAccountTransfer.setAllowTransferOtherTtbCda(otherMapList[2]);
            }

            depositAccountTransfers.add(depositAccountTransfer);
        });
        return depositAccountTransfers;
    }
}

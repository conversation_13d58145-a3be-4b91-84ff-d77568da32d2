package com.tmb.oneapp.transferservice.feature.authen.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonAuthForceFR implements Serializable {
    private String id;
    private String crmId;
    private String requestId;
    private Boolean isForce;
    private Boolean isForceDipchip;
    private String ruleId;
    private String reason;
    private String livenessParameter;
    private String channel;
    private Date updateDate;
}

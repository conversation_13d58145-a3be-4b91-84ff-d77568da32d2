package com.tmb.oneapp.transferservice.feature.financiallog.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.TransferActivities;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DEBIT_TRANSACTION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.REFERENCE_ACTIVITY_ID_VALUE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS;


public class OtherBankTransferTransactionLog extends TransferActivities {
    public OtherBankTransferTransactionLog() {

    }

    public OtherBankTransferTransactionLog(String crmId, TPromptPayVerifyETEResponse cacheOtherBank, String activityFinAndTransId, String transactionDateTime) {
        setUpFields(crmId, cacheOtherBank, activityFinAndTransId, transactionDateTime);
    }

    private void setUpFields(String crmId, TPromptPayVerifyETEResponse cacheOtherBank, String activityFinAndTransId, String transactionDateTime) {
        setCrmId(crmId);
        setActivityId(activityFinAndTransId);
        setTransactionDate(transactionDateTime);

        setReferenceActivityTypeId(REFERENCE_ACTIVITY_ID_VALUE);
        setChannelId(MB);
        setTransactionStatus(SUCCESS);
        setFinancialTransferCRDR(DEBIT_TRANSACTION);

        setFromAccountNo(cacheOtherBank.getSender().getAccountId());
        setFromAccountNickname(cacheOtherBank.getPaymentCacheData().getFromAccountNickname());

        setToAccountNo(cacheOtherBank.getReceiver().getAccountId());
        setToAccountName(cacheOtherBank.getReceiver().getAccountDisplayName());
        setToAccountNickname(cacheOtherBank.getPaymentCacheData().getToFavoriteNickname());

        setFinancialTransferAmount(String.valueOf(cacheOtherBank.getAmount()));
        setProxyType(cacheOtherBank.getReceiver().getProxyType());
        setProxyValue(cacheOtherBank.getReceiver().getProxyValue());

        setFinancialTransferMemo(cacheOtherBank.getPaymentCacheData().getNote());
        setFinancialTransferRefId(cacheOtherBank.getTransactionReference());
        setToBankShortName(cacheOtherBank.getPaymentCacheData().getBankShortName());
    }

}

package com.tmb.oneapp.transferservice.feature.activitylog.model;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BLANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PAYMENT_QR_PROMPT_PAY;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_MOBILE;

@Getter
@Setter
public class PromptPayValidateActivityLog extends TransferActivityLog {
    private String pinFreeFlag;
    private String ddpFlag;

    protected PromptPayValidateActivityLog(HttpHeaders headers) {
        super(headers);
    }


    public void updateActivityTypeId(String toProxyType, boolean transferToTTBAcct, String qr) {
        setActivityTypeId(getActivityTypeIdPromptPay(toProxyType, transferToTTBAcct, qr));
    }

    private String getMobileTransferActivityId(boolean transferToTTB, String qr) {
        if (transferToTTB) {
            if (StringUtils.equals(qr, PAYMENT_QR_PROMPT_PAY)) {
                return ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
            }
            return ACTIVITY_LOG_VALIDATE_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
        }

        if (StringUtils.equals(qr, PAYMENT_QR_PROMPT_PAY)) {
            return ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
        }

        return ACTIVITY_LOG_VALIDATE_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
    }

    private String getCitizenTransferActivityId(boolean transferToTTB, String qr) {
        if (transferToTTB) {
            if (StringUtils.equals(qr, PAYMENT_QR_PROMPT_PAY)) {
                return ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
            }
            return ACTIVITY_LOG_VALIDATE_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
        }

        if (StringUtils.equals(qr, PAYMENT_QR_PROMPT_PAY)) {
            return ACTIVITY_LOG_VALIDATE_QR_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
        }

        return ACTIVITY_LOG_VALIDATE_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
    }

    private String getActivityTypeIdPromptPay(String toProxyType, boolean transferToTTBAcct, String qr) {
        String activityTypeId = BLANK;
        switch (toProxyType) {
            case PROXY_TYPE_PROMPTPAY_BY_MOBILE:
                activityTypeId = getMobileTransferActivityId(transferToTTBAcct, qr);
                break;
            case PROXY_TYPE_PROMPTPAY_BY_CITIZEN:
                activityTypeId = getCitizenTransferActivityId(transferToTTBAcct, qr);
                break;
            default:
                break;
        }
        return activityTypeId;
    }
}

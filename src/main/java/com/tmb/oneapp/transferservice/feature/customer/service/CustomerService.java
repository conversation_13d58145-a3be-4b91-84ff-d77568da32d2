package com.tmb.oneapp.transferservice.feature.customer.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customer.client.CustomerExpServiceClient;
import com.tmb.oneapp.transferservice.feature.customer.client.CustomerServiceClient;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyRequest;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyResponse;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.mapper.DepositAccountTransferMapper;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.service.AccountTransferService;
import com.tmb.oneapp.transferservice.feature.notification.model.V1ENotificationSettingResponse;
import com.tmb.oneapp.transferservice.model.AccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.DailyUsageData;
import com.tmb.oneapp.transferservice.model.customer.PaymentAccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.customer.PinFreeCountData;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CustomerService {
    private static final TMBLogger<CustomerService> logger = new TMBLogger<>(CustomerService.class);
    private final CustomerServiceClient customerServiceClient;
    private final CustomerExpServiceClient customerExpServiceClient;
    private final AccountTransferService accountTransferService;

    public V1CrmProfile getCrmProfile(String correlationId, String crmId) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<V1CrmProfile>> res = customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
            return new V1CrmProfile();
        } catch (FeignException ex) {
            logger.error("Error while fetching data from CrmProfile : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public List<DepositAccount> getAccountsTransfer(String correlationId, String crmId) throws TMBCommonException {
        return DepositAccountTransferMapper.INSTANCE.toDepositAccountList(accountTransferService.getDepositAccountList(correlationId, crmId, false, true));
    }

    public List<DepositAccount> getAllAccountsTransfer(String correlationId, String crmId) throws TMBCommonException {
        return DepositAccountTransferMapper.INSTANCE.toDepositAccountList(accountTransferService.getAllDepositAccountList(correlationId, crmId, false, true));
    }

    public CustomerKYCResponse getCustomerKyc(String crmId, String correlationId) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> res = customerServiceClient.fetchCustomerKYC(correlationId, crmId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
            return new CustomerKYCResponse();
        } catch (FeignException ex) {
            logger.error("Error while fetching data from getCustomerKyc : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public void updatePinFreeCount(String correlationId, String crmId, PinFreeCountData pinFreeCountData) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<String>> res = customerServiceClient.updatePinFreeCount(correlationId, crmId, pinFreeCountData);
        } catch (FeignException ex) {
            logger.error("Update pin free error : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public void updateDailyUsage(String correlationId, String crmId, DailyUsageData dailyUsageData) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<String>> res = customerServiceClient.updateDailyUsage(correlationId, crmId, dailyUsageData);
        } catch (FeignException ex) {
            logger.error("Update daily limit error : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public CustomerProfileStatus getCustomerProfileByDeviceId(String deviceId) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<CustomerProfileStatus>> res = customerServiceClient.getCustomerProfileByDeviceId(deviceId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
            return new CustomerProfileStatus();
        } catch (FeignException ex) {
            logger.error("Error while fetching data from getCustomerKyc : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public V1ENotificationSettingResponse getENotificationSetting(String correlationId, String crmId) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<V1ENotificationSettingResponse>> res = customerServiceClient.getENotificationSetting(correlationId, crmId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
            return new V1ENotificationSettingResponse();
        } catch (FeignException ex) {
            logger.error("Error while fetching data from getENotificationSetting : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public String updatePaymentAccumulateUsageAmount(String correlationId, String crmId, PaymentAccumulateUsageRequest request) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<String>> res = customerServiceClient.updatePaymentAccumulateUsageAmount(correlationId, crmId, request);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
            return StringUtils.EMPTY;
        } catch (FeignException ex) {
            logger.error("cannot updatePaymentAccumulateUsageAmount : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    public String updateUsageAccumulation(String correlationId, String crmId, AccumulateUsageRequest accumulateUsageRequest) throws TMBCommonException {
        try {
            return Optional.of(customerServiceClient.updateUsageAccumulation(correlationId, crmId, accumulateUsageRequest))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .map(TmbServiceResponse::getData)
                    .orElse(null);

        } catch (FeignException ex) {
            logger.error("cannot updateUsageAccumulation : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }

    }


    public FRWhitelistResult isFrWhitelistByCrmId(String crmId, String correlationId) throws TMBCommonException {
        try {

            ResponseEntity<TmbOneServiceResponse<FRWhitelistResult>> res = customerExpServiceClient
                    .getFrWhitelistResult(crmId, correlationId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
        } catch (FeignException ex) {
            logger.error("Error while fetching data from getFrWhitelistResult : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
        return null;
    }

    public boolean isCommonFRExistedByUUID(String crmId, String correlationId, CommonFRVerifyRequest commonFRVerifyRequest, String ipAddress) throws TMBCommonException {
        CommonFRVerifyResponse commonFRVerifyResponse = getCommonFRByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress);
        return Boolean.TRUE.equals(commonFRVerifyResponse.getCommonfrSuccess());
    }

    public CommonFRVerifyResponse getCommonFRByUUID(String crmId, String correlationId, CommonFRVerifyRequest commonFRVerifyRequest, String ipAddress) throws TMBCommonException {
        try {
            return Optional.ofNullable(customerServiceClient.getCommonFRByUUID(correlationId, crmId, ipAddress, commonFRVerifyRequest))
                    .map(HttpEntity::getBody)
                    .map(TmbServiceResponse::getData)
                    .orElse(new CommonFRVerifyResponse());
        } catch (Exception e) {
            logger.error("can not calling get common fr verify service : [{}]", commonFRVerifyRequest, e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.OK, null);
        }
    }

}

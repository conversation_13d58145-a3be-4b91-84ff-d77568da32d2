package com.tmb.oneapp.transferservice.feature.ete.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;
@FeignClient(name = "ete-deposit-term-client", url = "${ete.deposit.term.url}",configuration = FeignCommonConfig.class)
public interface EteDepositTermClient {
    @PostMapping(value = "/v3.0/internal/deposit/term/withdrawal/inquiry", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EteDepositTermResponse> getDepositWithdrawalInfo(@RequestBody String reqTemplate, @RequestHeader Map<String, String> headers);

}

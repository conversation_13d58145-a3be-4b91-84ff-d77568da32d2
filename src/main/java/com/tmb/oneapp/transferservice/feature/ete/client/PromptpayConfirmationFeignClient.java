package com.tmb.oneapp.transferservice.feature.ete.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETERequest;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

@FeignClient(name = "ete-promptpay-confirmation-service", url = "${ete.promptpay.confirmation.url}",configuration = FeignCommonConfig.class)
public interface PromptpayConfirmationFeignClient {



	@CircuitBreaker(name = "transferPromptpayConfirmationToOther")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToOther(@RequestHeader HttpHeaders headers,
																  @RequestBody TPromptPayETERequest body);

	@CircuitBreaker(name = "transferPromptpayConfirmationToBBL")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToBBL(@RequestHeader HttpHeaders headers,
																				   @RequestBody TPromptPayETERequest body);

	@CircuitBreaker(name = "transferPromptpayConfirmationToKBANK")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToKBANK(@RequestHeader HttpHeaders headers,
																					 @RequestBody TPromptPayETERequest body);

	@CircuitBreaker(name = "transferPromptpayConfirmationToKTB")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToKTB(@RequestHeader HttpHeaders headers,
																				   @RequestBody TPromptPayETERequest body);

	@CircuitBreaker(name = "transferPromptpayConfirmationToSCB")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToSCB(@RequestHeader HttpHeaders headers,
																				   @RequestBody TPromptPayETERequest body);

	@CircuitBreaker(name = "transferPromptpayConfirmationToBAY")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToBAY(@RequestHeader HttpHeaders headers,
																				   @RequestBody TPromptPayETERequest body);

	@CircuitBreaker(name = "transferPromptpayConfirmationToGSB")
	@PostMapping(value = "${ete.promptpay.confirmation.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayConfirmationToGSB(@RequestHeader HttpHeaders headers,
																				   @RequestBody TPromptPayETERequest body);
}

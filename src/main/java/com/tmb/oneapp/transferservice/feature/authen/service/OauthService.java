package com.tmb.oneapp.transferservice.feature.authen.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthForceFR;
import com.tmb.oneapp.transferservice.feature.authen.client.OauthFeignClient;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenVerifyRefResponse;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.transferservice.feature.authen.model.VerifyPinCacheRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;

@Service
@RequiredArgsConstructor
public class OauthService {
    private static final TMBLogger<OauthService> logger = new TMBLogger<>(OauthService.class);
    public static final String CRM_ID_HEADER_COMMON_AUTHEN = "crm-id";
    private final OauthFeignClient oauthFeignClient;
    private static final String ZERO = "0";

    @LogAround
    public void verifyPinCache(String correlationId, String crmId, String key, String module) throws TMBCommonException {
        logger.info("========== Start call service name : {} ==========", "Oauth Service -> getVerifyPinCacheV2()");
        VerifyPinCacheRequest request = new VerifyPinCacheRequest()
                .setCrmId(crmId)
                .setModule(module)
                .setKey(key);
        try {
            Optional.ofNullable(oauthFeignClient.getVerifyPinCache(correlationId, request))
                    .filter(o -> o.getStatusCode().equals(HttpStatus.OK))
                    .map(ResponseEntity::getBody)
                    .map(TmbOneServiceResponse::getStatus)
                    .map(TmbStatus::getCode)
                    .filter(s -> s.equals(ResponseCode.SUCCESS.getCode()))
                    .orElseThrow();
            logger.info("VerifyPinCacheV2 ========== Success");

        } catch (Exception e) {
            logger.error("Got exception ========== Oauth Service -> getVerifyPinCacheV2()", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.OK, e);
        } finally {
            logger.info("========== End call service name : {} ==========", "Oauth Service -> getCache()");
        }
    }

    @LogAround
    public void verifyCommonAuthenWithPayload(HttpHeaders headers, CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest) throws TMBCommonException {
        var commonAuthenVerifyRefResponse = verifyCommonAuthentication(headers, commonAuthenWithPayloadRequest);
        boolean isPayloadChange = !isPayloadNotChanged(commonAuthenWithPayloadRequest, commonAuthenVerifyRefResponse);
        if (isPayloadChange) {
            logger.error("Payload has been changed, Please verify data [commonAuthenPayloadRequest = {}, commonAuthenResponse = {}]", commonAuthenWithPayloadRequest, commonAuthenVerifyRefResponse);
            throw new TMBCommonException(ResponseCode.INVALID_REQUEST.getCode(), "Common Authen payload incorrect!!.", ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }

    }

    private boolean isPayloadNotChanged(CommonAuthenWithPayloadRequest request, CommonAuthenVerifyRefResponse response) {
        return isAmountNotChanged(request, response) &&
                isDailyNotChanged(request, response) &&
                isFeatureIdNotChanged(request, response) &&
                isBankCodeNotChanged(request, response);
    }

    private boolean isDailyNotChanged(CommonAuthenWithPayloadRequest request, CommonAuthenVerifyRefResponse response) {
        return to2Decimal(request.getDailyAmount()).equals(to2Decimal(response.getDailyAmount()));
    }

    private boolean isAmountNotChanged(CommonAuthenWithPayloadRequest request, CommonAuthenVerifyRefResponse response) {
        return to2Decimal(request.getAmount()).equals(to2Decimal(response.getAmount()));
    }

    private boolean isFeatureIdNotChanged(CommonAuthenWithPayloadRequest request, CommonAuthenVerifyRefResponse response) {
        return Objects.equals(request.getFeatureId(), response.getFeatureId());
    }

    private boolean isBankCodeNotChanged(CommonAuthenWithPayloadRequest request, CommonAuthenVerifyRefResponse response) {
        return Objects.equals(StringUtils.leftPad(request.getBankCode(), 3, "0"), StringUtils.leftPad(response.getBankCode(), 3, "0"));
    }

    @LogAround
    public void verifyCommonAuthenWithPayloadForTermDepositOldVersion(HttpHeaders headers, CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest) throws TMBCommonException {
        String amount = commonAuthenWithPayloadRequest.getAmount();
        String dailyAmount = commonAuthenWithPayloadRequest.getDailyAmount();
        var commonAuthenVerifyRefResponse = verifyCommonAuthentication(headers, commonAuthenWithPayloadRequest);
        boolean isPayloadChange = !isPayloadNotChangeForTermDepositOldVersion(amount, dailyAmount, commonAuthenVerifyRefResponse);
        if (isPayloadChange) {
            logger.error("Payload has been changed, Please verify data [Cache amount = {}, Cache daily amount = {}, Common Authen response = {}]", amount, dailyAmount, commonAuthenVerifyRefResponse);
            throw new TMBCommonException(ResponseCode.INVALID_REQUEST.getCode(), "Common Authen payload incorrect!!.", ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }

    }

    @LogAround
    public CommonAuthenVerifyRefResponse verifyCommonAuthentication(HttpHeaders headers, CommonAuthenVerifyRefRequest commonAuthenVerifyRefRequest) throws TMBCommonException {
        String crmId = headers.getFirst(HEADER_CRM_ID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        String ipAddress = headers.getFirst(IP_ADDRESS);
        boolean isRequireHeadersNull = ObjectUtils.anyNull(crmId, correlationId, ipAddress);

        if (isRequireHeadersNull) {
            logger.error("Headers incorrect, please validate headers. [x-crmid = {}, x-correlation-id = {}, X-Forward-For = {}]", crmId, correlationId, ipAddress);
            throw new TMBCommonException(ResponseCode.INVALID_REQUEST.getCode(), "Headers incorrect", ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }

        HttpHeaders requestHeader = new HttpHeaders();
        requestHeader.set(CRM_ID_HEADER_COMMON_AUTHEN, crmId);
        requestHeader.set(HEADER_CORRELATION_ID, correlationId);
        requestHeader.set(IP_ADDRESS, ipAddress);
        try {
            return Optional.ofNullable(oauthFeignClient.verifyCommonAuthen(requestHeader, commonAuthenVerifyRefRequest))
                    .filter(o -> o.getStatusCode().equals(HttpStatus.OK))
                    .map(ResponseEntity::getBody)
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();
        } catch (Exception e) {
            logger.error("Got exception ========== Oauth Service -> verifyCommonAuthentication()", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.OK, e);
        }
    }

    public CommonAuthForceFR getCommonAuthForceFR(String correlationId ,String crmId) {
        try {
            return Optional.ofNullable(oauthFeignClient.getCommonAuthForceFR(correlationId, crmId))
                    .map(ResponseEntity::getBody)
                    .map(TmbServiceResponse::getData)
                    .orElse(null);
        } catch (Exception e) {
            logger.error("Got exception ========== Oauth Service -> getCommonAuthForceFR()", e);
            return null;
        }
    }

    @LogAround
    private boolean isPayloadNotChangeForTermDepositOldVersion(String amount, String dailyAmount, CommonAuthenVerifyRefResponse commonAuthenVerifyRefResponse) {
        BigDecimal cacheAmount = to2Decimal(amount);
        BigDecimal cacheDailyAmount = to2Decimal(dailyAmount);
        BigDecimal commonAuthenAmount = to2Decimal(commonAuthenVerifyRefResponse.getAmount());
        BigDecimal commonAuthenDailyAmount = to2Decimal(commonAuthenVerifyRefResponse.getDailyAmount());

        boolean amountNotChange = cacheAmount.equals(commonAuthenAmount);
        boolean dailyAmountNotChange = cacheDailyAmount.equals(commonAuthenDailyAmount);
        return amountNotChange && dailyAmountNotChange;
    }

    private static BigDecimal to2Decimal(String amount) {
        return new BigDecimal(StringUtils.defaultIfEmpty(amount, ZERO)).setScale(2, RoundingMode.HALF_UP);
    }
}

package com.tmb.oneapp.transferservice.feature.customeraccountbiz.mapper;

import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.DepositAccountTransfer;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DepositAccountTransferMapper {
    DepositAccountTransferMapper INSTANCE = Mappers.getMapper(DepositAccountTransferMapper.class);

    DepositAccount toDepositAccount(DepositAccountTransfer depositAccountTransfer);
    List<DepositAccount> toDepositAccountList(List<DepositAccountTransfer> depositAccountTransferList);
}

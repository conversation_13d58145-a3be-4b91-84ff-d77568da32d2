package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EteTransferAccount {
    String id;
    String title;
    String shortName;
    String type;
    String productCode;
    String branchId;
    String branchName;
    String currency;
    String interestRate;
    String currentInterestRate;
    String openingMethod;
    AccountStatus status;
}

package com.tmb.oneapp.transferservice.feature.notification.model;

import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.PaymentCacheData;
import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.model.transfer.V1ITransferData;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

public class NotificationTransferMapper {
    public static final NotificationTransferMapper INSTANCE = new NotificationTransferMapper();
    private static final String PATTERN_DATE_WITH_TIME_TH = "d MMM yy - HH:mm น.";
    private static final String PATTERN_DATE_WITH_TIME_EN = "d MMM yy - h:mm a";
    private static final String TH_LOWER_CASE = "th";
    private static final String TH_UPPER_CASE = "TH";
    private static final String TTB_BANK_SHORT_NAME = "ttb";
    private static final String DEFAULT_TXN_TYPE_EN_VALUE = "Account";
    private static final String DEFAULT_TXN_TYPE_TH_VALUE = "บัญชี";
    private static final String PROXY_TYPE_TRANSFER_OTHER_BANK = "00";
    private static final String PROXY_TYPE_PROMPTPAY_BY_CITIZEN = "01";
    private static final String PROXY_TYPE_PROMPTPAY_BY_MOBILE = "02";

    private NotificationTransferMapper() {

    }

    public V1TransferNotification toTransferNotification(String template, String crmId, String correlationId, String transactionDateTime, CategoryInfoDataModel categoryInfoDataModel, V1ITransferData transferData) {
        if (transferData instanceof V1TransferData) {
            return toTransferNotification(template, crmId, correlationId, transactionDateTime, categoryInfoDataModel, (V1TransferData) transferData);
        }
        return toTransferNotification(template, crmId, correlationId, transactionDateTime, categoryInfoDataModel, (TPromptPayVerifyETEResponse) transferData);
    }


    private List<String> getDateTime(String transactionDateTime) {
        long covertDateTimeStringToLong = Long.parseLong(transactionDateTime);
        Date date = new Date(covertDateTimeStringToLong);

        String addDateTimeTH = new SimpleDateFormat(PATTERN_DATE_WITH_TIME_TH,
                new Locale(TH_LOWER_CASE, TH_UPPER_CASE)).format(date);

        String addDateTimeEN = new SimpleDateFormat(PATTERN_DATE_WITH_TIME_EN).format(date);
        return Arrays.asList(addDateTimeEN, addDateTimeTH);

    }

    private V1TransferNotification toTransferNotification(String template, String crmId, String correlationId, String transactionDateTime, CategoryInfoDataModel categoryInfo, V1TransferData transferData) {
        V1TransferNotification transferNotification = new V1TransferNotification();
        transferNotification.setTemplateName(template);
        transferNotification.setCrmId(crmId);
        transferNotification.setXCorrelationId(correlationId);
        if (StringUtils.isNotBlank(transactionDateTime)) {
            List<String> dateTimes = getDateTime(transactionDateTime);
            transferNotification.setAddDateTimeEN(dateTimes.get(0));
            transferNotification.setAddDateTimeTH(dateTimes.get(1));
        }

        transferNotification.setFavoriteNickname(transferData.getToFavoriteNickname());
        transferNotification.setNote(transferData.getMemo());
        //Set values
        transferNotification.setFee(transferData.getFeeFromETE().toString());
        transferNotification.setAmount(transferData.getAmount().toString());
        transferNotification.setToAcctId(transferData.getToAccount().getAccountNo());
        transferNotification.setFromAcctId(transferData.getFromAccount().getAccountNo());
        transferNotification.setAccountNickname(transferData.getFromAccountNickname());
        transferNotification.setTransactionRefNo(transferData.getTransactionReference());
        transferNotification.setBankShortName(TTB_BANK_SHORT_NAME);
        transferNotification.setTxnTypeEN(DEFAULT_TXN_TYPE_EN_VALUE);
        transferNotification.setTxnTypeTH(DEFAULT_TXN_TYPE_TH_VALUE);
        transferNotification.setCategoryENAndTH(transferData.getCategoryId(), categoryInfo);
        transferNotification.setCurrency(Optional.ofNullable(transferData.getFromAccount().getCurrency()).orElse(""));
        return transferNotification;
    }

    private String getTxnTypeTHOfPromptPay(String toProxyType) {
        switch (toProxyType) {
            case PROXY_TYPE_PROMPTPAY_BY_CITIZEN:
                return "เลขบัตรประชาชน";
            case PROXY_TYPE_PROMPTPAY_BY_MOBILE:
                return "เบอร์มือถือ";
            case PROXY_TYPE_TRANSFER_OTHER_BANK:
            default:
                return DEFAULT_TXN_TYPE_TH_VALUE;
        }
    }

    private String getTxnTypeENOfPromptPay(String toProxyType) {
        switch (toProxyType) {
            case PROXY_TYPE_PROMPTPAY_BY_CITIZEN:
                return "Citizen ID / Tax ID";
            case PROXY_TYPE_PROMPTPAY_BY_MOBILE:
                return "Mobile No";
            case PROXY_TYPE_TRANSFER_OTHER_BANK:
            default:
                return DEFAULT_TXN_TYPE_EN_VALUE;
        }
    }

    private V1TransferNotification toTransferNotification(String template, String crmId, String correlationId, String transactionDateTime, CategoryInfoDataModel categoryInfo, TPromptPayVerifyETEResponse transferData) {
        V1TransferNotification transferNotification = new V1TransferNotification();
        transferNotification.setTemplateName(template);
        transferNotification.setCrmId(crmId);
        transferNotification.setXCorrelationId(correlationId);
        if (StringUtils.isNotBlank(transactionDateTime)) {
            List<String> dateTimes = getDateTime(transactionDateTime);
            transferNotification.setAddDateTimeEN(dateTimes.get(0));
            transferNotification.setAddDateTimeTH(dateTimes.get(1));
        }
        PaymentCacheData paymentCacheData = transferData.getPaymentCacheData();
        transferNotification.setFee(transferData.getFee().toString());
        transferNotification.setAmount(transferData.getAmount().toString());
        transferNotification.setFavoriteNickname(paymentCacheData.getToFavoriteNickname());
        transferNotification.setNote(paymentCacheData.getNote());
        transferNotification.setCategoryENAndTH(paymentCacheData.getCategoryId(), categoryInfo);
        Receiver receiver = transferData.getReceiver();
        transferNotification.setToAcctId(receiver.getProxyValue());
        transferNotification.setFromAcctId(transferData.getSender().getAccountId());
        transferNotification.setAccountNickname(paymentCacheData.getFromAccountNickname());
        transferNotification.setTransactionRefNo(transferData.getTransactionReference());
        transferNotification.setBankShortName(paymentCacheData.getBankShortName());
        transferNotification.setTxnTypeEN(getTxnTypeENOfPromptPay(receiver.getProxyType()));
        transferNotification.setTxnTypeTH(getTxnTypeTHOfPromptPay(receiver.getProxyType()));
        return transferNotification;
    }
}

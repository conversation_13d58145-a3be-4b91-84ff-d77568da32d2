package com.tmb.oneapp.transferservice.feature.activitylog.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
public class OnUsValidateActivityLog extends TransferActivityLog {
    private String pinFreeFlag;
    private String ddpFlag;

    protected OnUsValidateActivityLog(HttpHeaders headers, String correlationId) {
        super(headers, correlationId);
    }

    protected OnUsValidateActivityLog(HttpHeaders headers) {
        super(headers);
    }
}

package com.tmb.oneapp.transferservice.feature.activitylog.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.constant.ActivityLogConstant;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.CustomSlip;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpHeaders;

/**
 * Represents the activity log for a completed custom slip transaction.
 */
@Getter
@Setter
@Accessors (chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomSlipCompleteActivityLog extends BaseActivityEvent {
    private String txnType;
    private String slipId;
    private String slipCategory;
    private String slipDefaultFlag;

    public CustomSlipCompleteActivityLog(HttpHeaders headers, CustomSlip customSlip) {
        super(headers);
        super.setActivityTypeId(ActivityLogConstant.ACTIVITY_LOG_CUSTOM_SLIP_COMPLETE_BACKGROUND_ACTIVITY_ID);
        this.txnType = TransferServiceConstant.TXN_TYPE_TRANSFER_TEXT;

        if (customSlip != null) {
            this.slipId = customSlip.getSlipId();
            this.slipCategory = customSlip.getSlipCategory();
            this.slipDefaultFlag = Boolean.TRUE.equals(customSlip.getSlipDefaultFlag()) ? "true" : "false";
        }
    }
}

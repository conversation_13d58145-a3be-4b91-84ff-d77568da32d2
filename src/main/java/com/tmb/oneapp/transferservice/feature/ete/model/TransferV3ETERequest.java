package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferV3ETERequest {
    private TransferAccountV3 fromAccount;
    private TransferAccountV3 toAccount;
    private BigDecimal amount;
    private String postedDate;
    private String transactionReference;
    private BigDecimal feeAmount;
    private String chargeType;
    private String feeType;
    private String additionalDescription;
}

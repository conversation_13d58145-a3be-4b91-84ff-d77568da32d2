package com.tmb.oneapp.transferservice.feature.bank.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.transferservice.model.bank.BankInfoDataModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@FeignClient(name = "bank-service-client", url = "${bank-service.url}",configuration = FeignCommonConfig.class)
public interface BankServiceClient {

    @GetMapping("/v1/bank-service/categories")
    ResponseEntity<TmbOneServiceResponse<List<CategoryInfoDataModel>>> getAllCategory(@RequestHeader(value = TransferServiceConstant.HEADER_CORRELATION_ID) String xCorrelationId);

    @GetMapping(value = "/v1/bank-service/banks", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    ResponseEntity<TmbOneServiceResponse<List<BankInfoDataModel>>> getAllBankInfo(
            @RequestHeader(name = "x-correlation-id") String correlationId);
}

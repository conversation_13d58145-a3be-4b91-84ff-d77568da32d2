package com.tmb.oneapp.transferservice.feature.ete.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventLog;
import com.tmb.common.logger.TTBEventMonitoringType;
import com.tmb.common.logger.TTBEventStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.ete.client.EteDepositTermClient;
import com.tmb.oneapp.transferservice.feature.ete.client.EteGetAccountClient;
import com.tmb.oneapp.transferservice.feature.ete.client.EteTransferConfirmationClient;
import com.tmb.oneapp.transferservice.feature.ete.client.EteTransferValidationClient;
import com.tmb.oneapp.transferservice.feature.ete.client.PromptpayConfirmationFeignClient;
import com.tmb.oneapp.transferservice.feature.ete.client.PromptpayValidationFeignClient;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteTransferAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.EventLogTransfer;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TMBDataSuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferOtherBankETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferV3ETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.utils.AccountNumberUtils;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.AMOUNT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BANK_TMB_VALIDATE_DATEFORMAT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CHARGE_TYPE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CHARGE_TYPE_V3;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CONTENT_TYPE_VALUE_WITH_UTF8;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FROM_ACCOUNT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FROM_ACCOUNT_V3;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_APP_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CONTENT_TYPE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_REQUEST_DATE_TIME;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_REQUEST_UUID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_SERVICE_NAME;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.POSTED_DATE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.POSTED_DATE_V3;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROMPTPAY_CONFIRM_GET;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.REQUEST_APP_ID_OCP_GATEWAY_VALUE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TO_ACCOUNT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TO_ACCOUNT_V3;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_CONFIRMATION_GET;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.utils.TransferUtils.circuitBreakException;

@Service
public class EteServiceImp extends EteBasicService implements EteService {
    private static final TMBLogger<EteServiceImp> logger = new TMBLogger<>(EteServiceImp.class);

    @Autowired
    private EteTransferValidationClient eteTransferValidationClient;
    @Autowired
    private EteGetAccountClient eteGetAccountClient;

    @Autowired
    private EteDepositTermClient eteDepositTermClient;

    @Autowired
    private EteTransferConfirmationClient eteTransferConfirmationClient;
    @Autowired
    private PromptpayValidationFeignClient promptpayValidationFeignClient;

    @Autowired
    private PromptpayConfirmationFeignClient promptpayConfirmationFeignClient;
    @Value("${bank.cd-bbl:02}")
    String bankCdBBL;
    @Value("${bank.cd-kbank:04}")
    String bankCdKBANK;
    @Value("${bank.cd-ktb:06}")
    String bankCdKTB;
    @Value("${bank.cd-scb:14}")
    String bankCdSCB;
    @Value("${bank.cd-bay:25}")
    String bankCdBAY;
    @Value("${bank.cd-gsb:30}")
    String bankCdGSB;

    @Autowired
    private ObjectMapper mapper;

    protected Map<String, String> getRequestParameter(String serviceName) {
        Map<String, String> cbsHeaderReqParameter = new HashMap<>();
        final String requestUid = UUID.randomUUID().toString();
        SimpleDateFormat date = new SimpleDateFormat(TransferServiceConstant.BANK_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        cbsHeaderReqParameter.put(HEADER_REQUEST_UUID, requestUid);
        cbsHeaderReqParameter.put(HEADER_REQUEST_DATE_TIME, requestDateTime);
        cbsHeaderReqParameter.put(HEADER_CONTENT_TYPE, TransferServiceConstant.CONTENT_TYPE_VALUE);
        cbsHeaderReqParameter.put(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        cbsHeaderReqParameter.put(HEADER_SERVICE_NAME, serviceName);
        return cbsHeaderReqParameter;
    }

    private JSONObject getAccountParameter(String toAccountNo, String toAccountType) throws JSONException {
        JSONObject account = new JSONObject();
        account.put(TransferServiceConstant.ACCOUNT_NO, toAccountNo);
        account.put(TransferServiceConstant.ACCOUNT_TYPE, toAccountType);
        return account;
    }

    private JSONObject getAccountParameterV3(String toAccountNo, String toAccountType, String financialId) throws JSONException {
        JSONObject account = new JSONObject();
        account.put(TransferServiceConstant.ACCOUNT_NO_V3, toAccountNo);
        account.put(TransferServiceConstant.ACC_TYPE, toAccountType);
        account.put(TransferServiceConstant.FINANCIAL_ID, financialId);
        return account;
    }

    public FundTransferOwnTMBETESuccess validateFundTransfer(String toAccountNo, String fromAccountNo, String depositNo, BigDecimal amount, String crrDate) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toAccType = AccountNumberUtils.getAccountTypeByAccountNo(toAccountNo);
        String fromAccType = AccountNumberUtils.getAccountTypeByAccountNo(fromAccountNo);
        String toAccountNoAfterCheck = AccountNumberUtils.getToAccountNoOfNormalOrTD(toAccountNo, toAccType);
        String fromAccountNoAfterCheck = AccountNumberUtils.getFromAccountNoOfNormalOrTD(fromAccountNo, fromAccType, depositNo);
        Map<String, String> cbsHeaderReqParameter = getRequestParameter(TransferServiceConstant.TRANSFER_VALIDATION_GET);
        try {
            JSONObject bodyReqParameter = new JSONObject();
            bodyReqParameter.put(FROM_ACCOUNT, getAccountParameter(fromAccountNoAfterCheck, fromAccType));
            bodyReqParameter.put(TO_ACCOUNT, getAccountParameter(toAccountNoAfterCheck, toAccType));
            bodyReqParameter.put(AMOUNT, amount);
            bodyReqParameter.put(CHARGE_TYPE, TransferServiceConstant.ALPHABET_I);
            bodyReqParameter.put(POSTED_DATE, crrDate);
            return super.request(() -> eteTransferValidationClient.getFundTransferValidation(bodyReqParameter.toString(), cbsHeaderReqParameter), null, FundTransferOwnTMBETESuccess.class);
        } catch (JSONException e) {
            logger.error("getFee from ETE Exception error :", e);
        } catch (CallNotPermittedException ce) {
            logger.error("validate On-us CallNotPermittedException error : {}", ce);
            throw circuitBreakException();
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
    }

    public FundTransferOwnTMBETESuccess validateFundTransferV3(String toAccountNo,
                                                               String fromAccountNo,
                                                               String toFinancialId,
                                                               String fromFinancialId,
                                                               String depositNo,
                                                               BigDecimal amount,
                                                               String crrDate) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toAccType = AccountNumberUtils.getAccountTypeByAccountNo(toAccountNo);
        String fromAccType = AccountNumberUtils.getAccountTypeByAccountNo(fromAccountNo);
        String toAccountNoAfterCheck = AccountNumberUtils.getToAccountNoOfNormalOrTD(toAccountNo, toAccType);
        String fromAccountNoAfterCheck = AccountNumberUtils.getFromAccountNoOfNormalOrTD(fromAccountNo, fromAccType, depositNo);
        Map<String, String> cbsHeaderReqParameter = getRequestParameter(TransferServiceConstant.TRANSFER_VALIDATION_GET);
        try {
            JSONObject bodyReqParameter = new JSONObject();
            bodyReqParameter.put(FROM_ACCOUNT_V3, getAccountParameterV3(fromAccountNoAfterCheck, fromAccType, fromFinancialId));
            bodyReqParameter.put(TO_ACCOUNT_V3, getAccountParameterV3(toAccountNoAfterCheck, toAccType, toFinancialId));
            bodyReqParameter.put(AMOUNT, amount);
            bodyReqParameter.put(CHARGE_TYPE_V3, TransferServiceConstant.ALPHABET_I);
            bodyReqParameter.put(POSTED_DATE_V3, crrDate);
            return super.request(() -> eteTransferValidationClient.getFundTransferValidationV3(bodyReqParameter.toString(), cbsHeaderReqParameter), null, FundTransferOwnTMBETESuccess.class);
        } catch (JSONException e) {
            logger.error("getFee from ETE Exception error :", e);
        } catch (CallNotPermittedException ce) {
            logger.error("validate On-us CallNotPermittedException error : {}", ce);
            throw circuitBreakException();
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
    }

    public EteDepositTermResponse getDepositWithdrawalInfo(V1OnUsValidateRequest reqBody) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        Map<String, String> headerReqParameter = new HashMap<>();
        final String requestUid = UUID.randomUUID().toString();
        SimpleDateFormat date = new SimpleDateFormat(TransferServiceConstant.BANK_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        headerReqParameter.put(HEADER_REQUEST_UUID, requestUid);
        headerReqParameter.put(HEADER_REQUEST_DATE_TIME, requestDateTime);
        headerReqParameter.put(HEADER_CONTENT_TYPE, TransferServiceConstant.CONTENT_TYPE_VALUE);
        headerReqParameter.put(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        headerReqParameter.put(HEADER_SERVICE_NAME, TransferServiceConstant.TRANSFER_TD_VALIDATION_GET);
        try {
            JSONObject bodyReqParam = new JSONObject();
            bodyReqParam.put(TransferServiceConstant.ACCOUNT_ID, reqBody.getFromAccountNo());
            bodyReqParam.put(TransferServiceConstant.DEPOSIT_NO, reqBody.getDepositNo());
            bodyReqParam.put(TransferServiceConstant.AMOUNTS, reqBody.getAmount());
            return super.request(() -> eteDepositTermClient.getDepositWithdrawalInfo(bodyReqParam.toString(), headerReqParameter), null, EteDepositTermResponse.class);
        } catch (JSONException e) {
            logger.error("getTdWithDraw error : {}", e);
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
    }

    public EteTransferAccount getDepositAccount(String toAccountNo, String crmId, String correlationId, String toAccType) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        Map<String, String> headerReqParameter = new HashMap<>();
        final String requestUid = UUID.randomUUID().toString();
        SimpleDateFormat date = new SimpleDateFormat(TransferServiceConstant.BANK_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));

        headerReqParameter.put(HEADER_CONTENT_TYPE, TransferServiceConstant.CONTENT_TYPE_VALUE);
        headerReqParameter.put(HEADER_REQUEST_UUID, requestUid);
        headerReqParameter.put(HEADER_REQUEST_DATE_TIME, requestDateTime);
        headerReqParameter.put(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        headerReqParameter.put(HEADER_SERVICE_NAME, TransferServiceConstant.GET_ACCOUNT_SERVICE_NAME);
        headerReqParameter.put(HEADER_CORRELATION_ID, correlationId);
        headerReqParameter.put(HEADER_CRM_ID, crmId);
        try {
            JSONObject bodyReqParam = new JSONObject();
            String toAccount10Digits = toAccountNo.substring(0, 10);
            bodyReqParam.put(TransferServiceConstant.ACCOUNT_ID, toAccount10Digits);
            bodyReqParam.put(TransferServiceConstant.ACC_TYPE, toAccType);
            EteDepositAccount eteDepositAccount = super.request(() -> eteGetAccountClient.getDepositAccount(headerReqParameter, bodyReqParam.toString()), null, EteDepositAccount.class);
            return eteDepositAccount.getAccount();
        } catch (JSONException | NullPointerException e) {
            logger.error("getAccount from ETE Exception error : {}  ", e);
        } catch (CallNotPermittedException ce) {
            logger.error("getAccount from ETE CallNotPermittedException error : {}  ", ce);
            throw circuitBreakException();
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
    }

    private HttpHeaders setUpRequestHeader(String serviceName) {
        HttpHeaders headers = new HttpHeaders();
        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));

        headers.set(HEADER_REQUEST_UUID, UUID.randomUUID().toString());
        headers.set(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        headers.set(HEADER_REQUEST_DATE_TIME, requestDateTime);
        headers.set(HEADER_CONTENT_TYPE, CONTENT_TYPE_VALUE_WITH_UTF8);
        headers.set(HEADER_SERVICE_NAME, serviceName);

        return headers;
    }

    public TMBDataSuccess confirmTransfer(TransferETERequest transferETERequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        HttpHeaders requestHeader = setUpRequestHeader(TRANSFER_CONFIRMATION_GET);
        try {
            FundTransferOwnTMBETESuccess fundTransferOwnTMBETESuccess = super.request(() -> eteTransferConfirmationClient
                    .confirmTransfer(transferETERequest, requestHeader), null, FundTransferOwnTMBETESuccess.class);
            return fundTransferOwnTMBETESuccess.getData();
        } catch (CallNotPermittedException ce) {
            logger.error("getAccount from ETE CallNotPermittedException error : {}  ", ce);
            throw circuitBreakException();
        }
    }

    public TMBDataSuccess confirmTransferV3(TransferV3ETERequest transferETERequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        HttpHeaders requestHeader = setUpRequestHeader(TRANSFER_CONFIRMATION_GET);
        try {
            FundTransferOwnTMBETESuccess fundTransferOwnTMBETESuccess = super.request(() -> eteTransferConfirmationClient
                    .confirmTransferV3(transferETERequest, requestHeader), null, FundTransferOwnTMBETESuccess.class);
            return fundTransferOwnTMBETESuccess.getData();
        } catch (CallNotPermittedException ce) {
            logger.error("getAccount from ETE CallNotPermittedException error : {}  ", ce);
            throw circuitBreakException();
        }
    }

    @Override
    public TPromptPayVerifyETEResponse validateTransferPromptPay(TransferOtherBankETERequest transferOtherBankETERequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        long startTime = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));

        headers.set(HEADER_REQUEST_UUID, UUID.randomUUID().toString());
        headers.set(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        headers.set(HEADER_REQUEST_DATE_TIME, requestDateTime);
        headers.set(HEADER_SERVICE_NAME, "promptpay-credittransfer-inq");

        EventLogTransfer eventLog = new EventLogTransfer();
        eventLog.setEventName(TransferServiceConstant.TRANSFER_VALIDATE_OTHER_BANK);
        eventLog.setBankCode(transferOtherBankETERequest.getReceiver().getBankCode());

        TPromptPayETEResponse tPromptPayETEResponse;
        try {
            if (bankCdBBL.equals(transferOtherBankETERequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_BBL);
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptPayValidateToBBL(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdBAY.equals(transferOtherBankETERequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_BAY);
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptPayValidateToBAY(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdGSB.equals(transferOtherBankETERequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_GSB);
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptPayValidateToGSB(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdKBANK.equals(transferOtherBankETERequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_KBANK);
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptPayValidateToKBANK(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdKTB.equals(transferOtherBankETERequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_KTB);
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptPayValidateToKTB(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdSCB.equals(transferOtherBankETERequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_SCB);
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptPayValidateToSCB(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            } else {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_OTHER);
                eventLog.setFlexibleValue(transferOtherBankETERequest.getReceiver().getProxyValue());
                tPromptPayETEResponse = super.request(() -> promptpayValidationFeignClient.transferPromptpayValidationToOther(headers, transferOtherBankETERequest), eventLog, TPromptPayETEResponse.class);
            }
            String overrideFromAccountName = transferOtherBankETERequest.getSender().getAccountName();
            tPromptPayETEResponse.getData().getSender().setAccountName(overrideFromAccountName);

            eventLog.setStatusCode(tPromptPayETEResponse.getStatus().getCode());
            eventLog.setDescription(tPromptPayETEResponse.getStatus().getDescription());
            logger.event(buildEventLog(eventLog, startTime, TransferServiceConstant.PROMPTPAY_VALIDATE));

            return tPromptPayETEResponse.getData();

        } catch (TMBCommonException | TMBCustomCommonExceptionWithResponse e) {
            logger.event(this.buildEventLog(eventLog, startTime, TransferServiceConstant.PROMPTPAY_VALIDATE));
            throw e;
        } catch (CallNotPermittedException ce) {
            logger.event(this.circuitBreakerErrorEventLog(eventLog, startTime, TransferServiceConstant.PROMPTPAY_VALIDATE));
            logger.error("validateTransPromptPayToETE CallNotPermittedException error : {}", ce);
            throw circuitBreakException();
        }
    }

    @Override
    public TPromptPayVerifyETEResponse confirmTransferPromptPay(TPromptPayETERequest eteRequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        long startTime = System.currentTimeMillis();

        HttpHeaders headers = new HttpHeaders();
        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        headers.set(HEADER_REQUEST_UUID, UUID.randomUUID().toString());
        headers.set(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        headers.set(HEADER_REQUEST_DATE_TIME, requestDateTime);
        headers.set(HEADER_SERVICE_NAME, PROMPTPAY_CONFIRM_GET);

        EventLogTransfer eventLog = new EventLogTransfer();
        eventLog.setEventName(TransferServiceConstant.TRANSFER_CONFIRM_OTHER_BANK);
        eventLog.setBankCode(eteRequest.getReceiver().getBankCode());

        TPromptPayETEResponse tPromptPayETEResponse;
        try {
            if (bankCdBBL.equals(eteRequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_BBL);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToBBL(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdBAY.equals(eteRequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_BAY);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToBAY(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdGSB.equals(eteRequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_GSB);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToGSB(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdKBANK.equals(eteRequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_KBANK);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToKBANK(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdKTB.equals(eteRequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_KTB);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToKTB(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            } else if (bankCdSCB.equals(eteRequest.getReceiver().getBankCode())) {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_SCB);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToSCB(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            } else {
                eventLog.setBankShortName(TransferServiceConstant.CIRCUIT_BREAKER_OTHER);
                tPromptPayETEResponse = super.request(() -> promptpayConfirmationFeignClient.transferPromptpayConfirmationToOther(headers, eteRequest), eventLog, TPromptPayETEResponse.class);
            }

            eventLog.setStatusCode(tPromptPayETEResponse.getStatus().getCode());
            eventLog.setDescription(tPromptPayETEResponse.getStatus().getDescription());
            logger.event(buildEventLog(eventLog, startTime, TransferServiceConstant.PROMPTPAY_CONFIRM));

            return tPromptPayETEResponse.getData();

        } catch (TMBCommonException e) {
            logger.event(this.buildEventLog(eventLog, startTime, TransferServiceConstant.PROMPTPAY_CONFIRM));
            throw e;
        } catch (CallNotPermittedException ce) {
            logger.event(circuitBreakerErrorEventLog(eventLog, startTime, TransferServiceConstant.PROMPTPAY_CONFIRM));
            logger.error("confirmTransPromptPayToETE CallNotPermittedException error : {}", ce);
            throw circuitBreakException();
        }
    }

    public TTBEventLog buildEventLog(EventLogTransfer eventLog, long startTime, String path) {
        TTBEventLog ttbEventLog = new TTBEventLog(
                TTBEventMonitoringType.BUSINESS,
                path,
                TTBEventStatus.SUCCESS,
                HttpStatus.OK.value(),
                (int) (System.currentTimeMillis() - startTime)
        );
        ttbEventLog.setParameters(mapper.convertValue(eventLog, new TypeReference<>() {
        }));
        return ttbEventLog;
    }

    private TTBEventLog circuitBreakerErrorEventLog(EventLogTransfer eventLog, long startTime, String path){
        eventLog.setStatusCode(ResponseCode.CIRCUIT_BREAK_ERROR.getCode());
        eventLog.setDescription(TransferServiceConstant.FAILED_CIRCUIT_BREAKER);
        return this.buildEventLog(eventLog, startTime, path);
    }

    public EteTransferAccount getDepositAccount(String toAccountNo, String crmId, String correlationId, String toAccType, String financialId) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        Map<String, String> headerReqParameter = new HashMap<>();

        headerReqParameter.put(HEADER_SERVICE_NAME, TransferServiceConstant.GET_ACCOUNT_SERVICE_NAME);
        headerReqParameter.put(HEADER_CONTENT_TYPE, TransferServiceConstant.CONTENT_TYPE_VALUE);
        headerReqParameter.put(HEADER_APP_ID, REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        headerReqParameter.put(HEADER_CORRELATION_ID, correlationId);
        headerReqParameter.put(HEADER_CRM_ID, crmId);
        final String requestUid = UUID.randomUUID().toString();
        headerReqParameter.put(HEADER_REQUEST_UUID, requestUid);
        SimpleDateFormat date = new SimpleDateFormat(TransferServiceConstant.BANK_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        headerReqParameter.put(HEADER_REQUEST_DATE_TIME, requestDateTime);
        try {
            JSONObject bodyReqParam = new JSONObject();
            bodyReqParam.put(TransferServiceConstant.FINANCIAL_ID, financialId);
            bodyReqParam.put(TransferServiceConstant.ACC_TYPE, toAccType);
            String toAccount10Digits = toAccountNo.substring(0, 10);
            bodyReqParam.put(TransferServiceConstant.ACCOUNT_ID, toAccount10Digits);
            EteDepositAccount eteDepositAccount = super.request(() -> eteGetAccountClient.getDepositAccount
                    (headerReqParameter, bodyReqParam.toString()), null, EteDepositAccount.class);
            return eteDepositAccount.getAccount();
        } catch (JSONException | NullPointerException e) {
            logger.error("getAccount from ETE Exception error : {}  ", e);
        } catch (CallNotPermittedException ce) {
            logger.error("getAccount from ETE CallNotPermittedException error : {}  ", ce);
            throw circuitBreakException();
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
    }

}

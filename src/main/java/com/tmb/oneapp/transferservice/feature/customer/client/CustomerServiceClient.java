package com.tmb.oneapp.transferservice.feature.customer.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyRequest;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyResponse;
import com.tmb.oneapp.transferservice.model.AccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.DailyUsageData;
import com.tmb.oneapp.transferservice.model.customer.PaymentAccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.customer.PinFreeCountData;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.feature.notification.model.V1ENotificationSettingResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.PathVariable;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;


@FeignClient(name = "customer-service-client", url = "${customer-service.url}",configuration = FeignCommonConfig.class)
public interface CustomerServiceClient {
    @GetMapping(value = "/apis/customers/crmprofile", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<V1CrmProfile>> fetchCustomerCrmProfile(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId);

    @GetMapping(value = "/apis/customers/settings/notification/email", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<V1ENotificationSettingResponse>> getENotificationSetting(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId);

    @GetMapping(value = "/apis/customers/kyc/cache", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> fetchCustomerKYC(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId);

    @PostMapping(value = "/apis/customers/daily-usage", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> updateDailyUsage(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody DailyUsageData dailyUsageData

    );

    @PostMapping(value = "/apis/customers/pin-free-count", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> updatePinFreeCount(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody PinFreeCountData pinFreeCountData
    );

    @GetMapping(value = "/apis/customers/{deviceId}")
    ResponseEntity<TmbOneServiceResponse<CustomerProfileStatus>> getCustomerProfileByDeviceId(
            @PathVariable("deviceId") String deviceId);

    @PostMapping(value = "/apis/customers/update/payment-accumulate-usage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> updatePaymentAccumulateUsageAmount(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody PaymentAccumulateUsageRequest request
    );
    @PostMapping(value = "/v1/customers-service/common-fr/verify-fr", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CommonFRVerifyResponse>> getCommonFRByUUID(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = IP_ADDRESS) String ipAddress,
            @RequestBody CommonFRVerifyRequest commonFRVerifyRequest);

    @PatchMapping(value = {"/apis/customers/accumulate"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> updateUsageAccumulation(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody AccumulateUsageRequest accumulateUsageRequest
    );
}

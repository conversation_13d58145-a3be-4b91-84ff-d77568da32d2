package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tmb.oneapp.transferservice.model.FeignAdditionalStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FundTransferOwnTMBETESuccess extends EteBasicResponse {
    private TMBDataSuccess data;
    @JsonAlias("error")
    private List<FeignAdditionalStatus> additionalStatus;
}


package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@ToString
@JsonPropertyOrder({ "proxyType", "proxyValue", "accountId", "accountType", "accountName", "accountDisplayName",
		"accountLength", "taxId", "bankCode" })
public class Receiver {
	@JsonProperty("proxyType")
	private String proxyType;
	@JsonProperty("proxyValue")
	private String proxyValue;
	@JsonProperty("accountId")
	private String accountId;
	@JsonProperty("accountType")
	private String accountType;
	@JsonProperty("accountName")
	private String accountName;
	@JsonProperty("accountDisplayName")
	private String accountDisplayName;
	@JsonProperty("accountLength")
	private Integer accountLength;
	@JsonProperty("taxId")
	private String taxId;
	@JsonProperty("bankCode")
	private String bankCode;
	private String id;
	private String itmxFlag;
	private String creditFlag;
	private String category;
	private String requiredReference2Flag;
	private String scanOnly;
	private String customerTypeFlag;
	private String compCode;
}

package com.tmb.oneapp.transferservice.feature.account.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TDResponse {
    @JsonProperty("accountNo")
    private String accountNo;
    @JsonProperty("accountType")
    private String accountType;
    @JsonProperty("accountBalance")
    private String accountBalance;
    @JsonProperty("branchNameTh")
    private String branchNameTh;
    @JsonProperty("branchNameEn")
    private String branchNameEn;
    @JsonProperty("accountName")
    private String accountName;
    @JsonProperty("ledgerBalance")
    private String ledgerBalance;
    @JsonProperty("availableBalance")
    private String availableBalance;
    @JsonProperty("accruedInterest")
    private String accruedInterest;
    @JsonProperty("accountStatus")
    private String accountStatus;
    @JsonProperty("productNameTh")
    private String productNameTh;
    @JsonProperty("productNameEn")
    private String productNameEn;
    @JsonProperty("accountDetailView")
    private String accountDetailView;
    @JsonProperty("iconId")
    private String iconId;
    @JsonProperty("odLimtAmmout")
    private String odLimtAmmout;
    @JsonProperty("accumulatedIntPayable")
    private String accumulatedIntPayable;
    @JsonProperty("termDeposit")
    private String termDeposit;

}
package com.tmb.oneapp.transferservice.feature.activitylog.model;

import org.springframework.http.HttpHeaders;

public class OnUsConfirmationActivityLog extends TransferActivityLog {

    protected OnUsConfirmationActivityLog(HttpHeaders headers, String correlationId) {
        super(headers, correlationId);
    }

    protected OnUsConfirmationActivityLog(HttpHeaders headers) {
        super(headers);
    }
}

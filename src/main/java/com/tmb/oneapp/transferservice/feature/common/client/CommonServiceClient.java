package com.tmb.oneapp.transferservice.feature.common.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SEARCH_PARAM_CONSTANT;

@FeignClient(name = "common-service-client", url = "${common-service.url}",configuration = FeignCommonConfig.class)
public interface CommonServiceClient {

    @GetMapping(value = "/v1/common-service/configuration", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<TmbOneServiceResponse<List<CommonData>>> getCommonConfiguration(
            @RequestParam(SEARCH_PARAM_CONSTANT) String searchType,
            @RequestHeader HttpHeaders headers);

}

package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EteDepositTermResponse extends EteBasicResponse {
    @JsonProperty("additional_status")
    private List<AdditionalStatus> additionalStatus = null;
    @JsonProperty("account")
    private Account account;
    private List<EteError> errors;

    public static class Account {

        @JsonProperty("withdrawal_info")
        private WithdrawalInfo withdrawalInfo;

        @JsonProperty("withdrawal_info")
        public WithdrawalInfo getWithdrawalInfo() {
            return withdrawalInfo;
        }

        @JsonProperty("withdrawal_info")
        public void setWithdrawalInfo(WithdrawalInfo withdrawalInfo) {
            this.withdrawalInfo = withdrawalInfo;
        }

    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AdditionalStatus {

        @JsonProperty("status_code")
        private String statusCode;
        @JsonProperty("server_status_code")
        private String serverStatusCode;
        @JsonProperty("severity")
        private String severity;
        @JsonProperty("status_desc")
        private String statusDesc;
    }

    @Setter
    @Getter
    public static class WithdrawalInfo {

        @JsonProperty("outstanding_balances")
        private BigDecimal outstandingBalances;
        @JsonProperty("tax_amounts")
        private BigDecimal taxAmounts;
        @JsonProperty("penalty_amounts")
        private BigDecimal penaltyAmounts;
        @JsonProperty("penalty_rate")
        private String penaltyRate;
        @JsonProperty("total_interest")
        private BigDecimal totalInterest;
        @JsonProperty("interest_rate")
        private String interestRate;
        @JsonProperty("interest_amounts")
        private BigDecimal interestAmounts;
        @JsonProperty("anticipated_interest")
        private BigDecimal anticipatedInterest;
        @JsonProperty("product_type")
        private String productType;
        @JsonProperty("next_maturity_date")
        private String nextMaturityDate;
        @JsonProperty("next_interest_date")
        private String nextInterestDate;

    }

}

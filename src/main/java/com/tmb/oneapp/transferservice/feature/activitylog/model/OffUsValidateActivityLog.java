package com.tmb.oneapp.transferservice.feature.activitylog.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
public class OffUsValidateActivityLog extends TransferActivityLog {
    private String pinFreeFlag;
    private String ddpFlag;

    protected OffUsValidateActivityLog(HttpHeaders headers, String correlationId) {
        super(headers, correlationId);
    }

    protected OffUsValidateActivityLog(HttpHeaders headers) {
        super(headers);
    }

}

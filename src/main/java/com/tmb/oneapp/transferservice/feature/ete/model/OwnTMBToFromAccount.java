package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OwnTMBToFromAccount {
	@JsonProperty("accountNo")
	@JsonAlias({"accountNo ", "account_no"})
	private String accountNo;
	@JsonProperty("accountType")
	@JsonAlias("account_type")
	private String accountType;
	@JsonProperty("accountName")
	@JsonAlias("account_name")
	private String accountName;
	@JsonProperty("availBalance")
	@JsonAlias("avail_balance")
	private String availBalance;
	@JsonProperty("ledgerBalance")
	@JsonAlias("ledger_balance")
	private String ledgerBalance;

}

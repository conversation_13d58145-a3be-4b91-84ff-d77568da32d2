package com.tmb.oneapp.transferservice.feature.authen.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonAuthenVerifyRefRequest {

    @NotNull(message = "refId cannot be null")
    @NotEmpty(message = "refId cannot be empty string")
    @NotBlank(message = "refId cannot be blank")
    @Schema(description = "refId use for cache key", example = "88e24440-8f3b-11ee", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String refId;

    @NotNull(message = "flowName cannot be null")
    @NotEmpty(message = "flowName cannot be empty string")
    @NotBlank(message = "flowName cannot be blank")
    @Schema(description = "flowName for verify cache value", example = "Transfer", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String flowName;
}

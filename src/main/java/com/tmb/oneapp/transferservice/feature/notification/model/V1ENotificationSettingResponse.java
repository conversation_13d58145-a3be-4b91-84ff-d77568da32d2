package com.tmb.oneapp.transferservice.feature.notification.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1ENotificationSettingResponse {
    private boolean appLevelNotification;
    private boolean loginNotification;
    private boolean transactionNotification;
    private String loginNotificationDate;
    private String transactionNotificationDate;
}

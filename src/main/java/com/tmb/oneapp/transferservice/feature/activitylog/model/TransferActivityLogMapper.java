package com.tmb.oneapp.transferservice.feature.activitylog.model;

import com.tmb.oneapp.transferservice.model.CommonAuthenResult;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import com.tmb.oneapp.transferservice.utils.AccountNumberUtils;
import com.tmb.oneapp.transferservice.utils.NumberUtils;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_FCD_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_FCD_OWN_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OWN_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_FCD_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_FCD_OWN_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_VALIDATE_OWN_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BILL_PAYMENT_ACTIVITY_CONFIRM_STEP;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BILL_PAYMENT_ACTIVITY_VERIFY_STEP;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MOBILE_LENGTH;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_MOBILE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_TRANSFER_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_ACTIVITY_VERIFY_STEP;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_SHORT_NAME;

public class TransferActivityLogMapper {

    public static final TransferActivityLogMapper INSTANCE = new TransferActivityLogMapper();

    private TransferActivityLogMapper() {

    }

    public TransferActivityLog toTransferActivityLog(HttpHeaders headers, String toBankShortName, BigDecimal fee, TransferOtherBankValidateRequest request, VerifyTransactionResult verifyTransactionResult) {
        String activityTypeId = ACTIVITY_LOG_VALIDATE_OTHER_BANK_ACTIVITY_ID;
        if (TTB_BANK_CODE.equals(request.getToBankCode())) {
            activityTypeId = ACTIVITY_LOG_VALIDATE_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
        }
        CommonAuthenResult commonAuthenResult = Optional.ofNullable(verifyTransactionResult)
                .map(VerifyTransactionResult::commonAuthenResult)
                .orElse(null);

        OffUsValidateActivityLog offUsValidateActivityLog = new OffUsValidateActivityLog(headers);
        offUsValidateActivityLog.setActivityTypeId(activityTypeId);
        offUsValidateActivityLog.setFee(NumberUtils.insertCommas(fee));
        offUsValidateActivityLog.setToBankShortName(toBankShortName);
        offUsValidateActivityLog.setFlow(request.getFlow());
        offUsValidateActivityLog.setStep(BILL_PAYMENT_ACTIVITY_VERIFY_STEP);
        offUsValidateActivityLog.setFromAccount(request.getFromAccountNo());
        offUsValidateActivityLog.setToAccount(request.getToAccountNo());
        offUsValidateActivityLog.setAmount(NumberUtils.insertCommas(new BigDecimal(request.getAmount()).setScale(2, RoundingMode.DOWN)));
        offUsValidateActivityLog.setActivityDate(Long.toString(System.currentTimeMillis()));
        if (commonAuthenResult != null) {
            offUsValidateActivityLog.setPinFreeFlag(String.valueOf(commonAuthenResult.isPinFree()));
            offUsValidateActivityLog.setDdpFlag(Optional.ofNullable(commonAuthenResult.getIsForceFr())
                    .map(Object::toString)
                    .orElse("-"));
        }

        return offUsValidateActivityLog;
    }

    private String getOwnOrOtherTTBActivityIdValidate(boolean isTransferToOwnAcct, V1OnUsValidateRequest request) {
        if (AccountNumberUtils.isFcdAccount(request.getFromAccountNo())) {
            if (isTransferToOwnAcct) {
                return ACTIVITY_LOG_VALIDATE_FCD_OWN_ACTIVITY_ID;
            } else {
                return ACTIVITY_LOG_VALIDATE_FCD_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
            }
        } else {
            if (isTransferToOwnAcct) {
                return ACTIVITY_LOG_VALIDATE_OWN_ACTIVITY_ID;
            } else {
                return ACTIVITY_LOG_VALIDATE_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
            }
        }
    }

    public TransferActivityLog toTransferActivityLog(HttpHeaders headers, BigDecimal feeFromETE,
                                                     V1OnUsValidateRequest request,
                                                     boolean isTransferToOwnAccount,
                                                     VerifyTransactionResult verifyTransactionResult) {
        CommonAuthenResult commonAuthenResult = Optional.ofNullable(verifyTransactionResult)
                .map(VerifyTransactionResult::commonAuthenResult)
                .orElse(null);

        OnUsValidateActivityLog onUsValidateActivityLog = new OnUsValidateActivityLog(headers);
        onUsValidateActivityLog.setActivityTypeId(getOwnOrOtherTTBActivityIdValidate(isTransferToOwnAccount, request));
        onUsValidateActivityLog.setFlow(request.getFlow());
        onUsValidateActivityLog.setStep(TRANSFER_ACTIVITY_VERIFY_STEP);
        onUsValidateActivityLog.setFromAccount(request.getFromAccountNo());
        onUsValidateActivityLog.setToAccount(request.getToAccountNo());
        onUsValidateActivityLog.setToBankShortName(TTB_BANK_SHORT_NAME);
        onUsValidateActivityLog.setAmount(NumberUtils.insertCommas(request.getAmount()));
        onUsValidateActivityLog.setFee(NumberUtils.insertCommas(feeFromETE));
        onUsValidateActivityLog.setCurrency(request.getFromCurrency());
        if (commonAuthenResult != null) {
            onUsValidateActivityLog.setPinFreeFlag(String.valueOf(commonAuthenResult.isPinFree()));
            onUsValidateActivityLog.setDdpFlag(Optional.ofNullable(commonAuthenResult.getIsForceFr())
                    .map(Object::toString)
                    .orElse("-"));
        }
        return onUsValidateActivityLog;
    }

    public TransferActivityLog toTransferActivityLog(HttpHeaders headers, V1TransferData request, boolean isTransferToOwnAccount) {
        OnUsConfirmationActivityLog onUsConfirmationActivityLog = new OnUsConfirmationActivityLog(headers);
        onUsConfirmationActivityLog.setActivityTypeId(getOwnOrOtherTTBActivityIdConfirm(isTransferToOwnAccount, request));
        onUsConfirmationActivityLog.setFlow(request.getFlow());
        onUsConfirmationActivityLog.setStep("Confirm");
        onUsConfirmationActivityLog.setFromAccount(request.getFromAccount().getAccountNo());
        onUsConfirmationActivityLog.setToAccount(request.getToAccount().getAccountNo());
        onUsConfirmationActivityLog.setAmount(NumberUtils.insertCommas(request.getAmount()));
        onUsConfirmationActivityLog.setFee(NumberUtils.insertCommas(request.getFeeFromETE()));
        onUsConfirmationActivityLog.setRefNo(request.getTransactionReference());
        onUsConfirmationActivityLog.setCurrency(request.getFromAccount().getCurrency());
        return onUsConfirmationActivityLog;
    }

    public TransferActivityLog toTransferActivityLog(HttpHeaders headers, TPromptPayVerifyETEResponse cacheOtherBank) {
        OffUsConfirmationActivityLog offUsConfirmationActivityLog = new OffUsConfirmationActivityLog(headers);
        offUsConfirmationActivityLog.setActivityTypeId(cacheOtherBank.getReceiver().getProxyType(), cacheOtherBank.getReceiver().getBankCode(), cacheOtherBank.getPaymentCacheData().getQr());
        offUsConfirmationActivityLog.setStep(BILL_PAYMENT_ACTIVITY_CONFIRM_STEP);
        offUsConfirmationActivityLog.setFromAccount(cacheOtherBank.getSender().getAccountId());
        offUsConfirmationActivityLog.setFlow(cacheOtherBank.getPaymentCacheData().getFlow());
        offUsConfirmationActivityLog.setRefNo(cacheOtherBank.getTransactionReference());
        offUsConfirmationActivityLog.setToBankShortName(cacheOtherBank.getPaymentCacheData().getBankShortName());
        offUsConfirmationActivityLog.setAmount(NumberUtils.insertCommas(cacheOtherBank.getAmount()));
        offUsConfirmationActivityLog.setFee(cacheOtherBank.getFee() == null ? "0.00" : NumberUtils.insertCommas(cacheOtherBank.getFee()));
        offUsConfirmationActivityLog.setFieldByProxyType(cacheOtherBank.getReceiver().getProxyValue(), cacheOtherBank.getReceiver().getProxyType());
        boolean isPromptPayProxyType = !cacheOtherBank.getReceiver().getProxyType().equals(PROXY_TYPE_TRANSFER_OTHER_BANK);
        if (isPromptPayProxyType) {
            offUsConfirmationActivityLog.setToLinkedAccountNumber(cacheOtherBank.getReceiver().getAccountId());
        }
        return offUsConfirmationActivityLog;
    }

    public TransferActivityLog toTransferActivityLog(HttpHeaders headers, String toBankShortName, BigDecimal fee, TransferOtherBankValidateRequest request, boolean transferToTTBAcct, String eteAccountId, VerifyTransactionResult verifyTransactionResult) {
        CommonAuthenResult commonAuthenResult = Optional.ofNullable(verifyTransactionResult)
                .map(VerifyTransactionResult::commonAuthenResult)
                .orElse(null);
        PromptPayValidateActivityLog promptPayValidateActivityLog = new PromptPayValidateActivityLog(headers);
        promptPayValidateActivityLog.setFlow(request.getFlow());
        promptPayValidateActivityLog.setStep(BILL_PAYMENT_ACTIVITY_VERIFY_STEP);
        promptPayValidateActivityLog.setFromAccount(request.getFromAccountNo());
        promptPayValidateActivityLog.setToBankShortName(toBankShortName);
        String toProxyType = request.getToAccountNo().length() == MOBILE_LENGTH ? PROXY_TYPE_PROMPTPAY_BY_MOBILE : PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
        if (PROXY_TYPE_PROMPTPAY_BY_MOBILE.equals(toProxyType)) {
            promptPayValidateActivityLog.setToMobile(request.getToAccountNo());
        } else {
            promptPayValidateActivityLog.setToCitizenId(request.getToAccountNo());
        }
        promptPayValidateActivityLog.updateActivityTypeId(toProxyType, transferToTTBAcct, request.getQr());
        promptPayValidateActivityLog.setAmount(NumberUtils.insertCommas(new BigDecimal(request.getAmount())));
        promptPayValidateActivityLog.setFee(NumberUtils.insertCommas(fee));
        promptPayValidateActivityLog.setActivityDate(Long.toString(System.currentTimeMillis()));
        promptPayValidateActivityLog.setToLinkedAccountNumber(eteAccountId);
        if (commonAuthenResult != null) {
            promptPayValidateActivityLog.setPinFreeFlag(String.valueOf(commonAuthenResult.isPinFree()));
            promptPayValidateActivityLog.setDdpFlag(Optional.ofNullable(commonAuthenResult.getIsForceFr())
                    .map(Object::toString)
                    .orElse("-"));
        }
        return promptPayValidateActivityLog;
    }

    private String getOwnOrOtherTTBActivityIdConfirm(boolean isTransferToOwnAcct, V1TransferData request) {
        if(AccountNumberUtils.isFcdAccount(request.getFromAccount().getAccountNo())) {
            if (isTransferToOwnAcct) {
                return ACTIVITY_LOG_CONFIRM_FCD_OWN_ACTIVITY_ID;
            } else {
                return ACTIVITY_LOG_CONFIRM_FCD_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
            }
        } else {
            if (isTransferToOwnAcct) {
                return ACTIVITY_LOG_CONFIRM_OWN_ACTIVITY_ID;
            } else {
                return ACTIVITY_LOG_CONFIRM_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
            }
        }
    }
}

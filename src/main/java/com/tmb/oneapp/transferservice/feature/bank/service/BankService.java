package com.tmb.oneapp.transferservice.feature.bank.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.bank.client.BankServiceClient;
import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.transferservice.model.bank.BankInfoDataModel;
import com.tmb.oneapp.transferservice.service.V1OnUsTransferService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;

@Service
@RequiredArgsConstructor
public class BankService {
    private static final TMBLogger<V1OnUsTransferService> logger = new TMBLogger<>(V1OnUsTransferService.class);
    @Autowired
    private BankServiceClient bankServiceClient;

    public List<CategoryInfoDataModel> getCategories(String correlationId) throws TMBCommonException {
        try {
            HttpHeaders header = new HttpHeaders();
            header.set(HEADER_CORRELATION_ID, correlationId);
            ResponseEntity<TmbOneServiceResponse<List<CategoryInfoDataModel>>> res = bankServiceClient.getAllCategory(correlationId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
        } catch (FeignException ex) {
            logger.error("Error while fetching data from Common Config : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
        return new ArrayList<>();
    }

    public List<BankInfoDataModel> getBanks(String correlationId) throws TMBCommonException {
        try {
            HttpHeaders header = new HttpHeaders();
            header.set(HEADER_CORRELATION_ID, correlationId);
            ResponseEntity<TmbOneServiceResponse<List<BankInfoDataModel>>> res = bankServiceClient.getAllBankInfo(correlationId);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
        } catch (FeignException ex) {
            logger.error("Error while fetching data from Common Config : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
        return new ArrayList<>();
    }
}

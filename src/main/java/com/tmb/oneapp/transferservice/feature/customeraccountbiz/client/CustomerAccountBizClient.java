package com.tmb.oneapp.transferservice.feature.customeraccountbiz.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.customeraccountbiz.model.AccountSaving;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.customers.account.biz.service.name}", url = "${feign.customers.account.biz.service.url}")
public interface CustomerAccountBizClient {

    @GetMapping(value = "/v1/customer-account-biz/accounts-list", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<AccountSaving>> getAccountList(
            @RequestHeader(value = TransferServiceConstant.HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = TransferServiceConstant.HEADER_CRM_ID) String crmId,
            @RequestHeader(value = TransferServiceConstant.REFRESH_FLAG) Boolean refreshFlag,
            @RequestHeader(value = TransferServiceConstant.ALL_ACCOUNT_FLAG) Boolean allAccountFlag);
}

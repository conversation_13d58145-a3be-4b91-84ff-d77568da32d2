package com.tmb.oneapp.transferservice.feature.notification.model;

import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class V1TransferNotification extends V1NotificationBasic {
    private String accountNickname;
    private String fromAcctId;
    private String favoriteNickname;
    private String amount;
    private String fee;
    private String note;
    private String transactionRefNo;
    private String xCorrelationId;
    private String custFullNameTH;
    private String custFullNameEN;
    private String channelNameEN;
    private String channelNameTH;

    private String toAcctId;
    private String bankShortName;
    private String categoryEN;
    private String categoryTH;
    private String txnTypeEN;
    private String txnTypeTH;
    private String addDateTimeEN;
    private String addDateTimeTH;
    private String currency;

    public static String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }

        DecimalFormat commasFormat = new DecimalFormat("#,##0.00");
        commasFormat.setRoundingMode(RoundingMode.DOWN);

        return commasFormat.format(number);
    }

    public void setCustomerName(String firstNameEn, String lastNameEn, String fistNameTh, String lastNameTh) {
        this.custFullNameEN = StringUtils.isBlank(lastNameEn) ? firstNameEn : firstNameEn + " " + lastNameEn;
        this.custFullNameTH = StringUtils.isBlank(lastNameTh) ? fistNameTh : fistNameTh + " " + lastNameTh;
    }

    protected void setAmount(String amount) {
        if (amount != null) {
            this.amount = insertCommas(new BigDecimal(amount));
        }
    }

    protected void setFee(String fee) {
        if (fee != null) {
            this.fee = insertCommas(new BigDecimal(fee));
        }
    }

    public void setFavoriteNickname(String favoriteNickname) {
        this.favoriteNickname = StringUtils.isBlank(favoriteNickname) ? "-" : favoriteNickname;
    }

    public void setNote(String note) {
        this.note = StringUtils.isBlank(note) ? "-" : note;
    }

    public void setChannelName(String channelNameEn, String channelNameTh) {
        this.channelNameEN = channelNameEn;
        this.channelNameTH = channelNameTh;
    }

    public void setCategoryENAndTH(String categoryId, CategoryInfoDataModel categoryInfo) {
        if (org.springframework.util.StringUtils.hasText(categoryId)) {
            this.categoryEN = categoryInfo.getCategoryNameEn();
            this.categoryTH = categoryInfo.getCategoryNameTh();
        } else {
            this.categoryEN = "-";
            this.categoryTH = "-";
        }
    }

}

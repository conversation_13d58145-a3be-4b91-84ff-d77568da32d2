package com.tmb.oneapp.transferservice.feature.financiallog.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.feature.activitylog.service.V1ActivityLogService;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.financiallog.model.ExchangeTransactionLog;
import com.tmb.oneapp.transferservice.feature.financiallog.model.FinRequest;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.transfer.TransferActivities;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_CURRENT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_SAVING;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;

@Service
public class FinancialLogService {

    private static final TMBLogger<V1ActivityLogService> logger = new TMBLogger<>(V1ActivityLogService.class);

    @Value("${oneapp.customer.financial-log.topic-name}")
    private String topicNameFinancialLogOutput;

    @Value("${oneapp.customer.transaction-log.topic-name}")
    private String topicNameTransactionLogOutput;

    @Value("${oneapp.customer.exchange-transaction-log-name}")
    private String topicNameExChangeTransactionLogOutput;
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private CustomerService customerService;


    @LogAround
    @Async
    public void saveLogFinancialAndTransactionEvent(String correlationId, FinRequest finRequestLog, TransferActivities transactionActivitiesLog) {
        try {
            logger.info("start publish log refId: {}, correlationId : {}", finRequestLog.getReferenceID(), correlationId);
            transactionActivitiesLog.setActivityIdNew(finRequestLog.getActivityTypeIdNew());
            String financialLog = TMBUtils.convertJavaObjectToString(finRequestLog);
            String transactionLog = TMBUtils.convertJavaObjectToString(transactionActivitiesLog);
            kafkaProducerService.sendMessageAsync(topicNameFinancialLogOutput, financialLog);
            kafkaProducerService.sendMessageAsync(topicNameTransactionLogOutput, transactionLog);
        } catch (JsonProcessingException ex) {
            logger.error("cannot parse json : {}", ex);
        }
    }

    @LogAround
    @Async
    public void saveLogFinancialAndTransactionEvent(String correlationId, FinRequest finRequestLog, TransferActivities transactionActivitiesLog,
                                                    boolean isFcd, V1TransferData cacheOnUs, String sameOwner) {
        try {
            logger.info("start publish log refId: {}, correlationId : {}", finRequestLog.getReferenceID(), correlationId);
            transactionActivitiesLog.setActivityIdNew(finRequestLog.getActivityTypeIdNew());
            String financialLog = TMBUtils.convertJavaObjectToString(finRequestLog);
            String transactionLog = TMBUtils.convertJavaObjectToString(transactionActivitiesLog);
            ExchangeTransactionLog exchangeTransactionLog = composeExchangeTransactionRequest(
                    finRequestLog,
                    new Date(Long.parseLong(transactionActivitiesLog.getTransactionDate())),
                    isFcd,
                    cacheOnUs,
                    sameOwner);

            kafkaProducerService.sendMessageAsync(topicNameFinancialLogOutput, financialLog);
            kafkaProducerService.sendMessageAsync(topicNameTransactionLogOutput, transactionLog);
            kafkaProducerService.sendMessageAsync(topicNameExChangeTransactionLogOutput,TMBUtils.convertJavaObjectToString(exchangeTransactionLog));
        } catch (JsonProcessingException | TMBCommonException ex) {
            logger.error("cannot parse json : {}", ex);
        }
    }

    private ExchangeTransactionLog composeExchangeTransactionRequest(FinRequest finRequest, Date date,
                                                                     boolean isTransferToFcdAccount,
                                                                     V1TransferData cacheOnUs,
                                                                     String sameOwner) throws TMBCommonException {
        String amountCcy = finRequest.getTxnAmount();
        BigDecimal exchangeRate = new BigDecimal("0.00");

        if (cacheOnUs != null && cacheOnUs.getFxRate() != null && cacheOnUs.getUnitCurrency() != null) {
            exchangeRate = new BigDecimal(cacheOnUs.getFxRate())
                    .divide(new BigDecimal(cacheOnUs.getUnitCurrency()), 5, RoundingMode.DOWN)
                    .stripTrailingZeros();
        }

        CustomerKYCResponse customerKYCResponse = customerService.getCustomerKyc(finRequest.getCrmId(), finRequest.getReferenceID());
        String title = "นาย";
        if(!"M".equals(customerKYCResponse.getGender())){
            title = "นาง";
        }
        return new ExchangeTransactionLog()
                .setReferenceId(finRequest.getReferenceID())
                .setCrmId(finRequest.getCrmId())
                .setTransactionDate(date)
                .setTransactionType(isTransferToFcdAccount ? "buy" : "sell")
                .setTransactionAmountCcy(new BigDecimal(amountCcy).setScale(2, RoundingMode.DOWN))
                .setTransactionAmountThb(cacheOnUs.getAmountTHB())
                .setExchangeRate(exchangeRate)
                .setFromAccountNo(finRequest.getFromAccNo())
                .setFromAccountCcy(finRequest.getFinFlexValues1())
                .setFromAccountName(finRequest.getFromAccName())
                .setFromAccountType(mapAccountTypeForTransactionLog(finRequest.getFromAccType()))
                .setToAccountNo(finRequest.getToAccNo())
                .setToAccountCcy(finRequest.getFinFlexValues2())
                .setToAccountType(mapAccountTypeForTransactionLog(finRequest.getToAccType()))
                .setFlowName("TRANSFER")
                .setCustomerNameTh(title + " " +
                        customerKYCResponse.getCustomerFirstNameTh() + " " +
                        customerKYCResponse.getCustomerLastNameTh())
                .setToAcctName(finRequest.getToAccName())
                .setSameOwner(sameOwner);

    }

    private String mapAccountTypeForTransactionLog(String accountType) {
        return switch (StringUtils.trimToEmpty(accountType)) {
            case ACCOUNT_TYPE_SAVING -> "SA";
            case ACCOUNT_TYPE_CURRENT -> "CA";
            case ACCOUNT_TYPE_TERM_DEPOSIT -> "TD";
            default -> null;
        };
    }


}

package com.tmb.oneapp.transferservice.feature.activitylog.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public abstract class TransferActivityLog extends BaseActivityEvent {
    protected String flow;
    protected String step;
    protected String fromAccount;
    protected String toBankShortName;
    protected String amount;
    protected String fee;
    protected String refNo;

    protected String toAccount;
    protected String toMobile;
    protected String toCitizenId;

    protected String toLinkedAccountNumber;
    protected String currency;

    protected TransferActivityLog(HttpHeaders headers, String correlationId) {
        super(headers, correlationId);
    }

    protected TransferActivityLog(HttpHeaders headers) {
        super(headers);
    }
}

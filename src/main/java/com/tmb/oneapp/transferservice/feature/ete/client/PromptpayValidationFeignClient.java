package com.tmb.oneapp.transferservice.feature.ete.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETEResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferOtherBankETERequest;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;



@FeignClient(name = "ete-promptpay-validate-service", url = "${ete.promptpay.validate.url}",configuration = FeignCommonConfig.class)
public interface PromptpayValidationFeignClient {


	@CircuitBreaker(name = "transferPromptpayValidationToOther")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptpayValidationToOther(@RequestHeader HttpHeaders headers,
																 @RequestBody TransferOtherBankETERequest body);
	@CircuitBreaker(name = "transferPromptpayValidationToBBL")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptPayValidateToBBL(@RequestHeader HttpHeaders headers,
																		 @RequestBody TransferOtherBankETERequest body);

	@CircuitBreaker(name = "transferPromptpayValidationToKBANK")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptPayValidateToKBANK(@RequestHeader HttpHeaders headers,
																		   @RequestBody TransferOtherBankETERequest body);

	@CircuitBreaker(name = "transferPromptpayValidationToKTB")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptPayValidateToKTB(@RequestHeader HttpHeaders headers,
																		 @RequestBody TransferOtherBankETERequest body);

	@CircuitBreaker(name = "transferPromptpayValidationToSCB")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptPayValidateToSCB(@RequestHeader HttpHeaders headers,
																		 @RequestBody TransferOtherBankETERequest body);

	@CircuitBreaker(name = "transferPromptpayValidationToBAY")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptPayValidateToBAY(@RequestHeader HttpHeaders headers,
																		 @RequestBody TransferOtherBankETERequest body);

	@CircuitBreaker(name = "transferPromptpayValidationToGSB")
	@PostMapping(value = "${ete.promptpay.validate.path}", consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
	@ResponseBody
	ResponseEntity<TPromptPayETEResponse> transferPromptPayValidateToGSB(@RequestHeader HttpHeaders headers,
																		 @RequestBody TransferOtherBankETERequest body);
}

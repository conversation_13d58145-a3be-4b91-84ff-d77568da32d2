package com.tmb.oneapp.transferservice.feature.customerstransaction.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TriggerCacheRequest {
	@JsonProperty("crm_id")
	@Schema(example = "CRM ID of customer", requiredMode = Schema.RequiredMode.REQUIRED)
	private String crmId;
	@JsonProperty("channel_name")
	@Schema(example = "Channel through which customer updated acc details", requiredMode = Schema.RequiredMode.REQUIRED)
	private String channelName;
	@JsonProperty("product_group")
	@Schema(example = "Product group of customer(Debit,credit card) ", requiredMode = Schema.RequiredMode.REQUIRED)
	private String productGroup;

}

package com.tmb.oneapp.transferservice.feature.notification.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.notification.client.NotificationServiceClient;
import com.tmb.oneapp.transferservice.feature.notification.model.V1TransferNotification;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class V1TransferNotificationService extends V1NotificationService {
    @Value("${notification-service.e-noti.default.channel.en}")
    private String defaultChannelNameEN;

    @Value("${notification-service.e-noti.default.channel.th}")
    private String defaultChannelNameTH;

    private CustomerService customerService;



    public V1TransferNotificationService(ObjectMapper objectMapper,CustomerService customerService, NotificationServiceClient notificationServiceClient) {
        super(notificationServiceClient, customerService,objectMapper);
        this.customerService = customerService;
    }

    public void sendTransferNotification(V1TransferNotification notificationPayment, String email) throws TMBCommonException {
        boolean sendNotificationStatus = getENotificationStatus(notificationPayment.getXCorrelationId(), notificationPayment.getCrmId(), notificationPayment.getTemplateName());
        if (!sendNotificationStatus) {
            return;
        }
        CustomerKYCResponse customerKYCResponse = customerService.getCustomerKyc(notificationPayment.getCrmId(), notificationPayment.getXCorrelationId());
        notificationPayment.setCustomerName(customerKYCResponse.getCustomerFirstNameEn(), customerKYCResponse.getCustomerLastNameEn(), customerKYCResponse.getCustomerFirstNameTh(), customerKYCResponse.getCustomerLastNameTh());
        notificationPayment.setChannelName(defaultChannelNameEN, defaultChannelNameTH);
        sendMessage(notificationPayment.getXCorrelationId(), notificationPayment, email);
    }
}
package com.tmb.oneapp.transferservice.feature.customerstransaction.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.customerstransaction.model.TriggerCacheRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.customers.transaction.service.name}", url = "${feign.customers.transaction.service.url}")
public interface CustomersTransactionClient {

    @PostMapping(value = "/apis/internal/customer-transaction/trigger", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> triggerClearCache(
            @RequestHeader(value = TransferServiceConstant.HEADER_CORRELATION_ID) String correlationID,
            @RequestBody TriggerCacheRequest triggerCacheRequest);
}

package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TMBDataSuccess {
	@JsonProperty("fromAccount")
	@JsonAlias("from_account")
	private OwnTMBToFromAccount fromAccount;
	@JsonProperty("toAccount")
	@JsonAlias("to_account")
	private OwnTMBToFromAccount toAccount;
	@JsonProperty("transactionAmount")
	@JsonAlias("transaction_amount")
	private Double transactionAmount;
	@JsonProperty("waiveProductCode")
	@JsonAlias("waive_product_code")
	private String waiveProductCode;
	@JsonProperty("amountWaived")
	@JsonAlias("amount_waived")
	private Integer amountWaived;
	@JsonProperty("waiveRemaining")
	@JsonAlias("waive_remaining")
	private Integer waiveRemaining;
	@JsonProperty("waiveUsed")
	@JsonAlias("waive_used")
	private Integer waiveUsed;
	@JsonProperty("waiveFlag")
	@JsonAlias("waive_flag")
	private String waiveFlag;
	@JsonProperty("transactionReference")
	@JsonAlias("transaction_reference")
	private String transactionReference;
	@JsonProperty("feeAmount")
	@JsonAlias("fee_amount")
	private Double feeAmount;
	@JsonProperty("feeType")
	@JsonAlias("fee_type")
	private String feeType;
	@JsonProperty("feeRegion")
	@JsonAlias("fee_region")
	private String feeRegion;
	@JsonProperty("transactionTime")
	@JsonAlias("transaction_time")
	private String transactionTime;
}

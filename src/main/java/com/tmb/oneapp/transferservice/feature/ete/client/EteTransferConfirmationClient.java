package com.tmb.oneapp.transferservice.feature.ete.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferV3ETERequest;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "ete-transfer-confirmation-client", url = "${ete.transfer.validation.url}",configuration = FeignCommonConfig.class)
public interface EteTransferConfirmationClient {

    @CircuitBreaker(name = "fundTransferConfirmation")
    @PostMapping(value = "/v1.0/internal/fund-transfer/confirmation",
            consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
    ResponseEntity<FundTransferOwnTMBETESuccess> confirmTransfer(@RequestBody TransferETERequest transferETERequest,
                                                                 @RequestHeader HttpHeaders headers);

    @CircuitBreaker(name = "fundTransferConfirmation")
    @PostMapping(value = "/v3.0/internal/fund-transfer/confirmation",
            consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
    ResponseEntity<FundTransferOwnTMBETESuccess> confirmTransferV3(@RequestBody TransferV3ETERequest transferETERequest,
                                                                 @RequestHeader HttpHeaders headers);

}

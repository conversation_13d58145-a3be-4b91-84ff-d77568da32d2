package com.tmb.oneapp.transferservice.feature.common.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.common.client.CommonServiceClient;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;

@Service
@RequiredArgsConstructor
public class CommonService {
    private final CommonServiceClient commonServiceClient;
    private static final TMBLogger<CommonService> logger = new TMBLogger<>(CommonService.class);
    public List<CommonData> getCommonConfiguration(String correlationId, String searchModule) throws TMBCommonException {
        try {
            HttpHeaders header = new HttpHeaders();
            header.set(HEADER_CORRELATION_ID, correlationId);
            ResponseEntity<TmbOneServiceResponse<List<CommonData>>> res = commonServiceClient
                    .getCommonConfiguration(searchModule, header);
            if (res.getStatusCode() == HttpStatus.OK && res.getBody().getData() != null) {
                return res.getBody().getData();
            }
        } catch (FeignException ex) {
            logger.error("Error while fetching data from Common Config : {}", ex);
            throw new TMBCommonException(ResponseCode.DATA_NOT_FOUND.getCode(),
                    ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
        return new ArrayList<>();
    }
}

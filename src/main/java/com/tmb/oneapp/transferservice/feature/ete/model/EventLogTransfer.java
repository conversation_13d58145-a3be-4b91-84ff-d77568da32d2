package com.tmb.oneapp.transferservice.feature.ete.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventLogTransfer {
    private String eventName;
    private String statusCode;
    private String bankCode;
    private String bankShortName;
    private String descriptionFromEte;
    private String description;

    @Schema(description = "optional value for logging", example = "**********")
    @JsonIgnoreProperties
    private String flexibleValue;
}

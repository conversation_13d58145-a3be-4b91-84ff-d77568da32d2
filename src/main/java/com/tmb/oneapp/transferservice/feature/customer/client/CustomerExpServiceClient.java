package com.tmb.oneapp.transferservice.feature.customer.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "customers-exp-service-client", url = "${customers-exp-service.url}",configuration = FeignCommonConfig.class)
public interface CustomerExpServiceClient {

    @DeleteMapping(value = "/v1/customers-exp-service/cache/deposit", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<Void>> deleteDepositCache(
            @RequestHeader(value = TransferServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = TransferServiceConstant.ACCEPT_LANGUAGE) String acceptLanguage,
            @RequestHeader(value = TransferServiceConstant.APP_VERSION) String appVersion,
            @RequestParam(value = TransferServiceConstant.CRM_ID) String crmId);

    @GetMapping(value = "/apis/customer/whitelist/fr-daily-limit", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<FRWhitelistResult>> getFrWhitelistResult(
            @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @RequestHeader("X-Correlation-ID") String correlationId
    );
}

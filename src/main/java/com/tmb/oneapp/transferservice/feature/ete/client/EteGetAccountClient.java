package com.tmb.oneapp.transferservice.feature.ete.client;

import com.tmb.common.filter.FeignCommonConfig;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositAccount;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "ete-get-account-client", url = "${ete.deposit.url}",configuration = FeignCommonConfig.class)
public interface EteGetAccountClient {

    @CircuitBreaker(name = "depositGetAccount")
    @PostMapping(value = "/v3.0/internal/deposit/get-account", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EteDepositAccount> getDepositAccount(@RequestHeader Map<String, String> headers,
                                                               @RequestBody String reqTemplate);
}

package com.tmb.oneapp.transferservice.feature.notification.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.request.notification.EmailChannel;
import com.tmb.common.model.request.notification.NotificationRecord;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.lib.service.BaseService;
import com.tmb.oneapp.transferservice.feature.notification.client.NotificationServiceClient;
import com.tmb.oneapp.transferservice.feature.notification.model.V1ENotificationSettingResponse;
import com.tmb.oneapp.transferservice.feature.notification.model.V1NotificationBasic;
import lombok.AllArgsConstructor;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
public class V1NotificationService extends BaseService {
    private static final String LOCALE_TH = "th";
    private static final String SCHEDULE_TRANSFER_TEMPLATE_VALUE = "oneapp-scheduled-transfer-complete";
    private static final String SCHEDULE_TOPUP_TEMPLATE_VALUE = "oneapp-scheduled-topup-billpayment-complete";
    private NotificationServiceClient notificationServiceClient;

    private CustomerService customerService;

    private ObjectMapper mapper;

    private List<String> getListTemplateSkipCheckNotificationSetting() {
        return new ArrayList<>(
                List.of(
                        SCHEDULE_TRANSFER_TEMPLATE_VALUE,
                        SCHEDULE_TOPUP_TEMPLATE_VALUE
                )
        );
    }

    public boolean getENotificationStatus(String correlationId, String crmId, String templateName) throws TMBCommonException {
        boolean isSkipCheckNotificationSetting = getListTemplateSkipCheckNotificationSetting().contains(templateName);
        if (isSkipCheckNotificationSetting) {
            return true;
        }
        V1ENotificationSettingResponse eNotiSettingResponse = customerService.getENotificationSetting(correlationId, crmId);
        return eNotiSettingResponse.isTransactionNotification();
    }

    private NotificationRequest createNotificationRequest(V1NotificationBasic notificationPayment, String email) {
         Map<String, Object> params = mapper.convertValue(notificationPayment, Map.class);

        EmailChannel emailChannel = new EmailChannel();
        emailChannel.setEmailEndpoint(email);
        emailChannel.setEmailSearch(false);

        NotificationRecord notificationRecord = new NotificationRecord();
        notificationRecord.setParams(params);
        notificationRecord.setCrmId(notificationPayment.getCrmId());
        notificationRecord.setLanguage(LOCALE_TH);
        notificationRecord.setEmail(emailChannel);

        NotificationRequest notificationRequest = new NotificationRequest();
        notificationRequest.setRecords(List.of(notificationRecord));
        return notificationRequest;
    }

    public void sendMessage(String correlationId, @Valid V1NotificationBasic notificationPayment, String email) throws TMBCommonException {
        NotificationRequest notificationRequest = createNotificationRequest(notificationPayment, email);
        request(() -> notificationServiceClient.sendMessage(correlationId, notificationRequest));
    }
}

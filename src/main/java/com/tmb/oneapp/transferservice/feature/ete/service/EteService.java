package com.tmb.oneapp.transferservice.feature.ete.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TTBEventLog;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteTransferAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.EventLogTransfer;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TMBDataSuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferOtherBankETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferV3ETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;

import java.math.BigDecimal;

public interface EteService {

    EteDepositTermResponse getDepositWithdrawalInfo(V1OnUsValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    FundTransferOwnTMBETESuccess validateFundTransfer(String toAccountNo, String fromAccountNo, String depositNo, BigDecimal doubleValue, String currentDate) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    EteTransferAccount getDepositAccount(String toAccountNo, String crmId, String correlationId, String toAccType) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    TMBDataSuccess confirmTransfer(TransferETERequest eteRequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    TPromptPayVerifyETEResponse validateTransferPromptPay(TransferOtherBankETERequest transferOtherBankETERequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    TPromptPayVerifyETEResponse confirmTransferPromptPay(TPromptPayETERequest eteRequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    TTBEventLog buildEventLog(EventLogTransfer eventLog, long startTime, String path);

    EteTransferAccount getDepositAccount(String toAccountNo, String crmId, String correlationId, String toAccType, String financialId) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    FundTransferOwnTMBETESuccess validateFundTransferV3(String toAccountNo, String fromAccountNo, String toFinancialId, String fromFinancialId, String depositNo, BigDecimal doubleValue, String currentDate) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

    TMBDataSuccess confirmTransferV3(TransferV3ETERequest eteRequest) throws TMBCommonException, TMBCustomCommonExceptionWithResponse;

}

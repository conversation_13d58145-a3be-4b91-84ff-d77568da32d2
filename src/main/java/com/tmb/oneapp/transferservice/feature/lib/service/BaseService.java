package com.tmb.oneapp.transferservice.feature.lib.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import feign.FeignException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.function.Supplier;

public abstract class BaseService {

    private static final TMBLogger<BaseService> logger = new TMBLogger<>(BaseService.class);

    public <T> T request(Supplier<ResponseEntity<TmbOneServiceResponse<T>>> supplier) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<T>> response = supplier.get();
            TmbOneServiceResponse<T> oneAppResponse = response.getBody();
            if (!ResponseCode.SUCCESS.getCode().equals(oneAppResponse.getStatus().getCode())) {
                throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
            }
            T data = oneAppResponse.getData();
            if (data == null) {
                throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
            }
            return data;
        } catch (FeignException | NullPointerException e) {
            logger.error("calling one-app service got exception:", e);
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
    }
}

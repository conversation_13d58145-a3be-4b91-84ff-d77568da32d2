package com.tmb.oneapp.transferservice.feature.customerstransaction.service;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.transferservice.feature.customerstransaction.client.CustomersTransactionClient;
import com.tmb.oneapp.transferservice.feature.customerstransaction.model.TriggerCacheRequest;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CHANNEL_PB;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.GROUP_TYPE_CREDIT_CARD;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.GROUP_TYPE_DEPOSIT;

@Service
@RequiredArgsConstructor
public class CustomersTransactionService {
    private static final TMBLogger<CustomersTransactionService> logger = new TMBLogger<>(CustomersTransactionService.class);
    private final CustomersTransactionClient customersTransactionClient;

    public boolean clearDepositCache(String correlationId, String crmId) {
        try {
            TriggerCacheRequest triggerCacheRequest = new TriggerCacheRequest()
                    .setCrmId(crmId)
                    .setChannelName(CHANNEL_PB)
                    .setProductGroup(GROUP_TYPE_DEPOSIT);

            customersTransactionClient.triggerClearCache(correlationId, triggerCacheRequest);
            logger.info("clearDepositCache success!!");
            return true;
        } catch (FeignException.FeignClientException e) {
            logger.error("clearDepositCache got error :", e);
        }
        return false;
    }
    public boolean clearCreditCardCache(String correlationId, String crmId) {
        try {
            TriggerCacheRequest triggerCacheRequest = new TriggerCacheRequest()
                    .setCrmId(crmId)
                    .setChannelName(CHANNEL_PB)
                    .setProductGroup(GROUP_TYPE_CREDIT_CARD);

            customersTransactionClient.triggerClearCache(correlationId, triggerCacheRequest);
            logger.info("clearCreditCardCache success!!");
            return true;
        } catch (FeignException.FeignClientException e) {
            logger.error("clearCreditCardCache got error :", e);
        }
        return false;
    }
}

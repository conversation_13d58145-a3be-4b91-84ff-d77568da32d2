package com.tmb.oneapp.transferservice.feature.financiallog.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExchangeTransactionLog {

    private String crmId;

    private String referenceId;

    private Date transactionDate;

    private String fromAccountNo;

    private String toAccountNo;

    private String fromAccountCcy;

    private String toAccountCcy;

    private String fromAccountType;

    private String toAccountType;

    private String fromAccountName;

    private String transactionType;

    private BigDecimal transactionAmountCcy;

    private BigDecimal transactionAmountThb;

    private BigDecimal exchangeRate;

    private String customerNameTh;

    private String flowName;

    private String toAcctName;

    private String purposeCode;

    private String purposeDesc;

    private String foreignCust;

    private String sameOwner;
}

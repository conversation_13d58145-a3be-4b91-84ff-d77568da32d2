package com.tmb.oneapp.transferservice.feature.authen.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@ToString
public class VerifyPinCacheRequest {
    @NotBlank(message = "Key cannot be blank")
    @Schema(description = "cache key", example = "get_test_key", requiredMode = Schema.RequiredMode.REQUIRED)
    private String key;

    @NotBlank(message = "crm_id cannot be blank")
    @Schema(description = "crm_id in cache data", example = "001100000000000000000025521918", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("crm_id")
    private String crmId;

    @NotBlank(message = "module cannot be blank")
    @Schema(description = "model in cache data", example = "TRANSFER", requiredMode = Schema.RequiredMode.REQUIRED)
    private String module;
}

package com.tmb.oneapp.transferservice.service;

import com.tmb.common.cache.Transaction;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.formatter.Formatter;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonData;
import com.tmb.common.util.TMBUtils;
import com.tmb.common.util.VersionUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.service.V1ActivityLogService;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthForceFR;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import com.tmb.oneapp.transferservice.feature.bank.service.BankService;
import com.tmb.oneapp.transferservice.feature.cache.service.CacheService;
import com.tmb.oneapp.transferservice.feature.common.service.CommonService;
import com.tmb.oneapp.transferservice.feature.customer.client.CustomerExpServiceClient;
import com.tmb.oneapp.transferservice.feature.customer.model.CommonFRVerifyRequest;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.customerstransaction.service.CustomersTransactionService;
import com.tmb.oneapp.transferservice.feature.notification.model.NotificationTransferMapper;
import com.tmb.oneapp.transferservice.feature.notification.model.V1TransferNotification;
import com.tmb.oneapp.transferservice.feature.notification.service.V1TransferNotificationService;
import com.tmb.oneapp.transferservice.model.AccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.CategoryInfoDataModel;
import com.tmb.oneapp.transferservice.model.CommonAuthenResult;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.bank.BankInfoDataModel;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.DailyUsageData;
import com.tmb.oneapp.transferservice.model.customer.PinFreeCountData;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1ITransferData;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import com.tmb.oneapp.transferservice.utils.AccountNumberUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ALPHABET_I;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.APP_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.B011B;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.COMMON_MODULE_CONSTANT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FR_DEFAULT_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HOME_FAVORITE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HOME_TRANSFER_LANDING;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROMPTPAY_REF_SEQ;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SIX_INT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TMBO;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_PROCESS_TYPE;

@Service
@RequiredArgsConstructor
public class V1TransferServiceHelperImpl implements V1TransfersServiceHelper {
    private static final TMBLogger<V1TransferServiceHelperImpl> logger = new TMBLogger<>(V1TransferServiceHelperImpl.class);
    private static final String DRAFT_DATA_COLLECTION_NAME = "draft_transfer";
    private static final String TRANSFER_PAYMENT_TEMPLATE_VALUE = "oneapp-transfer-complete";
    private static final String FCD_TRANSFER_PAYMENT_TEMPLATE_VALUE = "oneapp-transfer-FCD-to-FCD-complete";
    private final int DRAFT_DATA_TTL = 300;
    private final Formatter formatter;
    private final MongoTemplate mongoTemplate;
    private final BankService bankService;
    private final V1TransferNotificationService v1TransferNotificationService;
    private final CacheService cacheService;
    private final V1ActivityLogService activityLogService;
    private final CustomerService customerService;
    private final CustomerExpServiceClient customerExpFeignClient;
    private final CustomersTransactionService customersTransactionService;
    private final CommonService commonService;
    private final FaceRecognizeService faceRecognizeService;
    private final OauthService oauthService;

    @Value("${validate.fr.financial.accu.amount:200000}")
    private Integer totalPaymentAccumulateUsageLimit;

    @Value("${common.authen.require.app.version:5.12.0}")
    private String commonAuthenRequireAppVersion;

    @Override
    public <T> Object getTransferDraftData(
            String transId,
            Class<T> valueType
    ) throws TMBCommonException {
        try {
            return cacheService.get(transId, valueType);
        } catch (Exception e) {
            logger.error("getTransferDraftData got exception:", e);
            logger.info("try to  getTransferDraftDataSecondary");
            return getTransferDraftDataSecondary(transId, valueType);
        }
    }

    public void validateDuplicateTransaction(String transId, V1ITransferData cacheValue) throws TMBCommonException {
        if (cacheValue.isTransactionUsed()) {
            logger.error("--------------------- Duplicate transaction --------------------- ");
            logger.error("Duplicate transactions when run more than once. [transId = {}]", transId);
            throw new TMBCommonException(
                    ResponseCode.DUPLICATE_TRANSACTION.getCode(),
                    ResponseCode.DUPLICATE_TRANSACTION.getMessage(),
                    ResponseCode.DUPLICATE_TRANSACTION.getService(),
                    HttpStatus.OK,
                    null);
        }

        cacheValue.setTransactionUsed(true);
        this.saveDataToCache(transId, cacheValue);
    }

    private <T> T getTransferDraftDataSecondary(String transId, Class<T> valueType) throws TMBCommonException {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("transId").is(transId));
            T transferData = mongoTemplate.findOne(query, valueType, DRAFT_DATA_COLLECTION_NAME);
            if (transferData != null) {
                return transferData;
            }
        } catch (Exception e) {
            logger.error("getTransferDraftData got exception:", e);
        }
        throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
    }

    @Override
    public boolean checkTransactionLimited(
            V1CrmProfile crmProfile,
            double remaining
    ) throws TMBCommonException {
        try {
            BigDecimal ebMaxLimitAmountCurrent = BigDecimal.valueOf(crmProfile.getEbMaxLimitAmtCurrent());
            BigDecimal ebAccuUsageAmountDaily = BigDecimal.valueOf(crmProfile.getEbAccuUsgAmtDaily());
            BigDecimal transferAmount = BigDecimal.valueOf(remaining);
            BigDecimal remainingAmount = ebMaxLimitAmountCurrent.subtract(ebAccuUsageAmountDaily);

            return remainingAmount.compareTo(transferAmount) < 0;
        } catch (NullPointerException | NumberFormatException e) {
            logger.error("checkTransactionLimited got exception:", e);
        }
        throw new TMBCommonException(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), HttpStatus.BAD_REQUEST, null);
    }

    @Override
    @Async
    public void sendNotification(
            String correlationId,
            String crmId,
            String refId,
            V1ITransferData transferData,
            String transactionDateTime
    ) {
        try {
            List<CategoryInfoDataModel> categories = bankService.getCategories(correlationId);
            String categoryId = transferData.getCategoryId();
            CategoryInfoDataModel category = new CategoryInfoDataModel();
            if (categories != null && !categories.isEmpty()) {
                category = categories.stream().filter(lc -> lc.getCategoryCd().equals(categoryId)).findFirst().orElse(new CategoryInfoDataModel());
            }
            final String email = getEmailCustomerKyc(crmId, correlationId);
            String template = getEmailTemplateName(transferData);
            V1TransferNotification transferNotification = NotificationTransferMapper.INSTANCE
                    .toTransferNotification(template, crmId, correlationId, transactionDateTime, category, transferData);
            logger.info("V1TransferNotification: {}", transferNotification);
            v1TransferNotificationService.sendTransferNotification(transferNotification, email);
        } catch (Exception e) {
            logger.error("", e);
        }
    }

    @NotNull
    private static String getEmailTemplateName(V1ITransferData transferData) {
        String template = TRANSFER_PAYMENT_TEMPLATE_VALUE;
        if(transferData instanceof V1TransferData data) {
            if(AccountNumberUtils.isFcdAccount(data.getFromAccount().getAccountNo())) {
                template = FCD_TRANSFER_PAYMENT_TEMPLATE_VALUE;
            }
        }
        return template;
    }

    private String getEmailCustomerKyc(String crmId, String correlationId) throws TMBCommonException {
        CustomerKYCResponse customerKycResponse = customerService.getCustomerKyc(crmId, correlationId);
        return customerKycResponse.getEmail();
    }

    @Override
    public void publishActivityTransaction(TransferActivityLog transferActivityLog) {
        activityLogService.logActivity(transferActivityLog);
    }

    @Override
    public void publishActivityCustomSlip(CustomSlipCompleteActivityLog customSlipCompleteActivityLog) {
        activityLogService.logActivity(customSlipCompleteActivityLog);
    }

    @Override
    public void deleteTransferDraftData(String transId) throws TMBCommonException {
        try {
            cacheService.delete(transId);
        } catch (Exception e) {
            logger.error("deleteTransferDraftData got exception", e);
            logger.info("try to deleteTransferDraftDataSecondary ");
            deleteTransferDraftDataSecondary(transId);
        }
    }

    private void deleteTransferDraftDataSecondary(String transId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("transId").is(transId));
            mongoTemplate.remove(query, DRAFT_DATA_COLLECTION_NAME);
        } catch (Exception e) {
            logger.error("", e);
        }
    }

    @Async
    @Override
    public void deleteAccountCache(HttpHeaders headers, String correlationId, String crmId) {

        try {
            customersTransactionService.clearDepositCache(correlationId, crmId);
        } catch (Exception ex) {
            logger.error("Warning cache delete while fetching data from deleteDepositCache or deleteLoanCache : {}", ex);
        }
    }

    @Override
    public void saveDataToCache(
            String transId,
            V1ITransferData obj
    ) throws TMBCommonException {
        try {
            cacheService.set(transId, obj, DRAFT_DATA_TTL);
        } catch (Exception e) {
            logger.error("saveDataToCache got exception ", e);
            logger.info("try to saveDataToSecondary");
            saveDataToSecondary(obj);
        }
    }

    private Date expiredIn(int timeInSecond) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, timeInSecond);
        return calendar.getTime();
    }

    private void saveDataToSecondary(
            V1ITransferData obj
    ) throws TMBCommonException {
        try {
            obj.setExpiredAt(expiredIn(DRAFT_DATA_TTL));
            mongoTemplate.save(obj, DRAFT_DATA_COLLECTION_NAME);
        } catch (Exception e) {
            logger.error("", e);
            logger.info("try to  saveDataToSecondary");
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
        }
    }

    @Override
    public String getBankShortName(
            String correlationId,
            String bankCode
    ) {
        try {
            List<BankInfoDataModel> bankInfoData = bankService.getBanks(correlationId).stream().filter(value -> value.getBankCd().equals(bankCode)).collect(Collectors.toList());
            if (!bankInfoData.isEmpty()) {
                return bankInfoData.get(0).getBankShortname();
            }
        } catch (Exception e) {
            logger.error("", e);
            return "";
        }
        return null;
    }

    @Override
    public String generateTransactionRef(String key, int digits) {
        try {
            return Transaction.getTransactionId(key, digits);
        } catch (Exception ex) {
            SecureRandom secureRandom = new SecureRandom();
            int number = secureRandom.nextInt(9);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String requestDate = dateFormat.format(new Date(System.currentTimeMillis()));
            return requestDate + number;
        }
    }

    public String getSequenceKey(String key, int digits) {
        return Transaction.getSequenceKey(key, digits);
    }

    @Override
    public String generateTerminalId() {
        try {
            String sequenceKey = this.getSequenceKey(PROMPTPAY_REF_SEQ, SIX_INT);
            return ALPHABET_I + sequenceKey + B011B
                    + TMBO;
        } catch (Exception ex) {
            SecureRandom secureRandom = new SecureRandom();
            int number = secureRandom.nextInt(999999);
            return String.format("%s%06d%s%s", ALPHABET_I, number, B011B, TMBO);
        }
    }

    @Override
    public String generateTransId(String crmId) {
        return "TRANSFER_" + crmId + "_" + TMBUtils.getUUID();
    }


    @Override
    public boolean validateIsRequireConfirmPin(
            String reqAmount,
            boolean isTransferToOwnAcct,
            boolean isPreLogin,
            V1CrmProfile crmProfile,
            CommonData commonConfig
    ) {
        if (isPreLogin) {
            return true;
        }

        boolean isConfirmPin = false;
        if (!isTransferToOwnAcct) {
            Double amountDouble = Double.parseDouble(reqAmount);
            Integer pinFreeMaxTrans = Integer.parseInt(commonConfig.getPinFreeMaxTrans());
            Integer pinFreeTranCount = crmProfile.getPinFreeTxnCount();
            Double pinFreeTranLimit = crmProfile.getPinFreeTrLimit();
            isConfirmPin = isRequireConfirmPinAndDailyLimitOfTransfer(crmProfile.getPinFreeSeetingFlag(), amountDouble, pinFreeTranCount, pinFreeMaxTrans, pinFreeTranLimit);
        }
        return isConfirmPin;
    }

    @Override
    public VerifyTransactionResult validateIsRequireVerifyTransaction(HttpHeaders headers, BigDecimal amount, boolean isTransferToOwnAccount, V1CrmProfile crmProfile) throws TMBCommonException {
        boolean isRequirePinResult = false;
        var faceRecognizeResponseResult = new FaceRecognizeResponse();
        var commonAuthenResult = new CommonAuthenResult();

        String appVersion = headers.getFirst(APP_VERSION);
        String crmId = headers.getFirst(HEADER_CRM_ID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst("pre-login"));
        boolean shouldValidateWithCommonAuthen = appVersion != null && VersionUtils.compare(appVersion, commonAuthenRequireAppVersion) >= 0;
        if (shouldValidateWithCommonAuthen) {
            commonAuthenResult = this.validateIsRequireCommonAuthen(headers, String.valueOf(amount), isTransferToOwnAccount, crmProfile);
        } else {
            CommonData commonConfig = commonService.getCommonConfiguration(correlationId, COMMON_MODULE_CONSTANT).get(0);
            isRequirePinResult = this.validateIsRequireConfirmPin(String.valueOf(amount), isTransferToOwnAccount, isPreLogin, crmProfile, commonConfig);

            if (!isTransferToOwnAccount) {
                faceRecognizeResponseResult = faceRecognizeService.validateFaceRecognize(correlationId, crmId, TRANSFER_PROCESS_TYPE, amount, appVersion);
            }
        }
        return new VerifyTransactionResult(isRequirePinResult, faceRecognizeResponseResult, commonAuthenResult);
    }

    public CommonAuthenResult validateIsRequireCommonAuthen(
            HttpHeaders headers,
            String reqAmount,
            boolean isTransferToOwnAcct,
            V1CrmProfile crmProfile
    ) throws TMBCommonException {
        CommonAuthenResult response = new CommonAuthenResult();
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst("pre-login"));

        if (isPreLogin) {
            response.setRequireCommonAuthen(true);
            return response;
        }

        if (isTransferToOwnAcct) {
            response.setRequireCommonAuthen(false);
            return response;
        }

        boolean isPinFree = false;
        Boolean isForceFr = null;
        Boolean isForceDipChip = null;
        boolean resultIsRequireCommonAuthen;
        String crmId = headers.getFirst(HEADER_CRM_ID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        double amountDouble = Double.parseDouble(reqAmount);
        Integer pinFreeTranCount = crmProfile.getPinFreeTxnCount();
        double pinFreeTranLimit = crmProfile.getPinFreeTrLimit();
        boolean isDisablePinFreeSetting = ("N").equalsIgnoreCase(crmProfile.getPinFreeSeetingFlag());
        boolean amountMoreThanLimit = amountDouble > pinFreeTranLimit;

        if (isDisablePinFreeSetting || amountMoreThanLimit) {
            resultIsRequireCommonAuthen = true;
        } else if (isNotPinFree(pinFreeTranCount, correlationId)) {
            resultIsRequireCommonAuthen = true;
        } else {
            CommonAuthForceFR commonAuthForceFRResult = oauthService.getCommonAuthForceFR(correlationId, crmId);
            boolean isCrmIdForceDipChip = isCommonAuthForceFRForceDipChip(commonAuthForceFRResult);
            boolean isCrmIdForceFr = isCommonAuthForceFRForceEnabled(commonAuthForceFRResult);

            if (isCrmIdForceDipChip) {
                isPinFree = true;
                isForceDipChip = true;
                resultIsRequireCommonAuthen = true;
            } else if (isCrmIdForceFr) {
                isPinFree = true;
                isForceFr = true;
                isForceDipChip = false;
                resultIsRequireCommonAuthen = true;
            } else {
                isPinFree = true;
                isForceFr = false;
                isForceDipChip = false;
                BigDecimal totalPaymentAccumulateUsage = crmProfile.getPaymentAccuUsgAmt().add(new BigDecimal(reqAmount));
                resultIsRequireCommonAuthen = totalPaymentAccumulateUsage.compareTo(BigDecimal.valueOf(totalPaymentAccumulateUsageLimit)) >= 0;
            }
        }

        response.setRequireCommonAuthen(resultIsRequireCommonAuthen);
        response.setIsForceFr(isForceFr);
        response.setPinFree(isPinFree);
        response.setIsForceDipChip(isForceDipChip);
        return response;
    }

    private boolean isCommonAuthForceFRForceEnabled(CommonAuthForceFR commonAuthForceFRResult) {
        if (ObjectUtils.isNotEmpty(commonAuthForceFRResult)) {
            Boolean isForce = commonAuthForceFRResult.getIsForce();
            return isForce != null && isForce;
        }
        return false;
    }

    private boolean isCommonAuthForceFRForceDipChip(CommonAuthForceFR commonAuthForceFRResult) {
        if (ObjectUtils.isNotEmpty(commonAuthForceFRResult)) {
            Boolean isForceDipchip = commonAuthForceFRResult.getIsForceDipchip();
            return isForceDipchip != null && isForceDipchip;
        }
        return false;
    }

    @LogAround
    private boolean isRequireConfirmPinAndDailyLimitOfTransfer(
            String pinFreeSettingFlag,
            Double rqAmount,
            Integer pinFreeTxnCount,
            Integer pinFreeMaxTrans,
            Double pinFreeTransactionLimit
    ) {
        if ((("N").equals(pinFreeSettingFlag)) || (rqAmount > pinFreeTransactionLimit)) {
            return true;
        }
        return validatePinFreeCount(pinFreeTxnCount, pinFreeMaxTrans);

    }

    private boolean validatePinFreeCount(
            Integer pinFreeTxnCount,
            Integer pinFreeMaxTrans
    ) {
        return pinFreeTxnCount >= pinFreeMaxTrans;
    }

    private boolean isNotPinFree(
            Integer pinFreeTxnCount,
            String correlationId
    ) throws TMBCommonException {
        CommonData commonConfig = commonService.getCommonConfiguration(correlationId, COMMON_MODULE_CONSTANT).get(0);

        Integer pinFreeMaxTrans = Integer.parseInt(commonConfig.getPinFreeMaxTrans());
        return pinFreeTxnCount >= pinFreeMaxTrans;
    }

    public void updateDailyUsage(
            String correlationId,
            String crmId,
            Double amount,
            V1CrmProfile crmProfile,
            boolean isRequirePin
    ) {
        if (!isRequirePin) {
            try {
                customerService.updatePinFreeCount(correlationId, crmId, PinFreeCountData.builder().pinFreeCount(crmProfile.getPinFreeTxnCount() + 1).build());
            } catch (Exception e) {
                logger.info("Update pin free error : {}", e);
            }
        }
        String dailyUsage = String.valueOf(amount + crmProfile.getEbAccuUsgAmtDaily());
        try {
            customerService.updateDailyUsage(correlationId, crmId, DailyUsageData.builder().dailyUsage(dailyUsage).build());
        } catch (Exception e) {
            logger.info("Update daily limit error : {}", e);
        }
    }

    @Override
    public void updatePinFreeCount(String correlationId, String crmId, V1CrmProfile crmProfile) {
        try {
            customerService.updatePinFreeCount(correlationId, crmId, PinFreeCountData.builder().pinFreeCount(crmProfile.getPinFreeTxnCount() + 1).build());
        } catch (Exception e) {
            logger.info("Update pin free error : {}", e);
        }
    }

    @Override
    public void updateUsageAccumulation(
            String correlationId,
            String crmId,
            BigDecimal amount,
            V1CrmProfile crmProfile,
            boolean isRequireCommonAuthen
    ) {
        BigDecimal dailyUsage = amount.add(BigDecimal.valueOf(crmProfile.getEbAccuUsgAmtDaily()));
        BigDecimal totalPaymentAccumulateUsage = Optional.ofNullable(crmProfile.getPaymentAccuUsgAmt()).orElse(BigDecimal.ZERO).add(amount);
        boolean isExceedLimitMustSetZero = isRequireCommonAuthen && totalPaymentAccumulateUsage.compareTo(new BigDecimal(totalPaymentAccumulateUsageLimit)) >= 0;
        if (isExceedLimitMustSetZero) {
            totalPaymentAccumulateUsage = BigDecimal.ZERO;
        }
        try {
            AccumulateUsageRequest accumulateUsageRequest = new AccumulateUsageRequest();
            accumulateUsageRequest.setDailyAccumulateUsageAmount(dailyUsage);
            accumulateUsageRequest.setPaymentAccumulateUsageAmount(totalPaymentAccumulateUsage);
            customerService.updateUsageAccumulation(correlationId, crmId, accumulateUsageRequest);
        } catch (Exception e) {
            logger.info("Update usage accumulation error : {}", e);
        }
    }

    public void transformRequest(TransferOtherBankValidateRequest request) {
        String flow = handleFlowName(request.getFlow(), request.getToFavoriteName());
        request.setFlow(flow);
    }

    public void transformRequest(V1OnUsValidateRequest request) {
        String flow = handleFlowName(request.getFlow(), request.getToFavoriteName());
        request.setFlow(flow);
    }

    @Override
    public void validateCommonFR(String frUuid, String correlationId, String crmId, String ipAddress, String flow) throws TMBCommonException {
        CommonFRVerifyRequest commonFRVerifyRequest = CommonFRVerifyRequest.builder()
                .uuid(frUuid)
                .flow(flow)
                .featureId(FR_DEFAULT_FEATURE_ID).build();
        boolean isUuidNotExisted = !customerService.isCommonFRExistedByUUID(crmId, correlationId, commonFRVerifyRequest, ipAddress);
        if (isUuidNotExisted) {
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), "Not Found UUID in Common FR!", ResponseCode.FAILED.getService(), HttpStatus.OK, null);
        }
    }

    private String handleFlowName(String flow, String toFavoriteName) {
        boolean isFlowNotEmpty = StringUtils.isNotEmpty(flow);
        if (isFlowNotEmpty) {
            return flow;
        }

        logger.info("flow name is empty-then replace with default flow name");
        boolean isToFavoriteEmpty = StringUtils.isEmpty(toFavoriteName);
        if (isToFavoriteEmpty) {
            return HOME_TRANSFER_LANDING;
        } else {
            return HOME_FAVORITE;
        }
    }

    public String formatMobileOrCitizen(String mobileOrCitizen) throws TMBCommonException {
        boolean isCitizen = mobileOrCitizen.length() > 10;
        if (isCitizen) {
            return formatCitizen(mobileOrCitizen);
        } else {
            return formatMobile(mobileOrCitizen);
        }
    }

    private String formatMobile(String mobile) throws TMBCommonException {
        return formatter.formatMobileNo(mobile);
    }

    private String formatCitizen(String citizen) throws TMBCommonException {
        return formatter.formatCitizenID(citizen);
    }
}

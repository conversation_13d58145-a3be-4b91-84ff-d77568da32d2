package com.tmb.oneapp.transferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.model.FRWhitelistResult;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.PaymentAccumulateUsageRequest;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.stream.Stream;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FOREIGNER_CUSTOMER_TYPE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TOP_UP_PROCESS_TYPE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_PROCESS_TYPE;

@Service
public class FaceRecognizeService {
    private static final TMBLogger<FaceRecognizeService> logger = new TMBLogger<>(FaceRecognizeService.class);
    @Autowired
    private CustomerService customerService;

    @Value("${validate.fr.require.app.version}")
    private String requireAppVersion;
    
    @Value("${validate.fr.financial.accu.amount:200000}")
    private Integer frFinancialAccuAmount;
    
    @Value("${validate.fr.transfer.trans.limit:50000}")
    private Integer frTransferTransLimit;

    @Value("${validate.fr.topup.flag:true}")
    private boolean frTopUpFlag;

    @Value("${validate.fr.transfer.flag:true}")
    private boolean frTransferFlag;

    public FaceRecognizeResponse validateFaceRecognize(String correlation, String crmId, String processType, BigDecimal amount,String appversion) throws TMBCommonException {
        V1CrmProfile crmProfile = customerService.getCrmProfile(correlation, crmId);
        if (ObjectUtils.isEmpty(crmProfile.getPaymentAccuUsgAmt())) {
            crmProfile.setPaymentAccuUsgAmt(BigDecimal.valueOf(0));
        }

        FaceRecognizeResponse faceRecognizeResponse = new FaceRecognizeResponse();

        if (TOP_UP_PROCESS_TYPE.equals(processType) && !frTopUpFlag) {
            faceRecognizeResponse.setIsRequireFr(false);
        } else if (TRANSFER_PROCESS_TYPE.equals(processType) && !frTransferFlag) {
            faceRecognizeResponse.setIsRequireFr(false);
        } else if (isWhitelisted(crmId,correlation) || isForeigner(crmId,correlation)){
            faceRecognizeResponse.setIsRequireFr(false);
        } else if (amount.compareTo(new BigDecimal(frTransferTransLimit)) >= 0) {
            faceRecognizeResponse.setIsRequireFr(true);
        }
        else {
            BigDecimal totalPaymentAccUsgAmt = crmProfile.getPaymentAccuUsgAmt().add(amount);
            faceRecognizeResponse.setIsRequireFr(totalPaymentAccUsgAmt.compareTo(new BigDecimal(frFinancialAccuAmount)) >= 0);
        }

        //validate app version if requireFR
        if (faceRecognizeResponse.getIsRequireFr()){
            validateVersionApp(appversion);
        }
        faceRecognizeResponse.setPaymentAccuUsgAmt(crmProfile.getPaymentAccuUsgAmt());
        return faceRecognizeResponse;
    }

    public void updatePaymentAccumulateUsageAmount(String correlationId, String crmId, BigDecimal amount, Boolean isRequireFr, BigDecimal paymentAccuUsgAmt) {
        try {
            amount = Objects.requireNonNullElse(amount, BigDecimal.ZERO);
            paymentAccuUsgAmt = Objects.requireNonNullElse(paymentAccuUsgAmt, BigDecimal.ZERO);

            BigDecimal sumAccumulateUsage = paymentAccuUsgAmt.add(amount);
            BigDecimal frLimit = new BigDecimal(frFinancialAccuAmount);

            if (Boolean.TRUE.equals(isRequireFr) && sumAccumulateUsage.compareTo(frLimit) >= 0) {
                customerService.updatePaymentAccumulateUsageAmount(correlationId, crmId, new PaymentAccumulateUsageRequest(BigDecimal.ZERO));
                logger.debug("Reset paymentAccuUsgAmt to ZERO for crmId: {}, reached limit: {}", crmId, frLimit);
            } else {
                customerService.updatePaymentAccumulateUsageAmount(correlationId, crmId, new PaymentAccumulateUsageRequest(sumAccumulateUsage));
                logger.debug("Updated paymentAccuUsgAmt to {} for crmId: {}", sumAccumulateUsage, crmId);
            }
        } catch (Exception e) {
            logger.error("Failed to update paymentAccuUsgAmt for crmId: {}, error: {}", crmId, e.getMessage(), e);
        }
    }

    private boolean isWhitelisted(String crmId, String correlationId) {
        try{
        FRWhitelistResult whitelistResult = customerService.isFrWhitelistByCrmId(crmId,correlationId);
        return whitelistResult.getIsInFrWhitelist();
        } catch (Exception ex){
            logger.error("Cannot calling customerExpFeignClient getFrWhitelistResult : {}",ex);
            return false;
        }
    }

    private boolean isForeigner(String crmId, String correlationId) throws TMBCommonException{
        CustomerKYCResponse kycResponse = customerService.getCustomerKyc(crmId,correlationId);
        String customerType = kycResponse.getCustomerType();
        if (null == customerType){
            logger.error("CustomerType is null in api get customer KYC, [crmId = {}]", crmId);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(),ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.OK,null);
        }
        return StringUtils.equals(FOREIGNER_CUSTOMER_TYPE, customerType);
    }

    private void validateVersionApp(final String appVersion) throws TMBCommonException {
        if (StringUtils.isBlank(appVersion) || StringUtils.isBlank(requireAppVersion)) {
            logger.error("App version is null, [appVersion = {}, requireAppVersion = {}]", appVersion, requireAppVersion);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(),"App version is null", ResponseCode.FAILED.getService(), HttpStatus.OK,null);
        }

        boolean isValidFormat = appVersion.split("-")[0].matches("^[.0-9]*$");

        if (!isValidFormat) {
            logger.error("App version format incorrect, [appVersion = {}]", appVersion);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(),"App version incorrect format", ResponseCode.FAILED.getService(), HttpStatus.OK,null);
        }

        boolean isSameVersion = StringUtils.equals(appVersion.split("-")[0], requireAppVersion);
        if (isSameVersion) {
            return;
        }

        String oldVersionAfterCompare = Stream.of(appVersion, requireAppVersion)
                .sorted()
                .findFirst()
                .orElseThrow();

        boolean isAppVersionOld = StringUtils.equals(appVersion, oldVersionAfterCompare);
        if (isAppVersionOld) {
            logger.error("App version : {} not allow fr., [appVersion = {}, requireAppVersion = {}]", appVersion, appVersion, requireAppVersion);
            throw new TMBCommonException(ResponseCode.FR_TRANSFER_UPDATE_APP.getCode(),ResponseCode.FR_TRANSFER_UPDATE_APP.getMessage(),
                    ResponseCode.FR_TRANSFER_UPDATE_APP.getService(), HttpStatus.OK,null);
        }

    }
}

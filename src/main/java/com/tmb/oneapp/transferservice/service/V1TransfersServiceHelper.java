package com.tmb.oneapp.transferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.CommonData;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLog;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1ITransferData;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;

public interface V1TransfersServiceHelper {

    <T> Object getTransferDraftData(String transId, Class<T> valueType) throws TMBCommonException;

    void validateDuplicateTransaction(String transId, V1ITransferData cacheValue) throws TMBCommonException;

    boolean checkTransactionLimited(V1CrmProfile crmProfile, double remaining) throws TMBCommonException;

    void sendNotification(String correlationId, String crmId, String refId, V1ITransferData transferData, String transactionDateTime);

    void publishActivityTransaction(TransferActivityLog transferActivities) throws TMBCommonException;

    void publishActivityCustomSlip(CustomSlipCompleteActivityLog customSlipCompleteActivityLog);

    void deleteTransferDraftData(String transId) throws TMBCommonException;

    void deleteAccountCache(HttpHeaders headers, String correlationId, String crmId) throws TMBCommonException;

    void saveDataToCache(String transId, V1ITransferData obj) throws TMBCommonException;

    String getBankShortName(String correlationId, String bankCode);

    String generateTransactionRef(String key, int digits);

    String generateTerminalId();

    String generateTransId(String crmId);

    boolean validateIsRequireConfirmPin(String reqAmount, boolean isTransferToOwnAcct, boolean isPreLogin, V1CrmProfile crmProfile, CommonData commonConfig);

    VerifyTransactionResult validateIsRequireVerifyTransaction(HttpHeaders headers, BigDecimal amount, boolean isTransferToOwnAccount, V1CrmProfile crmProfile) throws TMBCommonException;

    void updateDailyUsage(String correlationId, String crmId, Double amount, V1CrmProfile crmProfile, boolean isRequirePin) throws TMBCommonException;

    void updatePinFreeCount(String correlationId, String crmId, V1CrmProfile crmProfile);

    void updateUsageAccumulation(String correlationId, String crmId, BigDecimal amount, V1CrmProfile crmProfile, boolean isRequireCommonAuthen);

    void transformRequest(TransferOtherBankValidateRequest request);

    void transformRequest(V1OnUsValidateRequest request);

    void validateCommonFR(String frUuid, String correlationId, String crmId, String ipAddress, String flow) throws TMBCommonException;

    String formatMobileOrCitizen(String mobileOrCitizen) throws TMBCommonException;
}

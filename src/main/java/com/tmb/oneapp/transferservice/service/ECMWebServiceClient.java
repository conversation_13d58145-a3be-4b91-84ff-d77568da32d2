package com.tmb.oneapp.transferservice.service;

import com.kony.tmbbank.cmis.CmisContentStreamType;
import com.kony.tmbbank.cmis.CmisObjectListType;
import com.kony.tmbbank.cmis.CmisObjectType;
import com.kony.tmbbank.cmis.CmisPropertiesType;
import com.kony.tmbbank.cmis.CmisProperty;
import com.kony.tmbbank.cmis.CmisPropertyId;
import com.kony.tmbbank.cmis.CmisPropertyString;
import com.kony.tmbbank.cmis.DiscoveryService;
import com.kony.tmbbank.cmis.DiscoveryServicePort;
import com.kony.tmbbank.cmis.EnumIncludeRelationships;
import com.kony.tmbbank.cmis.ObjectService;
import com.kony.tmbbank.cmis.ObjectServicePort;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.model.ECMDocument;
import com.tmb.oneapp.transferservice.utils.EcmServiceUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.activation.DataHandler;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.handler.MessageContext;
import javax.xml.ws.soap.SOAPBinding;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class ECMWebServiceClient {

    private static final TMBLogger<ECMWebServiceClient> logger = new TMBLogger<>(ECMWebServiceClient.class);

    public List<ECMDocument> searchGetData(String repoId, String query, String authorization, String wsdlUrl) {
        logger.info("[searchGetData] Method begins here");
        URL url;
        List<ECMDocument> ecmDocumentList = new ArrayList<>();
        try {
            if(wsdlUrl.startsWith("https")){
                EcmServiceUtils.httpsAuthentication();
            }
            url = new URL(wsdlUrl);

            DiscoveryService service = new DiscoveryService(url);
            DiscoveryServicePort port = service.getDiscoveryServicePort();

            logger.info("++++++ [searchGetData] : repoId : " + repoId);
            logger.info("++++++ [searchGetData] : query : " + query);
            logger.info("++++++ [searchGetData] : authorization : " + authorization);
            logger.info("++++++ [searchGetData] : wsdlUrl : " + url);


            // Add HTTP request Headers
            Map<String, List<String>> requestHeaders = new HashMap<>();
            requestHeaders.put("Authorization", Arrays.asList(authorization));

            BindingProvider provider = (BindingProvider) port;
            provider.getRequestContext().put(MessageContext.HTTP_REQUEST_HEADERS, requestHeaders);

            SOAPBinding soapBinding = (SOAPBinding) provider.getBinding();
            soapBinding.setMTOMEnabled(true);

            CmisObjectListType outputList = port.query(
                    repoId,
                    query,
                    false,
                    false,
                    EnumIncludeRelationships.NONE,
                    "2",
                    BigInteger.valueOf(2),
                    null,
                    null
            );

            logger.info("outputList from query {}", TMBUtils.convertJavaObjectToString(outputList));

            List<CmisObjectType> cmisObjList = outputList.getObjects();

            if (cmisObjList != null) {
                for (int i = 0; i < cmisObjList.size(); i++) {
                    logger.info("Records found from CMIS query service ");
                    CmisObjectType cmsObject = cmisObjList.get(i);
                    CmisPropertiesType cms = cmsObject.getProperties();

                    logger.info("CmisPropertiesType----------- " + TMBUtils.convertJavaObjectToString(cms));
                    List<CmisProperty> propertyList = cms.getProperty();

                    if (propertyList != null) {

                        CmisProperty cmisProperty = propertyList.get(0);
                        CmisPropertyString cmisPropertyString = (CmisPropertyString) propertyList.get(1);

                        List<String> valueList = ((CmisPropertyId) cmisProperty).getValue();
                        List<String> valueListString = cmisPropertyString.getValue();

                        logger.info("cmisProperty objectId value is----------- " + valueList);
                        logger.info("cmisProperty objectId value is----------- " + valueListString);

                        ECMDocument ecmDocument = new ECMDocument();
                        ecmDocument.setIds(valueList.get(0));
                        ecmDocument.setDocumentTitle(valueListString.get(0));
                        ecmDocumentList.add(ecmDocument);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("Error occurred while getting objectId  -----" + ex.getMessage(), ex);
        }
        logger.info("[searchGetData] Method ends here - objectId:" + ecmDocumentList);
        return ecmDocumentList;
    }

    public byte[] getOutputBytes(String objectId, String authorization, String wsdlurl, String repoId) {
        logger.info("[getPdfbase64] Method begins here");
        byte[] outputFilebytes = null;
        URL url;
        try {
            logger.info("[getPdfbase64] objectId >>>" + objectId);
            logger.info("[getPdfbase64] authorization >>>" + authorization);
            logger.info("[getPdfbase64] wsdlurl >>>" + wsdlurl);
            logger.info("[getPdfbase64] repoId >>>" + repoId);

            if (wsdlurl.startsWith("https")) {
                EcmServiceUtils.httpsAuthentication();
            }
            url = new URL(wsdlurl);

            ObjectService service = new ObjectService(url);
            ObjectServicePort port = service.getObjectServicePort();
            logger.info("[getPdfbase64] port >>>" + port);

            BindingProvider provider = (BindingProvider) port;
            SOAPBinding soapBinding = (SOAPBinding) provider.getBinding();
            soapBinding.setMTOMEnabled(true);

            Map<String, List<String>> requestHeaders = new HashMap<>();
            requestHeaders.put("Authorization", Arrays.asList(authorization));

            provider.getRequestContext().put(MessageContext.HTTP_REQUEST_HEADERS, requestHeaders);

            CmisContentStreamType data = port.getContentStream(
                    repoId,
                    objectId,
                    null,
                    null,
                    null,
                    null
            );

            outputFilebytes = toBytes(data.getStream());

        } catch (Exception ex) {
            logger.error("Error occurred while fetching exim doc :" + ex.getMessage(), ex);
        }
        logger.info("[getPdfbase64] Method ends here");
        return outputFilebytes;
    }

    public static byte[] toBytes(DataHandler handler) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        handler.writeTo(output);
        return output.toByteArray();
    }
}

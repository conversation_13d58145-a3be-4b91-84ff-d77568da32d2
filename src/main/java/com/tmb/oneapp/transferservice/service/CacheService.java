package com.tmb.oneapp.transferservice.service;


import com.tmb.common.logger.TMBLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;


@Service
public class CacheService {
    private static final TMBLogger<CacheService> logger = new TMBLogger<>(CacheService.class);
    public static final String START_SET_CACHE_MESSAGE_TEMPLATE = "Start Set key: {} , value: {} to Redis";
    public static final String SUCCESS_SET_CACHE_MESSAGE_TEMPLATE = "Success Set key: {} , value: {} to Redis";
    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    public CacheService(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void set(String key, String value) {
        try {
            logger.info(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, value);
            logger.info(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("exception while setting data to Redis: ", e);
        }
    }

    public void set(String key, String value, Long ttl) {
        try {
            logger.info(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, value, ttl);
            logger.info(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("method set with ttl exception while setting data to Redis: ", e);
        }
    }

    public void set(String key, String value, Duration timeout) {
        try {
            logger.info(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, value, timeout);
            logger.info(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("method set with timeout exception while setting data to Redis: ", e);
        }
    }

    public String get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.warn("exception while fetching data from Redis: ", e);
        }
        return null;
    }

    public boolean delete(String key) {
        try {
            logger.info("Start delete key: {}", key);
            return redisTemplate.delete(key);
        } catch (Exception e) {
            logger.warn("exception while deleting to Redis: ", e);
            return false;
        }
    }
}

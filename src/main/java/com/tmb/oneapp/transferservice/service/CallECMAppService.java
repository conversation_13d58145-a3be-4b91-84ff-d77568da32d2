package com.tmb.oneapp.transferservice.service;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.transferservice.model.ECMDocument;
import com.tmb.oneapp.transferservice.model.ECMDocumentRequest;
import com.tmb.oneapp.transferservice.utils.EcmServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.List;

import static com.tmb.oneapp.transferservice.utils.EcmServiceUtils.TF_DOCTYPE_EXIMDOC;
import static com.tmb.oneapp.transferservice.utils.EcmServiceUtils.TF_DOCTYPE_SWIFT;
import static com.tmb.oneapp.transferservice.utils.EcmServiceUtils.TF_DOCTYPE_SWIFT_EXIMDOC;

@Service
@RequiredArgsConstructor
public class CallECMAppService {
    private static final TMBLogger<CallECMAppService> logger = new TMBLogger<>(CallECMAppService.class);

    @Value("${ecm-username}")
    private String ecmUsername;
    @Value("${ecm-password}")
    private String ecmPassword;
    @Value("${ecm-repo-id}")
    private String ecmRepoId;
    @Value("${ecm-host-port}")
    private String ecmHostPort;

    private final ECMWebServiceClient ecmWebServiceClient;

    public List<ECMDocument> getAttachFile(ECMDocumentRequest inputReq) {
        logger.info("[IN] REQUEST DATA :: {} ", inputReq.toString());

        List<ECMDocument> response;

        String docType = null;
        if (inputReq.getDebitOp().equals(true) && inputReq.getSwiftOp().equals(false)) {
            docType = TF_DOCTYPE_EXIMDOC;
        }
        if (inputReq.getSwiftOp().equals(true) && inputReq.getDebitOp().equals(false)) {
            docType = TF_DOCTYPE_SWIFT;
        }
        if (inputReq.getSwiftOp().equals(true) && inputReq.getDebitOp().equals(true)) {
            docType = TF_DOCTYPE_SWIFT_EXIMDOC;
        }
        logger.info("[IN] REQUEST DOC TYPE :: {} ", docType);

        String query = EcmServiceUtils.getSwiftOrEximDocStatement(inputReq.getRefId(), docType);
        logger.info(" QUERY For search id : {}", query);

        String authorization = EcmServiceUtils.getAuthorization(ecmUsername, ecmPassword);
        logger.info(" Authorization For Connect SOAP: {}", authorization);

        response = ecmWebServiceClient.searchGetData(ecmRepoId, query, authorization, ecmHostPort);

        /* getContent */
        for (int i = 0; i < response.size(); i++) {
            byte[] byteContent = ecmWebServiceClient.getOutputBytes(response.get(i).getIds(), authorization, ecmHostPort, ecmRepoId);
            String sq = Base64.getEncoder().encodeToString(byteContent);

            response.get(i).setContentBase64(sq);
            response.get(i).setAttachmentName(EcmServiceUtils.getAttachmentFileName(response.get(i).getDocumentTitle(), inputReq.getTxnDateTime()));

            logger.info("[OUT] Response data :: Ids {}", response.get(i).getIds());
            logger.info("[OUT] Response data :: DocumentTitle {}", response.get(i).getDocumentTitle());
            logger.info("[OUT] Response data :: BASE64 {}", response.get(i).getContentBase64());
            logger.info("[OUT] Response data :: AttachmentName {}", response.get(i).getAttachmentName());
        }

        return response;
    }
}

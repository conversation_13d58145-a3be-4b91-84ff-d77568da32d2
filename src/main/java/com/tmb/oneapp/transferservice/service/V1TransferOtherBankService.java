package com.tmb.oneapp.transferservice.service;

import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventStatus;
import com.tmb.common.util.MiniQR;
import com.tmb.common.util.VersionUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLogMapper;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferOtherBankETERequest;
import com.tmb.oneapp.transferservice.feature.ete.service.EteService;
import com.tmb.oneapp.transferservice.feature.financiallog.model.FinRequestOtherBankLog;
import com.tmb.oneapp.transferservice.feature.financiallog.model.OtherBankTransferTransactionLog;
import com.tmb.oneapp.transferservice.feature.financiallog.service.FinancialLogService;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankConfirmResponse;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import com.tmb.oneapp.transferservice.model.transfer.EteMapper;
import com.tmb.oneapp.transferservice.model.transfer.PaymentCacheData;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayETERequest;
import com.tmb.oneapp.transferservice.model.transfer.TPromptPayVerifyETEResponse;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.utils.TTBEventLogUtils;
import com.tmb.oneapp.transferservice.utils.TransferUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_OTHER_TTB;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_CITIZEN_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_CITIZEN_TTB;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_MOBILE_OTHER_BANK;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_MOBILE_TTB;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_OTHER_TTB_ACCOUNT_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.DEFAULT_AMOUNT;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.DEFAULT_BANK_CODE;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.DEFAULT_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.DEFAULT_FLOW;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.QR_SIZE;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BANK_TMB_VALIDATE_DATEFORMAT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.EIGHT_INT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FLOW_NAME_TRANSFER;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE;
import static com.tmb.oneapp.transferservice.utils.DateUtils.getCurrentDateTime;
import static com.tmb.oneapp.transferservice.utils.TransferUtils.makeBankCode3Digits;

@Service
@RequiredArgsConstructor
public class V1TransferOtherBankService {
    private static final TMBLogger<V1TransferOtherBankService> logger = new TMBLogger<>(V1TransferOtherBankService.class);
    private final V1TransfersServiceHelper transfersHelper;
    private final CustomerService customerService;
    private final OauthService oauthService;
    private final EteService eteService;
    private final ConfigurationService configurationService;
    private final FaceRecognizeService faceRecognizeService;
    private final FinancialLogService financialLogService;

    @Value("${amlo.amount.validate}")
    private BigDecimal amloAmountValidate;

    @Value("${common.authen.require.app.version:5.12.0}")
    private String minVersionCommonAuthTrigger;

    public TransferOtherBankValidateResponse transferOtherBankValidate(TransferOtherBankValidateRequest request, String crmId, String correlationId, HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toBankShortName = StringUtils.EMPTY;
        BigDecimal fee = BigDecimal.ZERO;
        VerifyTransactionResult transactionResult = null;
        long start = System.currentTimeMillis();
        String uri = "/v1/transfer/off-us/validate";

        try {
            transfersHelper.transformRequest(request);

            final String taxId = getTaxIdFromCustomerKyc(crmId, correlationId, request.getAmount());
            TransferOtherBankETERequest eteRequest = EteMapper.INSTANCE.toTransferValidationRequest(request, taxId, transfersHelper.generateTerminalId(), transfersHelper.generateTransactionRef(TRANSFER_REFERENCE_NUMBER_PREFIX, EIGHT_INT));
            TPromptPayVerifyETEResponse verifyResponse = eteService.validateTransferPromptPay(eteRequest);

            toBankShortName = transfersHelper.getBankShortName(correlationId, verifyResponse.getReceiver().getBankCode());
            verifyResponse.getSender().setBankCode(TTB_BANK_CODE);
            fee = verifyResponse.getFee();

            V1CrmProfile crmProfile = customerService.getCrmProfile(correlationId, crmId);
            if (transfersHelper.checkTransactionLimited(crmProfile, Double.parseDouble(request.getAmount()))) {
                throw new TMBCommonException(
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(),
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(),
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getService(),
                        HttpStatus.BAD_REQUEST,
                        null
                );
            }

            boolean transferToTTBAcct = verifyResponse.getReceiver().getBankCode().equals(TTB_BANK_CODE);
            boolean isTransferToOwnAcct = false;
            if (transferToTTBAcct) {
                List<DepositAccount> depositAccounts = customerService.getAccountsTransfer(correlationId, crmId);
                isTransferToOwnAcct = checkTransferToOwnAccount(request.getToAccountNo(), depositAccounts);
            }

            transactionResult = transfersHelper.validateIsRequireVerifyTransaction(headers, new BigDecimal(request.getAmount()), isTransferToOwnAcct, crmProfile);

            boolean isRequirePin = transactionResult.isRequirePin();

            String transId = transfersHelper.generateTransId(crmId);

            PaymentCacheData paymentCacheData = createPaymentCacheData(request, verifyResponse, toBankShortName, isTransferToOwnAcct);

            updateVerifyETEResponse(request, verifyResponse, transactionResult, transId, crmProfile, paymentCacheData);

            TransferOtherBankValidateResponse response = buildValidateResponse(verifyResponse, transactionResult, isRequirePin, transId);

            transfersHelper.saveDataToCache(transId, verifyResponse);

            TransferActivityLog activityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, toBankShortName, fee, request, transactionResult);
            transfersHelper.publishActivityTransaction(activityLog);

            eventBusinessLogging(crmProfile, transactionResult, uri, start);

            return response;
        } catch (Exception e) {
            handleValidateError(headers, toBankShortName, fee, request, transactionResult, e);
            throw e;
        }
    }

    private void eventBusinessLogging(V1CrmProfile crmProfile, VerifyTransactionResult transactionResult, String uri, long start) {
        HashMap<String, Object> customData = new HashMap<>();
        customData.put("crmId", crmProfile.getCrmId());
        customData.put("pinfree", transactionResult.commonAuthenResult().isPinFree());
        customData.put("ddp", transactionResult.commonAuthenResult().getIsForceFr());
        TTBEventLogUtils.sendBusinessEventLog(TTBEventStatus.SUCCESS, uri, HttpStatus.OK.value(), (int) (System.currentTimeMillis() - start), customData);
    }

    public TransferOtherBankValidateResponse transferPromptPayValidate(TransferOtherBankValidateRequest request, String crmId, String correlationId, HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        String toBankShortName = StringUtils.EMPTY;
        String fee = "0.00";
        boolean transferToTTBAcct = false;
        String receiverAccountId = StringUtils.EMPTY;
        VerifyTransactionResult transactionResult = null;
        long start = System.currentTimeMillis();
        String uri = "/v1/transfer/promptpay/validate";

        try {
            transfersHelper.transformRequest(request);

            final String taxId = getTaxIdFromCustomerKyc(crmId, correlationId, request.getAmount());
            TransferOtherBankETERequest eteRequest = EteMapper.INSTANCE.toTransferValidationRequest(request, taxId, transfersHelper.generateTerminalId(), transfersHelper.generateTransactionRef(TRANSFER_REFERENCE_NUMBER_PREFIX, EIGHT_INT));
            TPromptPayVerifyETEResponse verifyResponse = eteService.validateTransferPromptPay(eteRequest);

            verifyResponse.getSender().setBankCode(TTB_BANK_CODE);
            receiverAccountId = verifyResponse.getReceiver().getAccountId();
            String senderAccountId = verifyResponse.getSender().getAccountId();
            validateSameAccount(senderAccountId, receiverAccountId, verifyResponse.getReceiver().getBankCode(), request.getToAccountNo());
            fee = getPromptPayFee(verifyResponse.getFee(), request.getDepositAccount().getWaiveFeeForPromptPay());

            toBankShortName = transfersHelper.getBankShortName(correlationId, verifyResponse.getReceiver().getBankCode());

            transferToTTBAcct = verifyResponse.getReceiver().getBankCode().equals(TTB_BANK_CODE);
            boolean isTransferToOwnAcct = false;
            if (transferToTTBAcct) {
                List<DepositAccount> depositAccounts = customerService.getAccountsTransfer(correlationId, crmId);
                isTransferToOwnAcct = checkTransferToOwnAccount(receiverAccountId, depositAccounts);
            }

            V1CrmProfile crmProfile = customerService.getCrmProfile(correlationId, crmId);
            if (transfersHelper.checkTransactionLimited(crmProfile, Double.parseDouble(request.getAmount()))) {
                throw new TMBCommonException(
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(),
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(),
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getService(),
                        HttpStatus.BAD_REQUEST,
                        null
                );
            }

            transactionResult = transfersHelper.validateIsRequireVerifyTransaction(headers, new BigDecimal(request.getAmount()), isTransferToOwnAcct, crmProfile);

            boolean isRequirePin = transactionResult.isRequirePin();

            String transId = transfersHelper.generateTransId(crmId);

            PaymentCacheData paymentCacheData = createPaymentCacheData(request, verifyResponse, toBankShortName, isTransferToOwnAcct);

            updateVerifyETEResponse(request, verifyResponse, transactionResult, transId, crmProfile, paymentCacheData, fee);

            TransferOtherBankValidateResponse response = buildValidateResponse(verifyResponse, transactionResult, isRequirePin, transId);

            transfersHelper.saveDataToCache(transId, verifyResponse);

            TransferActivityLog transferActivityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, toBankShortName, new BigDecimal(fee), request, transferToTTBAcct, receiverAccountId, transactionResult);
            transfersHelper.publishActivityTransaction(transferActivityLog);

            eventBusinessLogging(crmProfile, transactionResult, uri, start);

            return response;

        } catch (Exception e) {
            TransferActivityLog transferActivityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, toBankShortName, new BigDecimal(fee), request, transferToTTBAcct, receiverAccountId, transactionResult);
            transferActivityLog.setFailureStatusWithReasonFromException(e);
            transfersHelper.publishActivityTransaction(transferActivityLog);
            throw e;
        }
    }

    public TransferOtherBankConfirmResponse transferOtherBankConfirm(@Valid TransferOtherBankConfirmRequest request, String crmId, String correlationId, HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse, IOException, WriterException {
        String transactionDateTime = getCurrentDateTime();
        TPromptPayVerifyETEResponse cacheResponse = null;
        String appVersion = headers.getFirst("app-version");

        try {
            cacheResponse = (TPromptPayVerifyETEResponse) transfersHelper.getTransferDraftData(request.getTransId(), TPromptPayVerifyETEResponse.class);
            transfersHelper.validateDuplicateTransaction(request.getTransId(), cacheResponse);

            validateAuthentication(request, cacheResponse, headers, correlationId, crmId);

            V1CrmProfile crmProfile = customerService.getCrmProfile(correlationId, crmId);
            if (transfersHelper.checkTransactionLimited(crmProfile, cacheResponse.getAmount().doubleValue())) {
                throw new TMBCommonException(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), HttpStatus.BAD_REQUEST, null);
            }

            TPromptPayETERequest eteRequest = buildConfirmRequest(cacheResponse);
            TPromptPayVerifyETEResponse confirmResponse = eteService.confirmTransferPromptPay(eteRequest);

            updateUsageMetrics(correlationId, crmId, cacheResponse, crmProfile, appVersion);

            transfersHelper.deleteAccountCache(headers, correlationId, crmId);
            transfersHelper.deleteTransferDraftData(request.getTransId());
            transfersHelper.sendNotification(correlationId, crmId, cacheResponse.getTransactionReference(), cacheResponse, transactionDateTime);

            TransferOtherBankConfirmResponse response = buildConfirmResponse(cacheResponse, confirmResponse, transactionDateTime);

            logConfirmActivity(headers, cacheResponse, confirmResponse, request, correlationId, crmId, transactionDateTime);

            return response;

        } catch (Exception e) {
            handleConfirmError(headers, cacheResponse, correlationId, crmId, transactionDateTime, e);
            throw e;
        }
    }

    private TransferOtherBankValidateResponse buildValidateResponse(TPromptPayVerifyETEResponse verifyResponse, VerifyTransactionResult transactionResult, boolean isRequirePin, String transId) {
        return TransferOtherBankValidateResponse.builder()
                .transId(transId)
                .amount(verifyResponse.getAmount().toString())
                .fee(verifyResponse.getFee().toString())
                .toAccountName(verifyResponse.getReceiver().getAccountDisplayName())
                .isRequireConfirmPin(isRequirePin)
                .isRequireFr(transactionResult.faceRecognizeResponse().getIsRequireFr())
                .isRequireCommonAuthen(transactionResult.commonAuthenResult().isRequireCommonAuthen())
                .commonAuthenticationInformation(verifyResponse.getPaymentCacheData().getCommonAuthenticationInformation())
                .build();
    }

    private void updateVerifyETEResponse(TransferOtherBankValidateRequest request, TPromptPayVerifyETEResponse verifyETEResponse, VerifyTransactionResult transactionResult, String transId, V1CrmProfile crmProfile, PaymentCacheData paymentCacheData, String fee) {

        CommonAuthenticationInformation commonAuthInfo = getCommonAuthenticationInformation(
                request,
                transactionResult,
                verifyETEResponse,
                crmProfile
        );

        FaceRecognizeResponse faceRecognizeResponse = transactionResult.faceRecognizeResponse();
        paymentCacheData.setIsRequireFr(faceRecognizeResponse.getIsRequireFr());
        paymentCacheData.setPaymentAccuUsgAmt(faceRecognizeResponse.getPaymentAccuUsgAmt());
        paymentCacheData.setRequireCommonAuthentication(transactionResult.commonAuthenResult().isRequireCommonAuthen());
        paymentCacheData.setCommonAuthenticationInformation(commonAuthInfo);


        verifyETEResponse.setRequireConfirmPin(transactionResult.isRequirePin());
        verifyETEResponse.setTransId(transId);
        verifyETEResponse.setPaymentCacheData(paymentCacheData);
        verifyETEResponse.setFee(new BigDecimal(fee));
    }

    private void updateVerifyETEResponse(TransferOtherBankValidateRequest request, TPromptPayVerifyETEResponse verifyETEResponse, VerifyTransactionResult transactionResult, String transId, V1CrmProfile crmProfile, PaymentCacheData paymentCacheData) {

        CommonAuthenticationInformation commonAuthInfo = getCommonAuthenticationInformation(
                request,
                transactionResult,
                verifyETEResponse,
                crmProfile
        );

        FaceRecognizeResponse faceRecognizeResponse = transactionResult.faceRecognizeResponse();
        paymentCacheData.setIsRequireFr(faceRecognizeResponse.getIsRequireFr());
        paymentCacheData.setPaymentAccuUsgAmt(faceRecognizeResponse.getPaymentAccuUsgAmt());
        paymentCacheData.setRequireCommonAuthentication(transactionResult.commonAuthenResult().isRequireCommonAuthen());
        paymentCacheData.setCommonAuthenticationInformation(commonAuthInfo);

        verifyETEResponse.setRequireConfirmPin(transactionResult.isRequirePin());
        verifyETEResponse.setTransId(transId);
        verifyETEResponse.setPaymentCacheData(paymentCacheData);
    }

    private PaymentCacheData createPaymentCacheData(
            TransferOtherBankValidateRequest request,
            TPromptPayVerifyETEResponse verifyETEResponse,
            String toBankShortName,
            boolean isTransferToOwnAcct
    ) {
        return PaymentCacheData.builder()
                .toFavoriteNickname(StringUtils.isNotBlank(request.getToFavoriteName()) ? request.getToFavoriteName() : null)
                .bankCode(verifyETEResponse.getReceiver().getBankCode())
                .note(request.getNote())
                .categoryId(request.getCategoryId())
                .flow(request.getFlow())
                .bankShortName(toBankShortName)
                .isTransferToOwnAccount(isTransferToOwnAcct)
                .fromAccountNickname(request.getDepositAccount().getProductNickname())
                .amountToSaveFavorite(verifyETEResponse.getAmount().toString())
                .reqToAccountNoToSaveFavorite(request.getToAccountNo())
                .qr(request.getQr())
                .build();
    }

    private CommonAuthenticationInformation getCommonAuthenticationInformation(TransferOtherBankValidateRequest request, VerifyTransactionResult transactionResult, TPromptPayVerifyETEResponse verifyResponse, V1CrmProfile crmProfile) {

        if (!transactionResult.commonAuthenResult().isRequireCommonAuthen()) {
            return null;
        }

        BigDecimal amount = new BigDecimal(request.getAmount());
        BigDecimal totalPaymentAccumulateAmount = amount.add(
                Optional.ofNullable(crmProfile)
                        .map(V1CrmProfile::getPaymentAccuUsgAmt)
                        .orElse(BigDecimal.ZERO)
        );

        return CommonAuthenticationInformation.builder()
                .featureId(TRANSFER_FEATURE_ID)
                .totalPaymentAccumulateUsage(totalPaymentAccumulateAmount)
                .toBankCode(makeBankCode3Digits(verifyResponse.getReceiver().getBankCode()))
                .flowName(FLOW_NAME_TRANSFER)
                .amount(request.getAmount())
                .build();
    }

    private TransferOtherBankConfirmResponse buildConfirmResponse(TPromptPayVerifyETEResponse cacheResponse, TPromptPayVerifyETEResponse confirmResponse, String transactionDateTime) throws IOException, WriterException {

        String refId = cacheResponse.getTransactionReference();
        MiniQR miniQR = new MiniQR(refId);

        return TransferOtherBankConfirmResponse.builder()
                .referenceNo(refId)
                .remainingBalance(confirmResponse.getBalance().getAvailable().toString())
                .transferCreatedDatetime(getDateFormatFrontEndFromTxnDt(transactionDateTime))
                .isToOwnAccount(cacheResponse.getPaymentCacheData().isTransferToOwnAccount())
                .qr(miniQR.drawToBase64(Integer.parseInt(QR_SIZE), Integer.parseInt(QR_SIZE)))
                .build();
    }

    private CommonAuthenWithPayloadRequest buildCommonAuthRequest(TransferOtherBankConfirmRequest request, TPromptPayVerifyETEResponse cacheResponse) {
        validateCommonAuthRequestParams(request, cacheResponse);

        return CommonAuthenWithPayloadRequest.builder()
                .amount(getFormattedAmount(cacheResponse))
                .dailyAmount(getFormattedDailyAmount(cacheResponse))
                .flowName(getFlowName(cacheResponse))
                .refId(request.getTransId())
                .bankCode(getBankCode(cacheResponse))
                .featureId(getFeatureId(cacheResponse))
                .build();
    }

    private void validateCommonAuthRequestParams(TransferOtherBankConfirmRequest request, TPromptPayVerifyETEResponse cacheResponse) {
        if (request == null) {
            throw new IllegalArgumentException("Transfer request cannot be null");
        }
        if (cacheResponse == null) {
            throw new IllegalArgumentException("Cache response cannot be null");
        }
        if (StringUtils.isBlank(request.getTransId())) {
            throw new IllegalArgumentException("Transaction ID cannot be empty");
        }
        if (cacheResponse.getAmount() == null) {
            throw new IllegalArgumentException("Transfer amount cannot be null");
        }
    }

    private String getFormattedAmount(TPromptPayVerifyETEResponse cacheResponse) {
        return Optional.ofNullable(cacheResponse.getAmount()).map(String::valueOf).orElse(DEFAULT_AMOUNT);
    }

    private String getFormattedDailyAmount(TPromptPayVerifyETEResponse cacheResponse) {
        return Optional.ofNullable(cacheResponse.getPaymentCacheData())
                .map(PaymentCacheData::getCommonAuthenticationInformation)
                .map(CommonAuthenticationInformation::getTotalPaymentAccumulateUsage)
                .map(String::valueOf)
                .orElse(DEFAULT_AMOUNT);
    }

    private String getFlowName(TPromptPayVerifyETEResponse cacheResponse) {
        return Optional.ofNullable(cacheResponse.getPaymentCacheData()).map(PaymentCacheData::getCommonAuthenticationInformation).map(CommonAuthenticationInformation::getFlowName).orElse(DEFAULT_FLOW);
    }

    private String getBankCode(TPromptPayVerifyETEResponse cacheResponse) {
        return Optional.ofNullable(cacheResponse.getReceiver()).map(Receiver::getBankCode).orElse(DEFAULT_BANK_CODE);
    }

    private String getFeatureId(TPromptPayVerifyETEResponse cacheResponse) {
        return Optional.ofNullable(cacheResponse.getPaymentCacheData())
                .map(PaymentCacheData::getCommonAuthenticationInformation)
                .map(CommonAuthenticationInformation::getFeatureId)
                .orElse(DEFAULT_FEATURE_ID);
    }

    private TPromptPayETERequest buildConfirmRequest(TPromptPayVerifyETEResponse cacheResponse) {
        return TPromptPayETERequest.builder()
                .sender(cacheResponse.getSender())
                .terminal(cacheResponse.getTerminal())
                .receiver(cacheResponse.getReceiver())
                .amount(cacheResponse.getAmount().setScale(2, RoundingMode.DOWN))
                .rtpTransactionReference(cacheResponse.getRtpTransactionReference())
                .transactionReference(cacheResponse.getTransactionReference())
                .senderType(cacheResponse.getSenderType())
                .fee(cacheResponse.getFee().doubleValue())
                .receiverType(cacheResponse.getReceiverType())
                .chargeCode(cacheResponse.getChargeCode())
                .chargeType("")
                .effectiveDate(getCurrentDateTTBValidateFormat())
                .transactionCreatedDatetime(ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .build();
    }

    private void logConfirmActivity(HttpHeaders headers, TPromptPayVerifyETEResponse cacheResponse, TPromptPayVerifyETEResponse confirmResponse, TransferOtherBankConfirmRequest request, String correlationId, String crmId, String transactionDateTime) throws TMBCommonException {

        try {
            TransferActivityLog activityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, cacheResponse);

            CustomSlipCompleteActivityLog customSlipLog = new CustomSlipCompleteActivityLog(headers, request.getCustomSlip());

            String activityFinId = setActivityFinIdByActivityLog(activityLog.getActivityTypeId());

            FinRequestOtherBankLog finLog = new FinRequestOtherBankLog(cacheResponse, crmId, activityLog.getActivityTypeId(), cacheResponse.getTransactionReference(), correlationId, activityFinId, transactionDateTime);
            finLog.setTxnBal(confirmResponse.getBalance().getAvailable().toString());

            OtherBankTransferTransactionLog transactionLog = new OtherBankTransferTransactionLog(crmId, cacheResponse, activityFinId, transactionDateTime);

            transfersHelper.publishActivityTransaction(activityLog);
            transfersHelper.publishActivityCustomSlip(customSlipLog);
            financialLogService.saveLogFinancialAndTransactionEvent(correlationId, finLog, transactionLog);
        } catch (Exception e) {
            logger.error("Error logging confirm activity", e);
            throw TransferUtils.failException(ResponseCode.FAILED);
        }
    }

    private String getTaxIdFromCustomerKyc(String crmId, String correlationId, String amount) throws TMBCommonException {
        BigDecimal amountBigDecimal = new BigDecimal(amount);
        if (amountBigDecimal.compareTo(amloAmountValidate) >= 0) {
            CustomerKYCResponse customerKYCResponse = customerService.getCustomerKyc(crmId, correlationId);
            return Optional.ofNullable(customerKYCResponse.getIdNo()).orElse("");
        }
        return "";
    }

    private boolean checkTransferToOwnAccount(String toAccountNo, List<DepositAccount> depositAccounts) {
        return depositAccounts.stream().anyMatch(account -> StringUtils.equals(account.getAccountNumber(), toAccountNo));
    }

    private String getPromptPayFee(BigDecimal feeFromETE, String waiveFeePromptPayFromDepositAccount) {
        String defaultFee = "0.00";

        if (feeFromETE == null) {
            return defaultFee;
        }

        String waiveFeePromptPayFromCommonConfig;
        try {
            TransferModuleModel transferModuleModel = configurationService.getTransferConfiguration();
            waiveFeePromptPayFromCommonConfig = transferModuleModel.getPromptPayProxyWaiveFee();
        } catch (TMBCommonException e) {
            logger.warn("Error getting transfer configuration, defaulting to waive fee", e);
            waiveFeePromptPayFromCommonConfig = "Y";
        }

        boolean isWaiveFee = StringUtils.equals("Y", waiveFeePromptPayFromCommonConfig) || StringUtils.equals("1", waiveFeePromptPayFromDepositAccount);

        return isWaiveFee ? defaultFee : String.valueOf(feeFromETE.setScale(2, RoundingMode.DOWN));
    }

    private void validateSameAccount(String fromAccountId, String toAccountId, String toBankCode, String mobileOrIdRequest) throws TMBCustomCommonExceptionWithResponse, TMBCommonException {
        if (StringUtils.equals(toBankCode, TTB_BANK_CODE) && StringUtils.equals(fromAccountId, toAccountId)) {
            throw createSameAccountException(mobileOrIdRequest);
        }
    }

    private TMBCustomCommonExceptionWithResponse createSameAccountException(String mobileOrIdRequest) throws TMBCommonException {
        Map<String, String> errorMsg = new HashMap<>();
        String formatMobileOrIdRequest = transfersHelper.formatMobileOrCitizen(mobileOrIdRequest);
        errorMsg.put("<$PromptPayNumber>", formatMobileOrIdRequest);

        return new TMBCustomCommonExceptionWithResponse(ResponseCode.PROMPTPAY_LINKED.getCode(), String.format(ResponseCode.PROMPTPAY_LINKED.getMessage(), formatMobileOrIdRequest), ResponseCode.PROMPTPAY_LINKED.getService(), HttpStatus.OK, null, null, errorMsg);
    }

    private void handleValidateError(HttpHeaders headers, String toBankShortName, BigDecimal fee, TransferOtherBankValidateRequest request, VerifyTransactionResult transactionResult, Exception e) {
        try {
            TransferActivityLog activityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, toBankShortName, fee, request, transactionResult);
            activityLog.setFailureStatusWithReasonFromException(e);
            transfersHelper.publishActivityTransaction(activityLog);
        } catch (Exception ex) {
            logger.error("Error handling validate error", ex);
        }
    }

    private void handleConfirmError(HttpHeaders headers, TPromptPayVerifyETEResponse cacheResponse, String correlationId, String crmId, String transactionDateTime, Exception e) {
        if (cacheResponse != null) {
            try {
                TransferActivityLog activityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, cacheResponse);
                activityLog.setFailureStatusWithReasonFromException(e);

                String activityFinId = setActivityFinIdByActivityLog(activityLog.getActivityTypeId());
                FinRequestOtherBankLog finLog = new FinRequestOtherBankLog(cacheResponse, crmId, activityLog.getActivityTypeId(), cacheResponse.getTransactionReference(), correlationId, activityFinId, transactionDateTime);
                finLog.setFailureStatusWithErrorCodeFromException(e);

                OtherBankTransferTransactionLog transactionLog = new OtherBankTransferTransactionLog(crmId, cacheResponse, activityFinId, transactionDateTime);
                transactionLog.setTransactionStatus(TransferServiceConstant.FAILURE);

                transfersHelper.publishActivityTransaction(activityLog);
                financialLogService.saveLogFinancialAndTransactionEvent(correlationId, finLog, transactionLog);
            } catch (Exception ex) {
                logger.error("Error handling confirm error", ex);
            }
        }
    }

    public static String getCurrentDateTTBValidateFormat() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        return dateFormat.format(new Date());
    }

    private String getDateFormatFrontEndFromTxnDt(String transactionDateTime) {
        Date dateFromString = new Date(Long.parseLong(transactionDateTime));
        SimpleDateFormat dateFormat = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        return dateFormat.format(dateFromString);
    }

    private String setActivityFinIdByActivityLog(String activityTypeId) {
        if (activityTypeId.equals(ACTIVITY_LOG_CONFIRM_OTHER_TTB_ACCOUNT_ACTIVITY_ID)) {
            return ACTIVITY_FIN_AND_TRANS_TRANSFER_OTHER_TTB;
        } else if (activityTypeId.equals(ACTIVITY_LOG_CONFIRM_OTHER_BANK_ACTIVITY_ID)) {
            return ACTIVITY_FIN_AND_TRANS_TRANSFER_OTHER_BANK;
        } else if (isPromptPayMobileToTTB(activityTypeId)) {
            return ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_MOBILE_TTB;
        } else if (isPromptPayMobileToOtherBank(activityTypeId)) {
            return ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_MOBILE_OTHER_BANK;
        } else if (isPromptPayCitizenToTTB(activityTypeId)) {
            return ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_CITIZEN_TTB;
        } else {
            if (isPromptPayCitizenToOtherBank(activityTypeId)) {
                return ACTIVITY_FIN_AND_TRANS_TRANSFER_PROMPT_PAY_CITIZEN_OTHER_BANK;
            }
        }
        return "ACTIVITY LOG ID NOT MATCH WITH ANY FIN ID";
    }

    private boolean isPromptPayCitizenToOtherBank(String activityTypeId) {
        return activityTypeId.equals(ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID) || activityTypeId.equals(ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_OTHER_BANK_ACTIVITY_ID);
    }

    private boolean isPromptPayCitizenToTTB(String activityTypeId) {
        return activityTypeId.equals(ACTIVITY_LOG_CONFIRM_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID) || activityTypeId.equals(ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_CITIZEN_TTB_ACTIVITY_ID);
    }

    private boolean isPromptPayMobileToOtherBank(String activityTypeId) {
        return activityTypeId.equals(ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID) || activityTypeId.equals(ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_OTHER_BANK_ACTIVITY_ID);
    }

    private boolean isPromptPayMobileToTTB(String activityTypeId) {
        return activityTypeId.equals(ACTIVITY_LOG_CONFIRM_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID) || activityTypeId.equals(ACTIVITY_LOG_CONFIRM_QR_PROMPTPAY_MOBILE_TTB_ACTIVITY_ID);
    }

    private void validateAuthentication(TransferOtherBankConfirmRequest request, TPromptPayVerifyETEResponse cacheResponse, HttpHeaders headers, String correlationId, String crmId) throws TMBCommonException {
        String appVersion = headers.getFirst(TransferServiceConstant.APP_VERSION);
        String ipAddress = headers.getFirst(IP_ADDRESS);
        String flow = cacheResponse.getPaymentCacheData().getFlow();

        if (shouldUseCommonAuth(appVersion)) {
            if (cacheResponse.getPaymentCacheData().isRequireCommonAuthentication()) {
                CommonAuthenWithPayloadRequest authRequest = buildCommonAuthRequest(request, cacheResponse);
                oauthService.verifyCommonAuthenWithPayload(headers, authRequest);
            }
        } else {
            validateLegacyAuthentication(request, cacheResponse, correlationId, crmId, ipAddress, flow);
        }
    }

    private boolean shouldUseCommonAuth(String appVersion) {
        return appVersion != null && VersionUtils.compare(appVersion, minVersionCommonAuthTrigger) >= 0;
    }

    private void validateLegacyAuthentication(TransferOtherBankConfirmRequest request, TPromptPayVerifyETEResponse cacheResponse, String correlationId, String crmId, String ipAddress, String flow) throws TMBCommonException {
        if (Boolean.TRUE.equals(cacheResponse.getPaymentCacheData().getIsRequireFr())) {
            transfersHelper.validateCommonFR(request.getFrUuid(), correlationId, crmId, ipAddress, flow);
        }

        if (Boolean.TRUE.equals(cacheResponse.isRequireConfirmPin())) {
            String moduleType = determineModuleType(cacheResponse);
            String pinRef = TransferServiceConstant.TRANSFER_PIN_REFERENCE_PREFIX + request.getTransId();

            oauthService.verifyPinCache(correlationId, crmId, pinRef, moduleType);
        }
    }

    private String determineModuleType(TPromptPayVerifyETEResponse cacheResponse) {
        return StringUtils.isNotBlank(cacheResponse.getPaymentCacheData().getQr()) ? TransferServiceConstant.QR_TRANSFER_MODULE : TransferServiceConstant.TRANSFER_MODULE;
    }

    private void updateUsageMetrics(String correlationId, String crmId, TPromptPayVerifyETEResponse cacheResponse, V1CrmProfile crmProfile, String appVersion) throws TMBCommonException {
        PaymentCacheData paymentCacheData = cacheResponse.getPaymentCacheData();
        boolean isCommonAuthVersion = appVersion != null && VersionUtils.compare(appVersion, minVersionCommonAuthTrigger) >= 0;

        if (paymentCacheData.isTransferToOwnAccount()) {
            logger.debug("Skip usage metrics update for own account transfer");
            return;
        }

        try {
            if (isCommonAuthVersion) {
                transfersHelper.updateUsageAccumulation(correlationId, crmId, cacheResponse.getAmount(), crmProfile, true);
                boolean isShouldUpdatePinFreeCount = !paymentCacheData.isRequireCommonAuthentication();
                if (isShouldUpdatePinFreeCount) {
                    transfersHelper.updatePinFreeCount(correlationId, crmId, crmProfile);
                }
            } else {
                transfersHelper.updateDailyUsage(correlationId, crmId, cacheResponse.getAmount().doubleValue(), crmProfile, cacheResponse.isRequireConfirmPin());
                faceRecognizeService.updatePaymentAccumulateUsageAmount(correlationId, crmId, cacheResponse.getAmount(), paymentCacheData.getIsRequireFr(), paymentCacheData.getPaymentAccuUsgAmt());
            }

            logger.info("Successfully updated usage metrics for transfer: {}", cacheResponse.getTransactionReference());
        } catch (Exception e) {
            logger.error("Error updating usage metrics", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), "Failed to update usage metrics", ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
        }
    }
}
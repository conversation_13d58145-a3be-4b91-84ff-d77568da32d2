package com.tmb.oneapp.transferservice.service;

import com.google.zxing.WriterException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.MiniQR;
import com.tmb.common.util.TMBUtils;
import com.tmb.common.util.VersionUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.account.client.AccountsServiceClient;
import com.tmb.oneapp.transferservice.feature.account.model.TDRequest;
import com.tmb.oneapp.transferservice.feature.account.model.TDResponse;
import com.tmb.oneapp.transferservice.feature.activitylog.model.CustomSlipCompleteActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLog;
import com.tmb.oneapp.transferservice.feature.activitylog.model.TransferActivityLogMapper;
import com.tmb.oneapp.transferservice.feature.authen.model.CommonAuthenWithPayloadRequest;
import com.tmb.oneapp.transferservice.feature.authen.service.OauthService;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.feature.ete.model.AccountStatus;
import com.tmb.oneapp.transferservice.feature.ete.model.EteDepositTermResponse;
import com.tmb.oneapp.transferservice.feature.ete.model.EteTransferAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.FundTransferOwnTMBETESuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TMBDataSuccess;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferAccount;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferAccountV3;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferETERequest;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferV3ETERequest;
import com.tmb.oneapp.transferservice.feature.ete.service.EteService;
import com.tmb.oneapp.transferservice.feature.financiallog.model.FinRequestOwnBankLog;
import com.tmb.oneapp.transferservice.feature.financiallog.model.OwnBankTransferTransactionLog;
import com.tmb.oneapp.transferservice.feature.financiallog.service.FinancialLogService;
import com.tmb.oneapp.transferservice.model.CommonAuthenResult;
import com.tmb.oneapp.transferservice.model.FXCache;
import com.tmb.oneapp.transferservice.model.FXExchangeRate;
import com.tmb.oneapp.transferservice.model.FaceRecognizeResponse;
import com.tmb.oneapp.transferservice.model.VerifyTransactionResult;
import com.tmb.oneapp.transferservice.model.customer.V1CrmProfile;
import com.tmb.oneapp.transferservice.model.request.TransferOnUsConfirmRequest;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferConfirmResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.model.transfer.V1TermDeposit;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferAccount;
import com.tmb.oneapp.transferservice.model.transfer.V1TransferData;
import com.tmb.oneapp.transferservice.utils.AccountNumberUtils;
import com.tmb.oneapp.transferservice.utils.DateUtils;
import com.tmb.oneapp.transferservice.utils.TTBEventLogUtils;
import com.tmb.oneapp.transferservice.utils.TransferValidations;
import feign.FeignException;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_OTHER_TTB;
import static com.tmb.oneapp.transferservice.constant.ActivityLogConstant.ACTIVITY_FIN_AND_TRANS_TRANSFER_OWN_TTB;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.COMMON_AUTH_DESTINATION_OTHER;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.COMMON_AUTH_DESTINATION_OWN;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_FCD_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.CommonAuthenticationConstant.TRANSFER_TD_FEATURE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.APP_VERSION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.BANK_TMB_VALIDATE_DATEFORMAT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.EIGHT_INT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FAILURE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FLOW_NAME_TRANSFER;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FLOW_NAME_TRANSFER_TD;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.IP_ADDRESS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_MODULE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_PIN_REFERENCE_PREFIX;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TTB_BANK_CODE_3DIGITS;


@Service
public class V1OnUsTransferService {

    private static final TMBLogger<V1OnUsTransferService> logger = new TMBLogger<>(V1OnUsTransferService.class);
    @Autowired
    private V1TransfersServiceHelper transfersServiceHelper;
    @Autowired
    private EteService eteService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private FaceRecognizeService frService;
    @Autowired
    private FinancialLogService financialLogService;
    @Autowired
    private OauthService oauthService;
    @Autowired
    private AccountsServiceClient accountsServiceClient;
    @Autowired
    private ConfigurationService configurationService;

    @Value("${td.require.app.version}")
    String tdRequireAppVersion;

    @Value("${common.authen.require.app.version:5.12.0}")
    private String commonAuthenRequireAppVersion;

    @Value("${validate.fr.financial.accu.amount:200000}")
    private Double totalPaymentAccumulateUsageLimit;

    private String getToNickNameOfOwnOrOtherTTB(String toFavoriteName, boolean isTransferToOwnAcct, List<DepositAccount> listDepositAccountOfCrmId, String toAccountNo) {
        if (StringUtils.isNotBlank(toFavoriteName)) {
            return toFavoriteName;
        } else {
            if (isTransferToOwnAcct) {
                DepositAccount toDepositAccount = listDepositAccountOfCrmId.stream().filter(ld -> StringUtils.equals(ld.getAccountNumber(), toAccountNo)).findFirst().orElse(new DepositAccount());

                return toDepositAccount.getProductNickname();
            }
        }
        return null;
    }

    public V1OnUsTransferValidateResponse validate(V1OnUsValidateRequest request,
                                                   String crmId,
                                                   String correlationId,
                                                   HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        long startTime = System.currentTimeMillis();
        transfersServiceHelper.transformRequest(request);
        BigDecimal feeFromETE = new BigDecimal("0.00");
        boolean isTransferToOwnAccount = false;
        VerifyTransactionResult verifyTransactionResult = null;
        List<DepositAccount> accounts = customerService.getAllAccountsTransfer(correlationId, crmId);
        try {
            isTransferToOwnAccount = checkTransferToOwnAccount(request.getToAccountNo(), accounts);
            String toAccType = AccountNumberUtils.getAccountTypeByAccountNo(request.getToAccountNo());
            String fromAccType = AccountNumberUtils.getAccountTypeByAccountNo(request.getFromAccountNo());

            checkBackwardCompatible(headers.getFirst(APP_VERSION), fromAccType);

            V1CrmProfile profile = customerService.getCrmProfile(correlationId, crmId);
            boolean isFcd = AccountNumberUtils.isFcdAccount(request.getFromAccountNo());
            FcdData fcdData = validateConvertAmount(request, isFcd, accounts, isTransferToOwnAccount, profile);
            BigDecimal amountTHB = fcdData.amountTHB;
            String unitCurrency = fcdData.unitCurrency;

            //call ETE to get Fee
            BigDecimal feeAmountFromTD = BigDecimal.ZERO;
            BigDecimal interest = BigDecimal.ZERO;
            BigDecimal penalty = BigDecimal.ZERO;
            BigDecimal tax = BigDecimal.ZERO;
            BigDecimal principal = BigDecimal.ZERO;
            BigDecimal netAmount = BigDecimal.ZERO;
            String maturityDate = TransferServiceConstant.BLANK;
            String chargeType = TransferServiceConstant.BLANK;
            EteDepositTermResponse.Account eteAccount;
            boolean isFromTermDepositAccount = fromAccType.equals(ACCOUNT_TYPE_TERM_DEPOSIT);
            if (isFromTermDepositAccount) {
                EteDepositTermResponse tdResponse = eteService.getDepositWithdrawalInfo(request);
                eteAccount = tdResponse.getAccount();
                EteDepositTermResponse.WithdrawalInfo withdrawInfo = eteAccount.getWithdrawalInfo();
                interest = withdrawInfo.getInterestAmounts();
                penalty = withdrawInfo.getPenaltyAmounts();
                tax = withdrawInfo.getTaxAmounts();
                netAmount = withdrawInfo.getOutstandingBalances();
                maturityDate = withdrawInfo.getNextMaturityDate();
                principal = request.getAmount();
                BigDecimal totalInterest = withdrawInfo.getTotalInterest();
                chargeType = getChargeType(totalInterest, chargeType);
                feeAmountFromTD = totalInterest.subtract(tax).abs().setScale(2, RoundingMode.DOWN);
            }

            verifyTransactionResult = validateVerifyTransaction(amountTHB, headers, isTransferToOwnAccount, profile, isFromTermDepositAccount);
            CommonAuthenticationInformation commonAuthenticationInformation =
                    getCommonAuthenticationInformation(request, verifyTransactionResult, profile, amountTHB, isFcd);
            Optional.ofNullable(commonAuthenticationInformation).ifPresent(info -> info.setAmount(amountTHB.toString()));
            boolean isRequireCommonAuthen = verifyTransactionResult.commonAuthenResult().isRequireCommonAuthen() || isFromTermDepositAccount;

            String transactionRef = transfersServiceHelper.generateTransactionRef(TRANSFER_REFERENCE_NUMBER_PREFIX, EIGHT_INT);
            String transId = transfersServiceHelper.generateTransId(crmId);
            String currentDate = DateUtils.getCurrentDateTTBValidateFormat();

            FundTransferOwnTMBETESuccess eteFee;
            if(StringUtils.isNotBlank(request.getToFinancialId())) {
                eteFee = eteService.validateFundTransferV3
                        (
                                request.getToAccountNo(),
                                request.getFromAccountNo(),
                                request.getToFinancialId(),
                                request.getToFinancialId(),
                                request.getDepositNo(),
                                request.getAmount().setScale(2, RoundingMode.DOWN),
                                currentDate
                        );
            } else {
                eteFee = eteService.validateFundTransfer
                        (
                                request.getToAccountNo(),
                                request.getFromAccountNo(),
                                request.getDepositNo(),
                                request.getAmount().setScale(2, RoundingMode.DOWN),
                                currentDate
                        );
            }

            EteTransferAccount eteTransferAccountAccount =
                    getEteTransferAccount(request, crmId, correlationId, toAccType, request.getToFinancialId());
            validateAccountStatus(eteTransferAccountAccount);

            feeFromETE = BigDecimal.valueOf(eteFee.getData().getFeeAmount()).setScale(2, RoundingMode.DOWN);
            String toAccountName = eteTransferAccountAccount.getTitle();
            V1TransferData transferData = new V1TransferData();
            transferData.setTransId(transId);
            transferData.setFxRate(request.getFxRate());

            V1TransferAccount fromAccount = new V1TransferAccount();
            fromAccount.setAccountNo(request.getFromAccountNo());
            fromAccount.setAccountType(fromAccType);
            fromAccount.setFinancialId(request.getFromFinancialId());
            fromAccount.setCurrency(request.getFromCurrency());
            transferData.setFromAccount(fromAccount);

            transferData.setFromAccountNickname(request.getDepositAccount().getProductNickname());
            transferData.setFromAccountName(request.getDepositAccount().getAccountName());

            V1TransferAccount toAccount = new V1TransferAccount();
            toAccount.setAccountNo(request.getToAccountNo());
            toAccount.setAccountType(toAccType);
            toAccount.setFinancialId(request.getToFinancialId());
            toAccount.setCurrency(request.getToCurrency());
            transferData.setToAccount(toAccount);

            transferData.setToAccountName(toAccountName);
            transferData.setToAccountNickname(getToNickNameOfOwnOrOtherTTB(request.getToFavoriteName(), isTransferToOwnAccount, accounts, request.getToAccountNo()));
            transferData.setToFavoriteNickname(request.getToFavoriteName());

            V1TermDeposit termDeposit = new V1TermDeposit();
            termDeposit.setTdInterestAmount(interest.toString());
            termDeposit.setTdTaxAmount(tax.toString());
            termDeposit.setTdNetAmount(netAmount.toString());
            termDeposit.setPenaltyAmount(penalty.toString());
            termDeposit.setTdMaturityDate(maturityDate);
            transferData.setTermDeposit(termDeposit);

            transferData.setBankCode(TransferServiceConstant.TTB_BANK_CODE);
            transferData.setAmount(request.getAmount().setScale(2, RoundingMode.DOWN));
            transferData.setAmountTHB(amountTHB.setScale(2, RoundingMode.DOWN));
            transferData.setPostedDate(currentDate);
            transferData.setTransactionReference(transactionRef);
            transferData.setDepositNo(request.getDepositNo());
            transferData.setFeeFromETE(feeFromETE);
            transferData.setFeeAmountFromTD(feeAmountFromTD);
            transferData.setChargeType(chargeType);
            transferData.setFlow(request.getFlow());
            transferData.setMemo(request.getNote());
            transferData.setCategoryId(request.getCategoryId());
            transferData.setIsToOwnAccount(isTransferToOwnAccount);
            transferData.setRequirePin(verifyTransactionResult.isRequirePin());
            transferData.setIsRequireFr(Optional.ofNullable(verifyTransactionResult.faceRecognizeResponse()).map(FaceRecognizeResponse::getIsRequireFr).orElse(false));
            transferData.setPaymentAccuUsgAmt(Optional.ofNullable(verifyTransactionResult.faceRecognizeResponse()).map(FaceRecognizeResponse::getPaymentAccuUsgAmt).orElse(null));
            transferData.setRequireCommonAuthen(isRequireCommonAuthen);
            transferData.setCommonAuthenticationInformation(commonAuthenticationInformation);
            transferData.setUnitCurrency(unitCurrency);
            convertJavaObjectToString(transferData);
            transfersServiceHelper.saveDataToCache(transId, transferData);

            TransferActivityLog transferActivityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, feeFromETE, request, isTransferToOwnAccount, verifyTransactionResult);
            transfersServiceHelper.publishActivityTransaction(transferActivityLog);

            V1OnUsTransferValidateResponse onUsTransferValidateResponse = new V1OnUsTransferValidateResponse();
            onUsTransferValidateResponse.setInterest(interest);
            onUsTransferValidateResponse.setPrincipal(principal);
            onUsTransferValidateResponse.setPenalty(penalty);
            onUsTransferValidateResponse.setNetAmount(netAmount);
            onUsTransferValidateResponse.setTax(tax);
            onUsTransferValidateResponse.setFee(feeFromETE);
            onUsTransferValidateResponse.setAmount(request.getAmount());
            onUsTransferValidateResponse.setIsRequireConfirmPin(verifyTransactionResult.isRequirePin());
            onUsTransferValidateResponse.setToAccountName(toAccountName);
            onUsTransferValidateResponse.setTransId(transId);
            onUsTransferValidateResponse.setIsRequireFr(Optional.ofNullable(verifyTransactionResult.faceRecognizeResponse()).map(FaceRecognizeResponse::getIsRequireFr).orElse(false));
            onUsTransferValidateResponse.setIsRequireCommonAuthen(isRequireCommonAuthen);
            onUsTransferValidateResponse.setCommonAuthenticationInformation(commonAuthenticationInformation);

            eventBusinessLogging(profile, verifyTransactionResult, startTime);

            return onUsTransferValidateResponse;
        } catch (Exception e) {
            TransferActivityLog transferActivityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(headers, feeFromETE, request, isTransferToOwnAccount, verifyTransactionResult);
            transferActivityLog.setFailureStatusWithReasonFromException(e);
            transfersServiceHelper.publishActivityTransaction(transferActivityLog);
            throw e;
        }
    }

    private FcdData validateConvertAmount(V1OnUsValidateRequest request,
                                          boolean isFcd,
                                          List<DepositAccount> accounts,
                                          boolean isTransferToOwnAccount,
                                          V1CrmProfile profile) throws TMBCommonException {
        FcdData fcdData;
        if(isFcd) {
            Optional<DepositAccount> optional = getFormOwnAccount(request.getFromAccountNo(), accounts);
            fcdData = validateTransactionFCD(request, optional.orElse(null), isTransferToOwnAccount, profile);
        } else {
            validateTransactionExceeded(!isTransferToOwnAccount, profile, request.getAmount().doubleValue());
            fcdData = new FcdData(request.getAmount(), null);
        }
        return fcdData;
    }

    record FcdData(BigDecimal amountTHB, String unitCurrency) {

    }

    private FcdData validateTransactionFCD(V1OnUsValidateRequest request,
                                           DepositAccount formAccount,
                                           boolean isTransferToOwnAccount,
                                           V1CrmProfile crmProfile) throws TMBCommonException {
        if (formAccount != null) {
            FXExchangeRate fxExchangeRate =
                    getFxRate(request.getFxTransId(), formAccount.getBalanceCurrency());

            request.setFxRate(fxExchangeRate.getBuyRate());
            request.setFromFinancialId(formAccount.getFinancialId());
            request.setToFinancialId(getFinancialId(request.getToAccountNo(), formAccount.getAcctCtl2()));
            request.setFromCurrency(formAccount.getBalanceCurrency());
            request.setToCurrency(formAccount.getBalanceCurrency());

            BigDecimal amountTHB = convertAmountToTHB(request, fxExchangeRate);
            if(AccountNumberUtils.isFcdAccount(request.getToAccountNo())
                    && "CDA".equalsIgnoreCase(AccountNumberUtils.getAccountTypeByAccountNo(request.getToAccountNo()))) {
                TransferModuleModel transferModuleModel = configurationService.getTransferConfiguration();
                validateFcdTdMinimumAmount(transferModuleModel.getFcdTdMinimumAmount(), amountTHB);
            }
            validateTransactionExceeded(!isTransferToOwnAccount, crmProfile, amountTHB.doubleValue());
            return new FcdData(amountTHB, fxExchangeRate.getUnitCurrency());
        } else {
            throw new TMBCommonException(ResponseCode.ACCOUNT_NOT_ELIGIBLE.getCode(),
                    ResponseCode.ACCOUNT_NOT_ELIGIBLE.getMessage(),
                    ResponseCode.ACCOUNT_NOT_ELIGIBLE.getService(),
                    HttpStatus.BAD_REQUEST,
                    null) ;
        }
    }

    private FXExchangeRate getFxRate(String fxTransId, String balanceCurrency) throws TMBCommonException {
        FXCache cacheOnUs = (FXCache) transfersServiceHelper.getTransferDraftData(fxTransId, FXCache.class);
        return Optional.ofNullable(cacheOnUs)
                .map(FXCache::getFxExchangeRates)
                .orElse(Collections.emptyList())
                .stream()
                .filter(rate -> balanceCurrency.equalsIgnoreCase(rate.getRtCcy()))
                .findFirst().orElseThrow(() -> new TMBCommonException(ResponseCode.FAILED.getCode(),
                        ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService(),
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        null));
    }

    private static String getChargeType(BigDecimal totalInterest, String chargeType) {
        if (totalInterest.doubleValue() < 0) {
            chargeType = TransferServiceConstant.TRANSFER_TD_NEG_INTEREST;
        }
        return chargeType;
    }

    private static void convertJavaObjectToString(V1TransferData transferData) {
        try {
            String test = TMBUtils.convertJavaObjectToString(transferData);
            logger.info("{}", test);
        } catch (Exception ex) {
            logger.error("");
        }
    }

    private void validateTransactionExceeded(boolean isTransferToOwnAccount, V1CrmProfile crmProfile, double request)
            throws TMBCommonException {
        if (isTransferToOwnAccount) {
            boolean isTransactionExceeded = transfersServiceHelper.checkTransactionLimited(crmProfile, request);
            if (isTransactionExceeded) {
                throw new TMBCommonException(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(),
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(),
                        ResponseCode.DAILY_LIMIT_EXCEEDED.getService(),
                        HttpStatus.BAD_REQUEST,
                        null);
            }
        }
    }

    private static void validateAccountStatus(EteTransferAccount eteTransferAccountAccount) throws TMBCommonException {
        String status = Optional.of(eteTransferAccountAccount).map(EteTransferAccount::getStatus)
                .map(AccountStatus::getStatusAccount).orElse("");
        if(status.startsWith("Closed") || status.startsWith("Locked") || status.startsWith("Frozen")) {
            throw new TMBCommonException(ResponseCode.INVALID_ACCOUNT_STATUS.getCode(),
                    ResponseCode.INVALID_ACCOUNT_STATUS.getMessage(),
                    ResponseCode.INVALID_ACCOUNT_STATUS.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }

    private String getFinancialId(String accountNo, String acctCtl2) {
        String acctCtl3 = StringUtils.leftPad(StringUtils.left(accountNo, 3), 4, "0");
        String accountType = AccountNumberUtils.getAccountTypeByAccountNo(accountNo);
        String acctCtl4 = AccountNumberUtils.getAccountControl4ByAccountType(accountType);
        return "0011" + acctCtl2 + acctCtl3 + acctCtl4;
    }

    private EteTransferAccount getEteTransferAccount(V1OnUsValidateRequest request,
                                                     String crmId, String correlationId,
                                                     String toAccType,
                                                     String financialId)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        if(StringUtils.isNotBlank(financialId)) {
            return eteService.getDepositAccount(request.getToAccountNo(), crmId, correlationId, toAccType, financialId);
        } else {
            return eteService.getDepositAccount(request.getToAccountNo(), crmId, correlationId, toAccType);
        }
    }

    private void eventBusinessLogging(V1CrmProfile crmProfile, VerifyTransactionResult verifyTransactionResult, long startTime) {
        HashMap<String, Object> customData = new HashMap<>();
        customData.put("crmId", crmProfile.getCrmId());
        customData.put("pinfree", verifyTransactionResult.commonAuthenResult().isPinFree());
        customData.put("ddp", verifyTransactionResult.commonAuthenResult().getIsForceFr());
        TTBEventLogUtils.sendBusinessEventLog(TTBEventStatus.SUCCESS, "/on-us/validate", HttpStatus.OK.value(), (int) (System.currentTimeMillis() - startTime), customData);
    }

    private VerifyTransactionResult validateVerifyTransaction(BigDecimal amountTHB,
                                                              HttpHeaders headers,
                                                              boolean isTransferToOwnAccount,
                                                              V1CrmProfile crmProfile,
                                                              boolean isFromTermDepositAccount) throws TMBCommonException {
        if (isFromTermDepositAccount) {
            var requireCommonAuthen = new CommonAuthenResult().setRequireCommonAuthen(true).setIsForceFr(null).setPinFree(false);
            return new VerifyTransactionResult(false, new FaceRecognizeResponse(), requireCommonAuthen);
        }
        return transfersServiceHelper.validateIsRequireVerifyTransaction(headers, amountTHB, isTransferToOwnAccount, crmProfile);
    }

    private CommonAuthenticationInformation getCommonAuthenticationInformation(V1OnUsValidateRequest request,
                                                                               VerifyTransactionResult verifyTransactionResult,
                                                                               V1CrmProfile crmProfile,
                                                                               BigDecimal amountTHB,
                                                                               boolean isFcd) {
        String fromAccType = AccountNumberUtils.getAccountTypeByAccountNo(request.getFromAccountNo());
        boolean isFromAccTypeTD = fromAccType.equals(ACCOUNT_TYPE_TERM_DEPOSIT);
        String featureId = TRANSFER_FEATURE_ID;
        String flowName = FLOW_NAME_TRANSFER;
        String destination = "";
        if(isFcd) {
            featureId = TRANSFER_FCD_FEATURE_ID;
            destination = COMMON_AUTH_DESTINATION_OTHER;
        } else {
            if (isFromAccTypeTD) {
                featureId = TRANSFER_TD_FEATURE_ID;
                flowName = FLOW_NAME_TRANSFER_TD;
                destination = COMMON_AUTH_DESTINATION_OWN;
            }
        }

        if (verifyTransactionResult.commonAuthenResult().isRequireCommonAuthen()) {
            BigDecimal totalPaymentAccumulateUsage = crmProfile.getPaymentAccuUsgAmt().add(amountTHB);
            return new CommonAuthenticationInformation()
                    .setFeatureId(featureId)
                    .setDestination(destination)
                    .setTotalPaymentAccumulateUsage(totalPaymentAccumulateUsage)
                    .setToBankCode(TTB_BANK_CODE_3DIGITS)
                    .setFlowName(flowName);
        }

        return null;
    }

    private boolean checkTransferToOwnAccount(String toAccountNo, List<DepositAccount> depositAccounts) {
        return depositAccounts.stream().anyMatch(cd -> StringUtils.equals(cd.getAccountNumber(), toAccountNo));
    }

    private Optional<DepositAccount> getFormOwnAccount(String fromAccountNo, List<DepositAccount> depositAccounts) {
        return depositAccounts.stream().filter(cd -> StringUtils.equals(cd.getAccountNumber(), fromAccountNo)).findFirst();
    }

    private void checkBackwardCompatible(String appVersion, String fromAccType) throws TMBCommonException {
        boolean isFromAccTypeTD = fromAccType.equals(ACCOUNT_TYPE_TERM_DEPOSIT);
        if (!isFromAccTypeTD) {
            return;
        }

        if (StringUtils.isBlank(appVersion)) {
            logger.error("[IN] App-Version is null, [app-version = {}]", appVersion);
            throw new TMBCommonException(ResponseCode.PB_UPDATE_APP.getCode(), ResponseCode.PB_UPDATE_APP.getMessage(),
                    ResponseCode.PB_UPDATE_APP.getService(), HttpStatus.BAD_REQUEST, null);
        }

        boolean isCurrentVersionLowerThanRequireVersion = (VersionUtils.compare(appVersion, tdRequireAppVersion) < 0);
        if (isCurrentVersionLowerThanRequireVersion) {
            logger.error("[IN] App-Version lower than Require App, [app-version = {}, requireAppVersion = {}]", appVersion, tdRequireAppVersion);
            throw new TMBCommonException(ResponseCode.PB_UPDATE_APP.getCode(), ResponseCode.PB_UPDATE_APP.getMessage(),
                    ResponseCode.PB_UPDATE_APP.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    public V1OnUsTransferConfirmResponse confirm(@Valid TransferOnUsConfirmRequest request, String crmId, String correlationId, HttpHeaders header) throws TMBCommonException, IOException, WriterException, ParseException, TMBCustomCommonExceptionWithResponse {
        final String appVersion = header.getFirst(APP_VERSION);
        V1OnUsTransferConfirmResponse transferConfirmResponse = new V1OnUsTransferConfirmResponse();
        V1TransferData cacheOnUs = (V1TransferData) transfersServiceHelper.getTransferDraftData(request.getTransId(), V1TransferData.class);
        transfersServiceHelper.validateDuplicateTransaction(request.getTransId(), cacheOnUs);
        final boolean isTransferToOwnAccount = Boolean.TRUE.equals(cacheOnUs.getIsToOwnAccount());
        //8.1 set-up activity log message
        String transactionDateTime = DateUtils.getCurrentDateTime();
        TransferActivityLog transferActivityLog = TransferActivityLogMapper.INSTANCE.toTransferActivityLog(header, cacheOnUs, isTransferToOwnAccount);

        //9.1 set up financial log message
        String refId = cacheOnUs.getTransactionReference();

        String activityFinAndTransId = isTransferToOwnAccount ? ACTIVITY_FIN_AND_TRANS_TRANSFER_OWN_TTB : ACTIVITY_FIN_AND_TRANS_TRANSFER_OTHER_TTB;
        FinRequestOwnBankLog finRequestOwnBankLog = new FinRequestOwnBankLog(correlationId, crmId, refId, cacheOnUs, activityFinAndTransId, transferActivityLog.getActivityTypeId(), transactionDateTime);

        //9.2 set-up transaction log message
        OwnBankTransferTransactionLog transactionLog = new OwnBankTransferTransactionLog(crmId, refId, cacheOnUs, activityFinAndTransId, transactionDateTime);
        //final V1CrmProfile crmProfile = cacheOnUs.getV1CrmProfile();
        final V1CrmProfile crmProfile = customerService.getCrmProfile(correlationId, crmId);


        try {
            verifyTransaction(request, header, cacheOnUs);

            //1 Get transfer data from draft
            boolean isNotTransferToOwnAccount = !isTransferToOwnAccount;
            boolean isFcd = AccountNumberUtils.isFcdAccount(cacheOnUs.getFromAccount().getAccountNo());
            double amount = isFcd ? cacheOnUs.getAmountTHB().doubleValue() : cacheOnUs.getAmount().doubleValue();
            validateTransactionExceeded(isNotTransferToOwnAccount, crmProfile, amount);
            //2 Check transaction limit

            String bankCode = cacheOnUs.getBankCode();
            String bankShortName = transfersServiceHelper.getBankShortName(correlationId, bankCode);
            //3 Get bankShortName

            TMBDataSuccess confirmETERes;
            if(StringUtils.isNotBlank(cacheOnUs.getToAccount().getFinancialId())) {
                finRequestOwnBankLog.setFinFlexValues1(cacheOnUs.getFromAccount().getCurrency());
                finRequestOwnBankLog.setFinFlexValues2(finRequestOwnBankLog.getFinFlexValues1());
                BigDecimal exchangeRate = new BigDecimal(cacheOnUs.getFxRate())
                        .divide(new BigDecimal(cacheOnUs.getUnitCurrency()), 5, RoundingMode.DOWN)
                        .stripTrailingZeros();
                finRequestOwnBankLog.setFinFlexValues4(exchangeRate.toString());
                TransferV3ETERequest eteRequest = setupETERequestV3(cacheOnUs);
                logger.info("On-us transfer confirm request: {}", TMBUtils.convertJavaObjectToString(eteRequest));
                confirmETERes = eteService.confirmTransferV3(eteRequest);
            } else {
                TransferETERequest eteRequest = setupETERequest(cacheOnUs);
                logger.info("On-us transfer confirm request: {}", TMBUtils.convertJavaObjectToString(eteRequest));
                confirmETERes = eteService.confirmTransfer(eteRequest);
            }
            logger.info("On-us transfer confirm response:{}", TMBUtils.convertJavaObjectToString(confirmETERes));
            //4 Call ete to confirm transfer

            final String remainingBalance = getRemainingBalance(correlationId, confirmETERes, cacheOnUs.getFromAccount().getAccountNo());

            //common FR Logic
            if (isNotTransferToOwnAccount) {
                updateRelatedData(crmId, correlationId, appVersion, cacheOnUs, crmProfile);
            }

            //5 Delete account cache
            transfersServiceHelper.deleteAccountCache(header, correlationId, crmId);

            String transactionDatetime = Long.toString(System.currentTimeMillis());

            logger.info("cacheOnUs: {}", cacheOnUs);
            transfersServiceHelper.sendNotification(correlationId, crmId, cacheOnUs.getTransactionReference(), cacheOnUs, transactionDatetime);
            //6. Send notification

            transfersServiceHelper.deleteTransferDraftData(request.getTransId());
            //7 Delete draft


            transferActivityLog.setToBankShortName(bankShortName);
            transactionLog.setToBankShortName(bankShortName);
            transfersServiceHelper.publishActivityTransaction(transferActivityLog);
            //8 Publish activity log

            CustomSlipCompleteActivityLog customSlipCompleteActivityLog = new CustomSlipCompleteActivityLog(header, request.getCustomSlip());
            transfersServiceHelper.publishActivityCustomSlip(customSlipCompleteActivityLog);

            //9 Publish Financial and Transaction log
            finRequestOwnBankLog.setTxnBal(remainingBalance);

            financialLogService.saveLogFinancialAndTransactionEvent(correlationId, finRequestOwnBankLog, transactionLog
                    , isFcd, cacheOnUs, isTransferToOwnAccount?"Y":"N" );

            String qr = !cacheOnUs.getFromAccount().getAccountType().equals("CDA")
                    && !cacheOnUs.getToAccount().getAccountType().equals("CDA")
                    ? generateQR(cacheOnUs.getTransactionReference()) : null;

            transferConfirmResponse.setReferenceNo(cacheOnUs.getTransactionReference());
            transferConfirmResponse.setRemainingBalance(remainingBalance);
            transferConfirmResponse.setTransferCreatedDatetime(getDateFormatFrontEndFromTxnDt(transactionDatetime));
            transferConfirmResponse.setIsToOwnAccount(isTransferToOwnAccount);
            transferConfirmResponse.setQr(qr);
            //10 Mapping response
        } catch (Exception ex) {
            logger.error("Exception: {}", ex);
            transferActivityLog.setFailureStatusWithReasonFromException(ex);
            finRequestOwnBankLog.setFailureStatusWithErrorCodeFromException(ex);
            transactionLog.setTransactionStatus(FAILURE);

            transfersServiceHelper.publishActivityTransaction(transferActivityLog);
            financialLogService.saveLogFinancialAndTransactionEvent(correlationId, finRequestOwnBankLog, transactionLog);
            throw ex;
        }

        return transferConfirmResponse;
    }

    private void updateRelatedData(String crmId, String correlationId, String appVersion, V1TransferData cacheOnUs, V1CrmProfile crmProfile) throws TMBCommonException {
        boolean shouldValidateAllTransactionWithCommonAuthen = appVersion != null && VersionUtils.compare(appVersion, commonAuthenRequireAppVersion) >= 0;
        if (shouldValidateAllTransactionWithCommonAuthen) {
            transfersServiceHelper.updateUsageAccumulation(correlationId, crmId, cacheOnUs.getAmountTHB(), crmProfile, cacheOnUs.isRequireCommonAuthen());
            boolean isShouldUpdatePinFreeCount = !cacheOnUs.isRequireCommonAuthen();
            if (isShouldUpdatePinFreeCount) {
                transfersServiceHelper.updatePinFreeCount(correlationId, crmId, crmProfile);
            }
        } else {
            frService.updatePaymentAccumulateUsageAmount(correlationId, crmId, cacheOnUs.getAmountTHB(), cacheOnUs.getIsRequireFr(), cacheOnUs.getPaymentAccuUsgAmt());
            transfersServiceHelper.updateDailyUsage(correlationId, crmId, cacheOnUs.getAmountTHB().doubleValue(), crmProfile, cacheOnUs.isRequirePin());
        }
    }

    private void verifyTransaction(TransferOnUsConfirmRequest request, HttpHeaders headers, V1TransferData cacheOnUs) throws TMBCommonException {
        final boolean isRequireFR = Boolean.TRUE.equals(cacheOnUs.getIsRequireFr());
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String appVersion = headers.getFirst(APP_VERSION);
        final String ipAddress = headers.getFirst(IP_ADDRESS);
        boolean shouldValidateAllTransactionWithCommonAuthen = appVersion != null && VersionUtils.compare(appVersion, commonAuthenRequireAppVersion) >= 0;
        boolean isFromTermDepositAccount = cacheOnUs.getFromAccount().getAccountType().equals(ACCOUNT_TYPE_TERM_DEPOSIT);
        if (shouldValidateAllTransactionWithCommonAuthen) {
            if (cacheOnUs.isRequireCommonAuthen()) {
                CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = getCommonAuthenWithPayloadRequest(request, cacheOnUs);
                oauthService.verifyCommonAuthenWithPayload(headers, commonAuthenWithPayloadRequest);

            }
        } else {
            if (isFromTermDepositAccount && cacheOnUs.isRequireCommonAuthen()) {
                var commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
                commonAuthenWithPayloadRequest.setAmount(String.valueOf(cacheOnUs.getAmountTHB()));
                commonAuthenWithPayloadRequest.setDailyAmount(null);
                commonAuthenWithPayloadRequest.setFlowName(cacheOnUs.getFlow());
                commonAuthenWithPayloadRequest.setRefId(request.getTransId());
                oauthService.verifyCommonAuthenWithPayloadForTermDepositOldVersion(headers, commonAuthenWithPayloadRequest);
            } else {
                if (cacheOnUs.isRequirePin()) {
                    String keyVerify = TRANSFER_PIN_REFERENCE_PREFIX + request.getTransId();
                    oauthService.verifyPinCache(correlationId, crmId, keyVerify, TRANSFER_MODULE);
                }
            }

            if (isRequireFR) {
                transfersServiceHelper.validateCommonFR(request.getFrUuid(), correlationId, crmId, ipAddress, cacheOnUs.getFlow());
            }
        }
    }

    @NotNull
    private static CommonAuthenWithPayloadRequest getCommonAuthenWithPayloadRequest(TransferOnUsConfirmRequest request, V1TransferData cacheOnUs) {
        var commonAuthen = cacheOnUs.getCommonAuthenticationInformation();
        CommonAuthenWithPayloadRequest commonAuthenWithPayloadRequest = new CommonAuthenWithPayloadRequest();
        commonAuthenWithPayloadRequest.setFeatureId(commonAuthen.getFeatureId());
        commonAuthenWithPayloadRequest.setAmount(String.valueOf(cacheOnUs.getAmountTHB()));
        commonAuthenWithPayloadRequest.setDailyAmount(String.valueOf(commonAuthen.getTotalPaymentAccumulateUsage()));
        commonAuthenWithPayloadRequest.setBankCode(commonAuthen.getToBankCode());
        commonAuthenWithPayloadRequest.setRefId(request.getTransId());
        commonAuthenWithPayloadRequest.setFlowName(cacheOnUs.getCommonAuthenticationInformation().getFlowName());
        return commonAuthenWithPayloadRequest;
    }

    private String getRemainingBalance(String correlationId, TMBDataSuccess confirmETERes, String fromAccountNoOnCache) throws TMBCommonException {
        String result;
        String remainingBalanceFromETE = confirmETERes.getFromAccount().getAvailBalance();
        boolean isFromTermDepositAccount = confirmETERes.getFromAccount().getAccountType().equals(ACCOUNT_TYPE_TERM_DEPOSIT);

        if (isFromTermDepositAccount) {
            BigDecimal tdRemainingBalance = getTermDepositRemainingBalance(correlationId, fromAccountNoOnCache, confirmETERes.getFromAccount().getAccountType());
            result = String.valueOf(tdRemainingBalance);
        } else {
            result = remainingBalanceFromETE;
        }

        return result;
    }

    public String getDateFormatFrontEndFromTxnDt(String transactionDateTime) {
        Date dateFromString = new Date(Long.parseLong(transactionDateTime));
        SimpleDateFormat dateFormat = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        return dateFormat.format(dateFromString);
    }

    private String generateQR(String referenceNo) throws IOException, WriterException {
        MiniQR miniQR = new MiniQR(referenceNo);
        return miniQR.drawToBase64(370, 370);
    }

    private TransferETERequest setupETERequest(V1TransferData transferCache) {

        V1TransferAccount fromAccountCache = transferCache.getFromAccount();
        TransferETERequest transferETERequest = new TransferETERequest();
        TransferAccount fromAccount = new TransferAccount();
        transferETERequest.setFromAccount(fromAccount);
        fromAccount.setAccountNo(fromAccountCache.getAccountNo());
        fromAccount.setAccountType(fromAccountCache.getAccountType());
        fromAccount.setFinancialId(fromAccountCache.getFinancialId());
        TransferAccount toAccount = new TransferAccount();

        V1TransferAccount toAccountCache = transferCache.getToAccount();
        transferETERequest.setToAccount(toAccount);
        toAccount.setAccountNo(toAccountCache.getAccountNo());
        toAccount.setAccountType(toAccountCache.getAccountType());
        toAccount.setFinancialId(toAccountCache.getFinancialId());
        transferETERequest.setAmount(transferCache.getAmount().setScale(2, RoundingMode.DOWN));
        transferETERequest.setPostedDate(transferCache.getPostedDate());
        transferETERequest.setTransactionReference(transferCache.getTransactionReference());
        transferETERequest.setChargeType(transferCache.getChargeType());

        BigDecimal fee = (ACCOUNT_TYPE_TERM_DEPOSIT.equals(transferCache.getFromAccount().getAccountType()) ? transferCache.getFeeAmountFromTD() : transferCache.getFeeFromETE());
        transferETERequest.setFeeAmount(fee);

        String toAccountNo = TransferValidations.getToAccountNoOfNormalOrTD(toAccountCache.getAccountNo(), toAccountCache.getAccountType());
        String fromAccountNo = TransferValidations.getFromAccountNoOfNormalOrTD(fromAccountCache.getAccountNo(), fromAccountCache.getAccountType(), transferCache.getDepositNo());

        toAccount.setAccountNo(toAccountNo);
        fromAccount.setAccountNo(fromAccountNo);

        return transferETERequest;
    }

    private TransferV3ETERequest setupETERequestV3(V1TransferData transferCache) {
        V1TransferAccount fromAccountCache = transferCache.getFromAccount();
        TransferV3ETERequest transferETERequest = new TransferV3ETERequest();
        TransferAccountV3 fromAccount = new TransferAccountV3();
        transferETERequest.setFromAccount(fromAccount);
        fromAccount.setAccountNo(fromAccountCache.getAccountNo());
        fromAccount.setAccountType(fromAccountCache.getAccountType());
        fromAccount.setFinancialId(fromAccountCache.getFinancialId());
        TransferAccountV3 toAccount = new TransferAccountV3();
        V1TransferAccount toAccountCache = transferCache.getToAccount();
        transferETERequest.setToAccount(toAccount);
        toAccount.setAccountNo(toAccountCache.getAccountNo());
        toAccount.setAccountType(toAccountCache.getAccountType());
        toAccount.setFinancialId(toAccountCache.getFinancialId());
        transferETERequest.setAmount(transferCache.getAmount().setScale(2, RoundingMode.DOWN));
        transferETERequest.setPostedDate(transferCache.getPostedDate());
        transferETERequest.setTransactionReference(transferCache.getTransactionReference());
        transferETERequest.setChargeType(StringUtils.isNotBlank(transferCache.getChargeType()) ? transferCache.getChargeType() : null);

        BigDecimal fee = (ACCOUNT_TYPE_TERM_DEPOSIT.equals(transferCache.getFromAccount().getAccountType()) ? transferCache.getFeeAmountFromTD() : transferCache.getFeeFromETE());
        transferETERequest.setFeeAmount(fee);
        transferETERequest.setFeeType(ACCOUNT_TYPE_TERM_DEPOSIT.equals(transferCache.getToAccount().getAccountType()) ? "FCF" : null);

        String toAccountNo = TransferValidations.getToAccountNoOfNormalOrTD(toAccountCache.getAccountNo(), toAccountCache.getAccountType());
        String fromAccountNo = TransferValidations.getFromAccountNoOfNormalOrTD(fromAccountCache.getAccountNo(), fromAccountCache.getAccountType(), transferCache.getDepositNo());

        toAccount.setAccountNo(toAccountNo);
        fromAccount.setAccountNo(fromAccountNo);

        return transferETERequest;
    }

    private BigDecimal getTermDepositRemainingBalance(String correlationId, String accountNo, String accountType) throws TMBCommonException {
        try {
            TDRequest tdRequest = new TDRequest()
                    .setAccountNo(accountNo)
                    .setAccountType(accountType);

            String tdRemainingBalance = Optional.ofNullable(accountsServiceClient.fetchTermDepositDetail(correlationId, tdRequest))
                    .filter(f -> f.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(f -> f.getStatus().getCode().equals(ResponseCode.SUCCESS.getCode()))
                    .map(TmbOneServiceResponse::getData)
                    .map(TDResponse::getAccountBalance)
                    .orElseThrow(() -> {
                        logger.error("Got Error when FetchTermDepositDetails. [correlationId = {}, accountNo = {}]", correlationId, accountNo);
                        return new TMBCommonException(ResponseCode.GENERAL_ERROR.getCode(), ResponseCode.GENERAL_ERROR.getMessage(),
                                ResponseCode.GENERAL_ERROR.getService(), HttpStatus.BAD_REQUEST, null);
                    });

            return new BigDecimal(tdRemainingBalance).setScale(2, RoundingMode.DOWN);

        } catch (FeignException e) {
            logger.info("Exception getTermDepositRemainingBalance :", e);
            throw new TMBCommonException(ResponseCode.GENERAL_ERROR.getCode(), ResponseCode.GENERAL_ERROR.getMessage(),
                    ResponseCode.GENERAL_ERROR.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @NotNull
    private static BigDecimal convertAmountToTHB(V1OnUsValidateRequest request,
                                                 FXExchangeRate fxExchangeRate) throws TMBCommonException {
        try {
            BigDecimal unitCurrency = new BigDecimal(fxExchangeRate.getUnitCurrency());
            BigDecimal amountOverSea = request.getAmount();
            BigDecimal expectRate = new BigDecimal(fxExchangeRate.getBuyRate());
            return expectRate
                    .multiply(amountOverSea)
                    .divide(unitCurrency, 7, RoundingMode.HALF_UP)
                    .setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            logger.error("Can not convert amont to THB: {}", e.getMessage(), e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    e);
        }
    }

    private static void validateFcdTdMinimumAmount(BigDecimal minimumAmountTHB, BigDecimal amountTHB)
            throws TMBCommonException {
        if (minimumAmountTHB.compareTo(amountTHB) > 0) {
            throw new TMBCommonException(ResponseCode.AMOUNT_LESS_THEN_MINIMUM.getCode(),
                    ResponseCode.AMOUNT_LESS_THEN_MINIMUM.getMessage(),
                    ResponseCode.AMOUNT_LESS_THEN_MINIMUM.getService(),
                    HttpStatus.BAD_REQUEST,
                    null);
        }
    }

}

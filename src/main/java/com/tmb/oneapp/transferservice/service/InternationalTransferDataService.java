package com.tmb.oneapp.transferservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mongodb.MongoException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.internationaltransfer.OTTCountry;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.InterestRateODSResponse;
import com.tmb.oneapp.transferservice.model.InterestRateResponse;
import com.tmb.oneapp.transferservice.model.InternationalTransferPurpose;
import com.tmb.oneapp.transferservice.model.ODSRequest;
import com.tmb.oneapp.transferservice.feign.ODSFeignClient;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.*;


@Service
public class InternationalTransferDataService {
    private static final TMBLogger<InternationalTransferDataService> logger = new TMBLogger<>(InternationalTransferDataService.class);
    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;
    private final ODSFeignClient odsFeignClient;

    public InternationalTransferDataService(MongoTemplate mongoTemplate, CacheService cacheService, ODSFeignClient odsFeignClient) {
        this.mongoTemplate = mongoTemplate;
        this.cacheService = cacheService;
        this.odsFeignClient = odsFeignClient;
    }

    public List<InternationalTransferPurpose> fetchPurposeMasterData() throws TMBCommonException {
        boolean isRedisDown = false;
        List<InternationalTransferPurpose> purposeList = null;

        try {
            purposeList = getDataFromCache(TransferServiceConstant.COMMON_TRANSFER_PURPOSE_MASTER_DATA, new TypeReference<>() {});
        } catch (Exception e) {
            isRedisDown = true;
            logger.warn(TransferServiceConstant.CACHE_FETCHING_ERROR_MESSAGE, e);
        }

        if (purposeList != null) {
            return purposeList;
        }

        try {
            purposeList = mongoTemplate.findAll(InternationalTransferPurpose.class);
            if (!isRedisDown) {
                setCache(purposeList, TransferServiceConstant.COMMON_TRANSFER_PURPOSE_MASTER_DATA);
            }

            return purposeList;

        } catch (MongoException e) {
            throw new TMBCommonException(ResponseCode.DB_FAILED.getCode(), ResponseCode.DB_FAILED.getMessage(),
                    ResponseCode.DB_FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    public List<OTTCountry> fetchOttCountryMasterData() throws TMBCommonException {
        boolean isRedisDown = false;
        List<OTTCountry> countryList = null;

        try {
            countryList = getDataFromCache(TransferServiceConstant.COMMON_TRANSFER_OTT_COUNTRY_MASTER_DATA, new TypeReference<>() {});
        } catch (Exception e) {
            isRedisDown = true;
            logger.warn(TransferServiceConstant.CACHE_FETCHING_ERROR_MESSAGE, e);
        }

        if (countryList != null) {
            return countryList;
        }

        try {
            countryList = mongoTemplate.findAll(OTTCountry.class);
            if (!isRedisDown) {
                setCache(countryList, TransferServiceConstant.COMMON_TRANSFER_OTT_COUNTRY_MASTER_DATA);
            }

            return countryList;
        } catch (MongoException e) {
            throw new TMBCommonException(ResponseCode.DB_FAILED.getCode(), ResponseCode.DB_FAILED.getMessage(),
                    ResponseCode.DB_FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    public <T> T getDataFromCache(String cacheName, final TypeReference<T> typeReference) throws JsonProcessingException {
        String data = cacheService.get(cacheName);
        return data == null ? null : (T) TMBUtils.convertStringToJavaObjWithTypeRef(data, typeReference);
    }

    public <T> void setCache(T data, String cacheName) {
        String value = null;
        try {
            value = TMBUtils.convertJavaObjectToString(data);
        } catch (JsonProcessingException e) {
            logger.error("Cannot serialized cache for data {}", e);
        }
        cacheService.set(cacheName, value);
    }

    public InterestRateResponse getInterestRate(String productCode) throws IOException, TMBCommonException {
        HttpHeaders headers = new HttpHeaders();

        headers.set(HEADER_REQUEST_UUID, UUID.randomUUID().toString());
        headers.set(HEADER_APP_ID, CHANNEL_MB);
        headers.add(HEADER_SERVICE_NAME, "get-rate-by-product-group");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        String requestDateTime = simpleDateFormat.format(new Date());
        headers.set(HEADER_REQUEST_DATE_TIME, requestDateTime);
        logger.info("request header ODS {}", TMBUtils.convertJavaObjectToString(headers));

        ODSRequest odsRequest = new ODSRequest();
        odsRequest.setProductGroup(productCode);
        logger.info("request body ODS {}",TMBUtils.convertJavaObjectToString(odsRequest));
        ResponseEntity<InterestRateODSResponse> interestRateODS = odsFeignClient.getInterestRate(headers, odsRequest);
        InterestRateResponse interestRate = Objects.requireNonNull(interestRateODS.getBody()).getInterestRates()
                .stream().filter(r -> r.getISMAXOFGRP().equalsIgnoreCase("Y")
                        && r.getInActiveFlag().equalsIgnoreCase("N")).findFirst()
                .orElseThrow(() -> new TMBCommonException("Interest rate filter failed"));
        logger.info("interestRateODS response: {}", TMBUtils.convertJavaObjectToString(interestRate));
        return interestRate;

    }

}

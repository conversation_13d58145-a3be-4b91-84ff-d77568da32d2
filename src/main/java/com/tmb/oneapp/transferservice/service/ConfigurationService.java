package com.tmb.oneapp.transferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.repository.TransferModuleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ConfigurationService {
    private static final TMBLogger<ConfigurationService> logger = new TMBLogger<>(ConfigurationService.class);

    private final TransferModuleRepository transferModuleRepository;

    @Cacheable(cacheNames = "transfer_module_config", key = "'transfer_module_config'", unless = "#result==null")
    public TransferModuleModel getTransferConfiguration() throws TMBCommonException {
        try {
            Optional<TransferModuleModel> data = transferModuleRepository.findById("transfer_module");
            return data.orElse(null);
        } catch (Exception ex){
            logger.error("Error when retrieve data from transfer_module: {}", ex);
            throw new TMBCommonException(ResponseCode.DB_FAILED.getCode(), ResponseCode.DB_FAILED.getMessage(),
                    ResponseCode.DB_FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }
}

package com.tmb.oneapp.transferservice.configuration;

import com.tmb.oneapp.transferservice.utils.MyTaskDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;

@Configuration
public class AppConfig {

    @Bean
    TaskDecorator mdcCopyTaskDecorator() {
        return new MyTaskDecorator();
    }

}

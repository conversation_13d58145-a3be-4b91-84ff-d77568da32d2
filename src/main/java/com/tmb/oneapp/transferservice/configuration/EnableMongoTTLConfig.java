package com.tmb.oneapp.transferservice.configuration;


import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

import javax.annotation.PostConstruct;

@Configuration
public class EnableMongoTTLConfig {

  private final MongoTemplate mongoTemplate;

  public EnableMongoTTLConfig(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @PostConstruct
  public void initExpiredAtIndexesDraftTransfer() {
    IndexOperations indexOps = mongoTemplate.indexOps("draft_transfer");
    // Check if the index already exists
    boolean indexExists = indexOps.getIndexInfo().stream()
        .anyMatch(indexInfo -> indexInfo.getName().equals("expiredAt_1"));
    if (!indexExists) {
      Index index = new Index().on("expiredAt", Sort.Direction.ASC).expire(0L);
      indexOps.ensureIndex(index);
    }
  }
}

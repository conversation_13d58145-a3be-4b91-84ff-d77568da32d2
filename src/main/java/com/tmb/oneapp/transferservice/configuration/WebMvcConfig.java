package com.tmb.oneapp.transferservice.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {
        configure.addPathPrefix("/v1/transfer-service",  HandlerTypePredicate.forBasePackage("com.tmb.oneapp.transferservice.controller.v1"));
        configure.addPathPrefix("/v2/transfer-service",  HandlerTypePredicate.forBasePackage("com.tmb.oneapp.transferservice.controller.v2"));
    }
}

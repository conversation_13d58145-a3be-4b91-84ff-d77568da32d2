package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CategoryInfoDataModel {

	@Id
	@Schema(description = "Unique key to save/fetch Category in mongo DB", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("category_id")
	private String categoryCd;

	@Schema(description = "Category name Thai", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("category_name_th")
	private String categoryNameTh;

	@Schema(description = "Category name English", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("category_name_en")
	private String categoryNameEn;

	@Schema(description = "Category Icon", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("category_icon")
	private String categoryIcon;

	@Schema(description = "Category Display Order", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("category_display_Order")
	private String categoryDisplayOrder;

	@Schema(description = "Category Display Transfer", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("category_display_transfer")
	private String categoryDisplayTransfer;


}

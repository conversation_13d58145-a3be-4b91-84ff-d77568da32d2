package com.tmb.oneapp.transferservice.model.customer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerKYCResponse {
    private String rmId;
    private String idNo;
    private String idType;
    private String customerFirstNameEn;
    private String customerFirstNameTh;
    private String customerLastNameEn;
    private String customerLastNameTh;
    private String gender;
    private String email;
    private String emailVerifyStatus;
    private String emailType;
    private String customerType;
    private String customerTitleTh;
}

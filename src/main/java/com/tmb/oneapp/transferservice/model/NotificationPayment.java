package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public abstract class NotificationPayment extends Notification {
    private static final String PATTERN_DATE_WITH_TIME_TH = "d MMM yy - HH:mm น.";
    private static final String PATTERN_DATE_WITH_TIME_EN = "d MMM yy - h:mm a";
    private static final String TH_LOWER_CASE = "th";
    private static final String TH_UPPER_CASE = "TH";
    protected String addDateTimeEN;
    protected String addDateTimeTH;


    protected NotificationPayment(String templateName, String refNo, String crmId, String correlationId, String transactionDateTime) {
        super(templateName, refNo, crmId, correlationId);

        checkTransactionDateTime(transactionDateTime);
    }

    private void checkTransactionDateTime(String transactionDateTime) {
        if (StringUtils.isNotBlank(transactionDateTime)) {
            setDateTime(transactionDateTime);
        }
    }

    private void setDateTime(String transactionDateTime) {
        long covertDateTimeStringToLong = Long.parseLong(transactionDateTime);
        Date date = new Date(covertDateTimeStringToLong);

        this.addDateTimeTH =  new SimpleDateFormat(PATTERN_DATE_WITH_TIME_TH,
                new Locale(TH_LOWER_CASE, TH_UPPER_CASE)).format(date);

        this.addDateTimeEN = new SimpleDateFormat(PATTERN_DATE_WITH_TIME_EN).format(date);
    }
}

package com.tmb.oneapp.transferservice.model.bank;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1CategoryInfoDataModel {
    private String categoryId;
    private String categoryNameTh;
    private String categoryNameEn;
    private String categoryIcon;
    private String categoryDisplayOrder;
    private String categoryDisplayTransfer;
}

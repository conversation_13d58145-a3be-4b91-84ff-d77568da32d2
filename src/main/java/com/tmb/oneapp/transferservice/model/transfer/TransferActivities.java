package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferActivities {
    @JsonProperty("Flow")
    String flow;

    @JsonProperty("reference_activity_type_id")
    private String referenceActivityTypeId;

    @JsonProperty("activity_type_id")
    private String activityId;

    @JsonProperty("activity_type_id_new")
    private String activityIdNew;

    @JsonProperty("crm_id")
    private String crmId;

    @JsonProperty("channel_id")
    private String channelId;

    @JsonProperty("transaction_status")
    private String transactionStatus;

    @JsonProperty("transaction_date")
    private String transactionDate;

    @JsonProperty("from_account_no")
    private String fromAccountNo;

    @JsonProperty("from_account_nickname")
    private String fromAccountNickname;

    @JsonProperty("to_account_no")
    private String toAccountNo;

    @JsonProperty("to_account_nickname")
    private String toAccountNickname;

    @JsonProperty("to_account_name")
    private String toAccountName;

    @JsonProperty("to_bank_short_name")
    private String toBankShortName;

    @JsonProperty("financial_tranfer_amount")
    private String financialTransferAmount;

    @JsonProperty("financial_tranfer_memo")
    private String financialTransferMemo;

    @JsonProperty("financial_tranfer_cr_dr")
    private String financialTransferCRDR;

    @JsonProperty("proxy_type")
    private String proxyType;

    @JsonProperty("proxy_value")
    private String proxyValue;

    @JsonProperty("financial_tranfer_ref_id")
    private String financialTransferRefId;

    private String billerCompCode;
    private String billerRef1;
    private String billerRef2;
    private String billerNameEn;
    private String billerNameTh;
    private String labelRef1En;
    private String labelRef1Th;
    private String labelRef2En;
    private String labelRef2Th;
    private String txnType;
    private String finFlexValues;

}

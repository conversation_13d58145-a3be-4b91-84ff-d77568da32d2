package com.tmb.oneapp.transferservice.model.transfer;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.model.CommonFee;
import com.tmb.oneapp.transferservice.model.CommonTime;
import com.tmb.oneapp.transferservice.model.SchedulePromtpay;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Document(collection = "transfer_module")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferModuleModel {
    @Id
    @Field("_id")
    private String id;

    @Field("withdraw_td_cutoff_time")
    private CommonTime withdrawTdCutoffTime;

    @Field("promptpay_account_fee")
    private List<CommonFee> promptpayAccountFee;

    @Field("promptpay_account_trans_limit")
    private String promptpayAccountTransLimit;

    @Field("promptpay_proxy_trans_limit")
    private String promptpayProxyTransLimit;

    @Field("promptpay_proxy_fee")
    private List<CommonFee> promptpayProxyFee;

    @Field("promptpay_proxy_waive_fee")
    private String promptPayProxyWaiveFee;

    @Field("exchange_trans_limit")
    private String exchangeTransLimit;

    @Field("schedule_promptpay")
    private SchedulePromtpay schedulePromtpay;

    @Field("fr_financial_accu_amount")
    private BigDecimal frFinancialAccuAmount;

    @Field("fr_schedule_flag")
    private Boolean frScheduleFlag;

    @Field("fr_topup_flag")
    private Boolean frTopupFlag;

    @Field("fr_transfer_flag")
    private Boolean frTransferFlag;

    @Field("fr_transfer_ott_flag")
    private Boolean frTransferOttFlag;

    @Field("fr_billpay_trans_limit")
    private BigDecimal frBillpayTransLimit;

    @Field("fr_transfer_trans_limit")
    private BigDecimal frTransferTransLimit;

    @Field("fcd_td_minimum_amount")
    private BigDecimal fcdTdMinimumAmount;
}

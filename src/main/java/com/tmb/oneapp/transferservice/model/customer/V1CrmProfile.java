package com.tmb.oneapp.transferservice.model.customer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class V1CrmProfile {
    String crmId;
    String ebTxnLimitAmt;
    Double ebMaxLimitAmtCurrent;
    String ebMaxLimitAmtHist;
    String ebMaxLimitAmtRequest;
    Double ebAccuUsgAmtDaily;
    String ebCustomerStatusId;
    String ibUserStatusId;
    String mbUserStatusId;
    String referCd;
    String ottMaxLimitAmtCurrent;
    String ottAccuUsgAmtDaily;
    String ottMaxLimitAmtHist;
    String ottMaxLimitAmtRequest;
    String pinFreeSeetingFlag;
    double pinFreeTrLimit;
    String pinFreeBpLimit;
    int pinFreeTxnCount;
    String defaultAcctId;
    String autoSaveSlipMain;
    String autoSaveSlipOwn;
    String quickBalanceSettingFlag;
    BigDecimal paymentAccuUsgAmt;
}

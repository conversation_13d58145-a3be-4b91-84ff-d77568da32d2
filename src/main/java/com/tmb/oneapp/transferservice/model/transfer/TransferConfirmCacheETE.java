package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferAccount;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransferConfirmCacheETE {
    private TransferAccount fromAccount;
    private String fromAccountName;
    private String fromAccountNickname;
    private TransferAccount toAccount;
    private String toAccountName;
    private String toAccountNickname;
    private String toFavoriteNickname;
    private TermDepositCache termDeposit;
    private Boolean toOtherBank;
    private String bankCode;
    private BigDecimal amount;
    private String postedDate;
    private String transactionReference;
    private String depositNo;
    private BigDecimal feeFromETE;
    private BigDecimal feeAmountFromTD;
    private String chargeType;
    private String flow;
    private String memo;
    private String categoryId;
    private Boolean isToOwnAccount;
    private boolean transactionUsed;
    private boolean isRequirePin;
}


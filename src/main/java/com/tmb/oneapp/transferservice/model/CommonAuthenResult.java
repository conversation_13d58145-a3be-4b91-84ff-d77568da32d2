package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class CommonAuthenResult {
    private boolean isRequireCommonAuthen;
    private Boolean isForceFr;
    private boolean isPinFree;
    private Boolean isForceDipChip;
}

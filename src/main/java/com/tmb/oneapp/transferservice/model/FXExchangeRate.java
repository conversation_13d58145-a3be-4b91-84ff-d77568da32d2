package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FXExchangeRate {
    protected String imgUrl;
    protected String currencyDesc;
    protected String sbillRate;
    protected String ottRate;
    protected String rtCcy;
    protected String nostroSwiftCode;
    protected String seq;

    private String bttRate;
    private String buyRate;
    private String unitCurrency;
}


package com.tmb.oneapp.transferservice.model.transfer;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1OnUsTransferValidateResponse {
    private String transId;
    private BigDecimal interest;
    private BigDecimal principal;
    private BigDecimal penalty;
    private BigDecimal netAmount;
    private BigDecimal tax;
    private BigDecimal fee;
    private BigDecimal amount;
    private Boolean isRequireConfirmPin;
    private String toAccountName;
    private Boolean isRequireFr;
    private Boolean isRequireCommonAuthen;
    private CommonAuthenticationInformation commonAuthenticationInformation;
}
package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InterestRateResponse {
    @JsonProperty("rt_grp_07")
    public double rTGRP07;
    @JsonProperty("rt_grp_04")
    public double rTGRP04;
    @JsonProperty("rt_grp_01")
    public double rTGRP01;
    @JsonProperty("rt_grp_08")
    public double rTGRP08;
    @JsonProperty("rt_grp_02")
    public double rTGRP02;
    @JsonProperty("rt_grp_05")
    public double rTGRP05;
    @JsonProperty("rt_grp_09")
    public double rTGRP09;
    @JsonProperty("rt_grp_03")
    public double rTGRP03;
    @JsonProperty("rt_grp_10")
    public double rTGRP10;
    @JsonProperty("is_max_of_grp")
    public String iSMAXOFGRP;
    @JsonProperty("rt_grp_06")
    public double rTGRP06;
    @JsonProperty("inactive_flg")
    public String inActiveFlag;

}
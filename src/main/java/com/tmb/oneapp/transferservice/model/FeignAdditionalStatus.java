package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeignAdditionalStatus {
	@JsonAlias("status_code")
	@JsonProperty("statusCode")
	private String statusCode;
	@JsonAlias({"server_status_code", "code"})
	@JsonProperty("serverStatusCode")
	private String serverStatusCode;
	@JsonProperty("severity")
	private String severity;
	@JsonAlias({"status_desc", "message"})
	@JsonProperty("statusDesc")
	private String statusDesc;
}

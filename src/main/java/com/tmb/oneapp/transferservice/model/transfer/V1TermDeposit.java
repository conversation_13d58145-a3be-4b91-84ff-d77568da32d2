package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1TermDeposit {
    private String tdInterestAmount;
    private String tdTaxAmount;
    private String tdNetAmount;
    private String penaltyAmount;

    private String tdMaturityDate;
}

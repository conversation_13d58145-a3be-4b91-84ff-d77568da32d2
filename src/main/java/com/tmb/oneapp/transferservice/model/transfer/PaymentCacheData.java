package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentCacheData {
    private String bankCode;
    private String note;
    private String flow;
    private String fromAccountNickname;
    private String toFavoriteNickname;
    private String bankShortName;
    private String qr;
    private String amountToSaveFavorite;

    private String billerName;
    private String billerCompCode;
    private String originRef1;
    private String originRef2;
    private String feeBillpay;
    private String fromAccountName;
    private String topUpAccountName;
    private boolean isPayByOwner;

    private String reqToAccountNoToSaveFavorite;
    private String categoryId;
    private boolean isTransferToOwnAccount;
    private String cardNumber;
    private Boolean isCreditCard;

    private boolean preLogin;
    private boolean transactionUsed;

    private boolean isRequiredRef2;
    private boolean allowShareToRd;
    private Boolean isRequireFr;
    private BigDecimal paymentAccuUsgAmt;

    private boolean isRequireCommonAuthentication;
    private CommonAuthenticationInformation commonAuthenticationInformation;
}

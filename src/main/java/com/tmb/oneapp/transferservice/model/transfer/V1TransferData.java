package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.Date;

import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1TransferData implements V1ITransferData {
    private String transId;
    private V1TransferAccount fromAccount;
    private String fromAccountName;
    private String fromAccountNickname;
    private V1TransferAccount toAccount;
    private String toAccountName;
    private String toAccountNickname;
    private String toFavoriteNickname;
    private V1TermDeposit termDeposit;
    private Boolean toOtherBank;
    private String bankCode;
    private BigDecimal amount;
    private BigDecimal amountTHB;
    private String postedDate;
    private String transactionReference;
    private String depositNo;
    private BigDecimal feeFromETE;
    private BigDecimal feeAmountFromTD;
    private String chargeType;
    private String flow;
    private String memo;
    private String categoryId;
    private Boolean isToOwnAccount;
    private boolean isTransactionUsed;
    private boolean isRequirePin;
    private Date expiredAt;
    private Boolean isRequireFr;
    private BigDecimal paymentAccuUsgAmt;
    private boolean isRequireCommonAuthen;
    private CommonAuthenticationInformation commonAuthenticationInformation;
    private String fxRate;
    private String unitCurrency;

    @Override
    public Boolean isTransactionUsed() {
        return this.isTransactionUsed;
    }

    @Override
    public void setTransactionUsed(Boolean value) {
        this.isTransactionUsed = value;
    }
}

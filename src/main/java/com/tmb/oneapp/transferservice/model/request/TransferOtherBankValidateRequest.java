package com.tmb.oneapp.transferservice.model.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.transferservice.model.transfer.DepositAccount;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferOtherBankValidateRequest {
    private  String fromAccountNo;
    private String toAccountNo;
    private String toBankCode;
    private String toFavoriteName;
    private String amount;
    private String categoryId;
    private String note;
    private String flow;
    private String qr;
    private DepositAccount depositAccount;
}

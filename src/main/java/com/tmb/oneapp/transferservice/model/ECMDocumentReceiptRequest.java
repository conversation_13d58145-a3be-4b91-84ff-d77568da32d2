package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ECMDocumentReceiptRequest {
    private String startDateTime;
    private String endDateTime;
    private String docTypeCode;
    private String accountNo;
}

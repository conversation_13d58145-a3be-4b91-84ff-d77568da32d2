package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.feature.ete.model.Sender;
import com.tmb.oneapp.transferservice.feature.ete.model.Terminal;
import com.tmb.oneapp.transferservice.model.response.CommonAuthenticationInformation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TPromptPayVerifyETEResponse implements V1ITransferData {
    private Sender sender;
    private Receiver receiver;
    private Terminal terminal;
    private BigDecimal amount;
    private String rtpTransactionReference;
    private String transactionReference;
    private String transactionCreatedDatetime;
    private String chargeCode;
    private BigDecimal fee;
    private Vat vat;
    private Tax tax;
    private Balance balance;
    private String senderType;
    private String receiverType;

    private String reference1;
    private String reference2;
    private String reference3;
    private String reference4;
    private Double interRegionFee;
    private String paymentType;
    private String transferType;
    private boolean isRequireConfirmPin;
    private boolean isTransactionUsed;

    private PaymentCacheData paymentCacheData;

    private String transId;
    private Date expiredAt;

    @Override
    public String getCategoryId() {
        if (paymentCacheData != null && paymentCacheData.getCategoryId() != null) {
            return paymentCacheData.getCategoryId();
        }
        return null;
    }

    @Override
    public void setExpiredAt(Date expiredAt) { this.expiredAt = expiredAt; }

    @Override
    public Boolean isTransactionUsed() {
        return this.isTransactionUsed;
    }

    @Override
    public void setTransactionUsed(Boolean value) {
        this.isTransactionUsed = value;
    }
}

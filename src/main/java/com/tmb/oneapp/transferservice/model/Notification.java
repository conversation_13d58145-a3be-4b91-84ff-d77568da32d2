package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public abstract class Notification {
    @JsonProperty("template_name")
    protected String templateName;
    protected String accountNickname;
    protected String fromAcctId;
    protected String favoriteNickname;
    protected String amount;
    protected String fee;
    protected String note;
    protected String transactionRefNo;
    protected String crmId;
    protected String xCorrelationId;
    protected String custFullNameTH;
    protected String custFullNameEN;
    protected String channelNameEN;
    protected String channelNameTH;

    protected Notification(String templateName, String refNo, String crmId, String correlationId) {
        this.templateName = templateName;
        this.transactionRefNo = refNo;
        this.crmId = crmId;
        this.xCorrelationId = correlationId;
    }

    public void setCustomerName(String firstNameEn, String lastNameEn, String fistNameTh, String lastNameTh) {
        this.custFullNameEN = StringUtils.isBlank(lastNameEn) ? firstNameEn : firstNameEn + " " + lastNameEn;
        this.custFullNameTH = StringUtils.isBlank(lastNameTh) ? fistNameTh :  fistNameTh + " " + lastNameTh;
    }

    protected String masking(String data) {
        return "xx" + data.substring(data.length() - 4);
    }

    protected void setAmount(String amount) {
        if (amount != null) {
            this.amount = insertCommas(new BigDecimal(amount));
        }
    }

    protected void setFee(String fee) {
        if (fee != null) {
            this.fee = insertCommas(new BigDecimal(fee));
        }
    }

    public void setToFavoriteNickname(String favoriteNickname) {
        this.favoriteNickname = StringUtils.isBlank(favoriteNickname) ? "-" : favoriteNickname;
    }

    public void setNote(String note) {
        this.note = StringUtils.isBlank(note) ? "-" : note;
    }

    public void setChannelName(String  channelNameEn, String channelNameTh) {
        this.channelNameEN = channelNameEn;
        this.channelNameTH = channelNameTh;
    }

    public  String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }

        DecimalFormat commasFormat = new DecimalFormat("#,##0.00");
        commasFormat.setRoundingMode(RoundingMode.DOWN);

        return commasFormat.format(number);
    }
}

package com.tmb.oneapp.transferservice.model.transfer;

import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.feature.ete.model.Sender;
import com.tmb.oneapp.transferservice.feature.ete.model.Terminal;
import com.tmb.oneapp.transferservice.feature.ete.model.TransferOtherBankETERequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import org.apache.commons.lang3.StringUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MOBILE_LENGTH;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_PROMPTPAY_BY_MOBILE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.PROXY_TYPE_TRANSFER_OTHER_BANK;

public class EteMapper {

    public final static EteMapper INSTANCE = new EteMapper();

    private EteMapper() {

    }

    private String getToProxyType(
            String toBankCode,
            String toAccountNo
    ) {
        boolean isPromptPay = StringUtils.isBlank(toBankCode);
        if (isPromptPay) {
            return (toAccountNo.length() == MOBILE_LENGTH) ? PROXY_TYPE_PROMPTPAY_BY_MOBILE : PROXY_TYPE_PROMPTPAY_BY_CITIZEN;
        }

        return PROXY_TYPE_TRANSFER_OTHER_BANK;
    }

    public TransferOtherBankETERequest toTransferValidationRequest(TransferOtherBankValidateRequest request, String taxId, String terminalId, String transRef) {
        DepositAccount depositAccount = request.getDepositAccount();
        String qr = request.getQr();
        String toAccountNo = request.getToAccountNo();
        String toBankCode = request.getToBankCode();
        String amount = request.getAmount();
        String ttbBankCode = "11";
        String terminalType = "80";
        String paymentQrPromptPay = "PROMPTPAY";
        String senderTypeKeyIn = "H";
        String senderTypeQr = "Q";
        Sender sender = new Sender();
        sender.setAccountId(depositAccount.getAccountNumber());
        sender.setAccountType(depositAccount.getAccountType());
        sender.setAccountLength(depositAccount.getAccountNumber().length());
        sender.setAccountName(depositAccount.getAccountName());
        sender.setBankCode(ttbBankCode);
        sender.setTaxId(StringUtils.isNotBlank(taxId) ? taxId : "");
        String toProxyType = getToProxyType(toBankCode, toAccountNo);
        Receiver receiver = new Receiver();
        receiver.setProxyType(toProxyType);
        receiver.setProxyValue(toAccountNo);
        receiver.setAccountId(toAccountNo);
        receiver.setAccountType(toProxyType);
        receiver.setAccountLength(toAccountNo.length());
        receiver.setBankCode(toBankCode);

        Terminal terminal = new Terminal();
        terminal.setId(terminalId);
        terminal.setType(terminalType);
        TransferOtherBankETERequest transferOtherBankETERequest = new TransferOtherBankETERequest();
        transferOtherBankETERequest.setSender(sender);
        transferOtherBankETERequest.setReceiver(receiver);
        transferOtherBankETERequest.setTerminal(terminal);
        transferOtherBankETERequest.setAmount(amount);
        transferOtherBankETERequest.setRtpTransactionReference("");
        transferOtherBankETERequest.setTransactionCreatedDatetime(ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));

        boolean isQr = StringUtils.equals(qr, paymentQrPromptPay);
        transferOtherBankETERequest.setSenderType(isQr ? senderTypeQr : senderTypeKeyIn);
        transferOtherBankETERequest.setTransactionReference(transRef);
        return transferOtherBankETERequest;
    }
}

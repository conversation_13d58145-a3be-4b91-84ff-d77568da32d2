package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.math.BigDecimal;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1OnUsValidateRequest {
    private String fromAccountNo;
    private String toAccountNo;
    private String bankCode;
    private String toFavoriteName;
    private BigDecimal amount;
    private String categoryId;
    private String note;
    private String flow;
    private String depositNo;
    private DepositAccount depositAccount;

    private String fxTransId;
    private String fromFinancialId;
    private String toFinancialId;
    private String fromCurrency;
    private String toCurrency;
    private String fxRate;

}

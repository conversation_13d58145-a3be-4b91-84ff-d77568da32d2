package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1OnUsTransferConfirmResponse {

    private String referenceNo;
    private String remainingBalance;
    private String transferCreatedDatetime;
    private Boolean isToOwnAccount;
    private String qr;
}

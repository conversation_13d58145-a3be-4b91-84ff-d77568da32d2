package com.tmb.oneapp.transferservice.model.bank;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * Save BankInfo pojo to save in mongo DB
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BankInfoDataModel {

	@Schema(description = "Unique key to save/fetch Bank config in mongo DB", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_cd")
	private String bankCd;

	@Schema(description = "Bank name Thai", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_name_th")
	private String bankNameTh;

	@Schema(description = "Bank name English", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_name_en")
	private String bankNameEn;

	@Schema(description = "Bank short name", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_shortname")
	private String bankShortname;

	@Schema(description = "allowed account lenght for this bank ex : {some banks will have 9/10/11 digits in account number (Bank of China)}", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_acct_length")
	private String bankAcctLength;

	@Schema(description = "Bank status, based on this system will decide that bank allowed to do transfer or not", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_status")
	private String bankStatus;

	@Schema(description = "orft effective date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("orft_effective_date")
	private Date orftEffectiveDate;

	@Schema(description = "orft expiry date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("orft_expire_date")
	private Date orftExpireDate;

	@Schema(description = "smart effective date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("smart_effective_date")
	private Date smartEffectiveDate;

	@Schema(description = "smart expiry date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("smart_expire_date")
	private Date smartExpireDate;

	@Schema(description = "based on this order UI system will show bank in list", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("display_order")
	private Integer displayOrder;

	@Schema(description = "promptpay effective date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("promptpay_effective_date")
	private Date promptpayEffectiveDate;

	@Schema(description = "promptpay expiry date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("promptpay_expire_date")
	private Date promptpayExpireDate;

	@Schema(description = "promtpay status", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("promptpay_status")
	private String promptpayStatus;

	@Schema(description = "bank logo reference url : firebase storage URL", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_logo")
	private String bankLogo;

	@Schema(description = "bank logo inactive reference url : firebase storage URL", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("bank_logo_inactive")
	private String bankLogoInactive;

	@Schema(description = "last updated date", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("updateDate")
	private Date updateDate;

	@Schema(description = "last updated user", requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("updateBy")
	private String updateBy;

	@Schema(description = "is Active")
	@JsonProperty("is_active")
	private Boolean isActive;

	@Schema(description = "error status when is active false")
	@JsonProperty("error_status")
	private String errorStatus;
}

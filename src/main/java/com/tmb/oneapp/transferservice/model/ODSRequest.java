package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ODSRequest {
    @JsonProperty("ODSService")
    private ODSService odsService;

    @JsonProperty("product_group")
    private String productGroup;
}

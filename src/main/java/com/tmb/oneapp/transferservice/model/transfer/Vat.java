
package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@JsonPropertyOrder({ "amount", "rate" })
public class Vat {

	@JsonProperty("amount")
	private Double amount;
	@JsonProperty("rate")
	private Double rate;

}

package com.tmb.oneapp.transferservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Document(collection = "ott_transfer_purpose")
public class InternationalTransferPurpose {
    @Field("transfer_purpose_code")
    private String transferPurposeCode;

    @Field("transfer_purpose_seq")
    private String transferPurposeSeq;

    @Field("transfer_purpose_picture_id")
    private String transferPurposePictureId;

    @Field("transfer_purpose_name")
    private String transferPurposeName;

    @Field("transfer_purpose_recommended")
    private String transferPurposeRecommended;

    @Field("transfer_purpose_status")
    private String transferPurposeStatus;

    @Field("recommended_doc_flag")
    private String recommendedDocFlag;

    @Field("special_remark_flag")
    private String specialRemarkFlag;

    @Field("recommended_doc_th")
    private String recommendedDocTh;

    @Field("recommended_doc_en")
    private String recommendedDocEn;

    @Field("create_date")
    private String createDate;

    @Field("update_date")
    private String updateDate;

    @Field("app_id")
    private String appId;
}

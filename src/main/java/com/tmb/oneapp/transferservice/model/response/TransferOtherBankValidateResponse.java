package com.tmb.oneapp.transferservice.model.response;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferOtherBankValidateResponse {
    String transId;
    String interest;
    String principal;
    String penalty;
    String netAmount;
    String tax;
    String fee;
    String amount;
    Boolean isRequireConfirmPin;
    String toAccountName;
    Boolean isRequireFr;

    Boolean isRequireCommonAuthen;
    CommonAuthenticationInformation commonAuthenticationInformation;
}

package com.tmb.oneapp.transferservice.model.transfer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.transferservice.feature.ete.model.Receiver;
import com.tmb.oneapp.transferservice.feature.ete.model.Sender;
import com.tmb.oneapp.transferservice.feature.ete.model.Terminal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TPromptPayETERequest {
    private Sender sender;
    private Receiver receiver;
    private Terminal terminal;
    private BigDecimal amount;
    private String rtpTransactionReference;
    private String transactionReference;
    private String transactionCreatedDatetime;
    private String senderType;
    private String effectiveDate;
    private Double fee;
    private String receiverType;
    private String chargeType;
    private String chargeCode;
    private String reference1;
    private String reference2;
    private String reference3;
}

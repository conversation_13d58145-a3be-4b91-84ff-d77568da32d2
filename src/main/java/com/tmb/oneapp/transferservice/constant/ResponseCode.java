package com.tmb.oneapp.transferservice.constant;

import lombok.Getter;

import java.io.Serializable;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.CIRCUIT_BREAK_ERROR_MESSAGE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DB_FAILED_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DB_FAILED_DESCRIPTION;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FAILED_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.FAILED_MESSAGE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS_CODE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.SUCCESS_MESSAGE;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_ERROR_TMBCBS_PREFIX;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TRANSFER_SERVICE_NAME;

@Getter
public enum ResponseCode implements Serializable {

    DATA_NOT_FOUND("*********", "DATA NOT FOUND", TRANSFER_SERVICE_NAME , ""),
    PIN_ERROR_LOCKED_CAUSE("pin_error_locked_cause", "Your PIN is locked because you have reached the maximum attempts.", TRANSFER_SERVICE_NAME, ""),
    PROMPTPAY_LINKED("promptpay_linked", "The Promptpay no. %s is linked to the same account you are trying to transfer from. Please enter another promptpay no.", TRANSFER_SERVICE_NAME, ""),
    DAILY_LIMIT_EXCEEDED("transfer_0001", "DAILY LIMIT EXCEEDED", TRANSFER_SERVICE_NAME, ""),

    SUCCESS(SUCCESS_CODE, SUCCESS_MESSAGE, TRANSFER_SERVICE_NAME, SUCCESS_MESSAGE),
    DB_FAILED(DB_FAILED_CODE, FAILED_MESSAGE, TRANSFER_SERVICE_NAME, FAILED_MESSAGE),
    FAILED(FAILED_CODE, FAILED_MESSAGE, TRANSFER_SERVICE_NAME, DB_FAILED_DESCRIPTION),
    MANDATORY_FIELD("0011", "Mandatory Filed Missing", TRANSFER_SERVICE_NAME, "Mandatory Filed Missing"),
    GENERAL_ERROR("0001", "general error", TRANSFER_SERVICE_NAME, "unknown error"),
    DUPLICATE_TRANSACTION("duplicate_transaction", "Duplicate transaction", TRANSFER_SERVICE_NAME, "unknown error"),
    CIRCUIT_BREAKER_ERROR("0014", CIRCUIT_BREAK_ERROR_MESSAGE, TRANSFER_SERVICE_NAME, CIRCUIT_BREAK_ERROR_MESSAGE),
    INVALID_REQUEST("0010", "Invalid Request", TRANSFER_SERVICE_NAME, "Invalid Request"),
    FR_TRANSFER_UPDATE_APP("fr_transfer_update_app", "Please update your ttb touch to the latest version for transfers or top-ups exceeding 50,000.00 per transaction, inclusive of transactions exceeding 200,000.00 per day.", TRANSFER_SERVICE_NAME, "FR Validatre app version check error"),
    PROMPT_PAY_NOT_REGISTERED("ppgw_b247048", "The Promptpay no. %s is not registered to allow transfering money. Please ask your recipient to register their mobile no./ citizen ID for receieve money with their own bank.", TRANSFER_SERVICE_NAME, null),
    INCORRECT_TO_ACCOUNT("transfer_0003", "Invalid to account number", TRANSFER_SERVICE_NAME, "Invalid to account number"),
    PB_UPDATE_APP("pb_update_app", "To continue using application and transacting safely, please update your ttb touch to the latest version.", TRANSFER_SERVICE_NAME, null),
    CIRCUIT_BREAK_ERROR("0014", CIRCUIT_BREAK_ERROR_MESSAGE, TRANSFER_SERVICE_NAME, CIRCUIT_BREAK_ERROR_MESSAGE),
    ACCOUNT_NOT_ELIGIBLE("1000018", "Account not eligible to this customer", TRANSFER_SERVICE_NAME, ""),
    INVALID_ACCOUNT_STATUS(TRANSFER_ERROR_TMBCBS_PREFIX + "8009", "Invalid account status", TRANSFER_SERVICE_NAME, "Invalid account status"),
    AMOUNT_LESS_THEN_MINIMUM(TRANSFER_ERROR_TMBCBS_PREFIX + "8019", "Amount less then minimum", TRANSFER_SERVICE_NAME, "Amount less then minimum");

    private String code;
    private String message;
    private String service;
    private String description;

    /**
     * Constructor
     *
     * @param code
     * @param message
     * @param service
     */
    ResponseCode(String code, String message, String service, String description) {
        this.code = code;
        this.message = message;
        this.service = service;
        this.description = description;
    }
}
package com.tmb.oneapp.transferservice.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonAuthenticationConstant {
    public static final String TRANSFER_FEATURE_ID = "1005";
    public static final String TRANSFER_TD_FEATURE_ID = "1014";
    public static final String TRANSFER_FCD_FEATURE_ID = "1032";
    public static final String DEFAULT_AMOUNT = "0.00";
    public static final String DEFAULT_BANK_CODE = "";
    public static final String DEFAULT_FEATURE_ID = "";
    public static final String DEFAULT_FLOW = "";
    public static final String QR_SIZE = "370";
    public static final String COMMON_AUTH_DESTINATION_OWN = "own";
    public static final String COMMON_AUTH_DESTINATION_OTHER = "other";
}

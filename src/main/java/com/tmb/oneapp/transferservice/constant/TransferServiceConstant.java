package com.tmb.oneapp.transferservice.constant;

public class TransferServiceConstant {
    public static final String HEADER_TIMESTAMP = "Timestamp";
    public static final String HEADER_CORRELATION_ID = "x-correlation-id";
    public static final String HEADER_CONTENT_TYPE = "content-type";
    public static final String HEADER_REQUEST_UUID = "request-uid";
    public static final String HEADER_APP_ID = "request-app-id";
    public static final String HEADER_SERVICE_NAME = "service-name";
    public static final String HEADER_REQUEST_DATE_TIME = "request-datetime";
    public static final String HEADER_CRM_ID = "x-crmid";
    public static final String IP_ADDRESS = "X-Forward-For";
    public static final String OS_VERSION = "os-version";
    public static final String CHANNEL = "channel";
    public static final String APP_VERSION = "app-version";
    public static final String DEVICE_ID = "device-id";
    public static final String ACCEPT_LANGUAGE = "Accept-Language";
    public static final String DEVICE_MODEL = "device-model";
    public static final String SUCCESS = "success";
    public static final String FAILURE = "failure";
    public static final String ACCOUNT_TYPE_CURRENT = "DDA";
    public static final String ACCOUNT_TYPE_SAVING = "SDA";
    public static final String ACCOUNT_TYPE_TERM_DEPOSIT = "CDA";
    public static final String BLANK = "";
    public static final Integer MOBILE_LENGTH = 10;
    public static final String TERM_DEPOSIT_ACCOUNT_LAST_DIGIT_DEFAULT = "000";
    public static final String TRANSFER_VALIDATION_GET = "fund-transfer-validation";
    public static final String MB = "mb";
    public static final String CLEARING_STATUS = "01";
    //TXN
    public static final String TXN_TYPE_TRANSFER = "001";
    public static final String TXN_TYPE_TRANSFER_TEXT = "Transfer";
    // ETE Body Constant
    public static final String FROM_ACCOUNT = "fromAccount";
    public static final String TO_ACCOUNT = "toAccount";
    public static final String AMOUNT = "amount";
    public static final String CHARGE_TYPE = "chargeType";
    public static final String ALPHABET_I = "I";
    public static final String POSTED_DATE = "postedDate";
    public static final String BANK_DATEFORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
    public static final String CONTENT_TYPE_VALUE = "application/json";
    public static final String REQUEST_APP_ID_OCP_GATEWAY_VALUE = "A0478-MB";
    public static final String ACCOUNT_NO = "accountNo";
    public static final String ACCOUNT_TYPE = "accountType";
    public static final String STATUS_SUCCESS_CODE = "0000";
    public static final String ACCOUNT_ID = "account_id";
    public static final String ACC_TYPE = "account_type";
    public static final String ACCOUNT_NO_V3 = "account_no";
    public static final String FINANCIAL_ID = "financial_id";
    public static final String FROM_ACCOUNT_V3 = "from_account";
    public static final String TO_ACCOUNT_V3 = "to_account";
    public static final String CHARGE_TYPE_V3 = "charge_type";
    public static final String POSTED_DATE_V3 = "posted_date";
    public static final String GET_ACCOUNT_SERVICE_NAME = "account-info-inquiry";
    public static final String TRANSFER_TD_NEG_INTEREST = "M";
    public static final String TRANSFER_TD_VALIDATION_GET = "term-deposit-account-withdrawal-inquiry";
    public static final String DEPOSIT_NO = "deposit_no";
    public static final String AMOUNTS = "amounts";
    public static final String BANK_TMB_VALIDATE_DATEFORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String CONTENT_TYPE_VALUE_WITH_UTF8 = "application/json;charset=UTF-8";
    public static final String TTB_BANK_CODE = "11";
    public static final String TTB_BANK_CODE_3DIGITS = "011";
    public static final String TTB_BANK_SHORT_NAME = "ttb";
    public static final String COMMON_TRANSFER_PURPOSE_MASTER_DATA = "common_transfer_purpose_masterdata";
    public static final String COMMON_TRANSFER_OTT_COUNTRY_MASTER_DATA = "common_transfer_ott_country_master_data";
    public static final String CACHE_FETCHING_ERROR_MESSAGE = "exception while fetching data from Redis {}";
    public static final String CHANNEL_MB = "mb";
    public static final String PAYMENT_QR_PROMPT_PAY = "PROMPTPAY";
    public static final String THAI_QR_FIN_FLEX_VALUES1 = "QR";
    public static final String TRANSFER_SERVICE_NAME = "service-name";
    public static final String SUCCESS_CODE = "0000";
    public static final String SUCCESS_MESSAGE = "success";
    public static final String FAILED_CODE = "0001";
    public static final String FAILED_MESSAGE = "failed";
    public static final String DB_FAILED_CODE = "0100";
    public static final String DB_FAILED_DESCRIPTION = "Cannot do this transaction";
    public static final String LOCALE_TH = "th";
    public static final String DEFAULT_APP_VERSION = "5.0.0";
    public static final String CRM_ID = "crmId";
    public static final String TRANSFER_CONFIRMATION_GET = "fund-transfer-confirmation";
    public static final String PROMPTPAY_CONFIRM_GET = "promptpay-credittransfer-add";
    public static final String TMBO = "TMBO";
    public static final String B011B = "B011B";
    public static final String COMMON_MODULE_CONSTANT = "common_module";
    public static final String SEARCH_PARAM_CONSTANT = "search";
    // Transaction step
    public static final String TRANSFER_ACTIVITY_VERIFY_STEP = "Enter details";
    public static final String BILL_PAYMENT_ACTIVITY_VERIFY_STEP = "Enter details";
    public static final String BILL_PAYMENT_ACTIVITY_CONFIRM_STEP = "Confirm";
    // Proxy Type
    public static final String PROXY_TYPE_TRANSFER_OTHER_BANK = "00";
    public static final String PROXY_TYPE_PROMPTPAY_BY_CITIZEN = "01";
    public static final String PROXY_TYPE_PROMPTPAY_BY_MOBILE = "02";
    // User Status
    public static final String EB_CUSTOMER_STATUS = "02";
    public static final String MB_CUSTOMER_STATUS = "02";
    public static final String MB_CUSTOMER_STATUS_PIN_LOCK = "05";
    // Process Type
    public static final String TRANSFER_PROCESS_TYPE = "TRANSFER";
    public static final String TOP_UP_PROCESS_TYPE = "TOP_UP";

    public static final String TRANSFER_REFERENCE_NUMBER_PREFIX = "financial_reference_id_sequence_";
    public static final String PROMPTPAY_REF_SEQ = "promptpay_ref_sequence";
    public static final int EIGHT_INT = 8;
    public static final int SIX_INT = 6;
    public static final int MAX_TO_ACCT_ID = 10;
    public static final String D00 = "000";
    public static final String SMART_FLAG_TRANSFER_OTHER_BANK = "other";
    public static final String SMART_FLAG_TRANSFER_TTB_OR_PROMPT_PAY = "promptpay";
    public static final String REFERENCE_ACTIVITY_ID_VALUE = "000";
    public static final String DEBIT_TRANSACTION = "2";
    public static final String CONSTANTTWO = "2";
    public static final String FOREIGNER_CUSTOMER_TYPE = "920";
    public static final String TRANSFER_MODULE = "transfer";
    public static final String QR_TRANSFER_MODULE = "QR Transfer";
    public static final String TRANSFER_PIN_REFERENCE_PREFIX = "VERIFY_PIN_REF_ID_";
    public static final String HOME_FAVORITE = "Home-Favorite";
    public static final String HOME_TRANSFER_LANDING = "Home-Transfer-Landing";


    public static final String TRANSFER_VALIDATE_OTHER_BANK = "TRANSFER_VALIDATE_OTHER_BANK";
    public static final String TRANSFER_CONFIRM_OTHER_BANK = "TRANSFER_CONFIRM_OTHER_BANK";
    public static final String PROMPTPAY_VALIDATE = "/v1.0/credittransfer/promptpay/validation";
    public static final String PROMPTPAY_CONFIRM = "/v1.0/credittransfer/promptpay/confirmation";

    public static final String CIRCUIT_BREAKER_BBL = "BBL";
    public static final String CIRCUIT_BREAKER_KBANK = "KBANK";
    public static final String CIRCUIT_BREAKER_BAY = "BAY";
    public static final String CIRCUIT_BREAKER_KTB = "KTB";
    public static final String CIRCUIT_BREAKER_SCB = "SCB";
    public static final String CIRCUIT_BREAKER_GSB = "GSB";
    public static final String CIRCUIT_BREAKER_OTHER = "Other";
    public static final String FAILED_CIRCUIT_BREAKER = "failed by circuit breaker";

    public static final String TRANSFER_ERROR_PROMPTPAY_PREFIX = "ppgw_";
    public static final String TRANSFER_ERROR_TMBCBS_PREFIX = "cbs_";
    public static final String FAILED_ETE = "failed by ETE";

    public static final Integer FR_DEFAULT_FEATURE_ID = 1005;
    public static final String REFRESH_FLAG = "Refresh-flag";
    public static final String ALL_ACCOUNT_FLAG = "list-all-accounts-flag";
    public static final String GROUP_TYPE_DEPOSIT = "deposit";
    public static final String GROUP_TYPE_CREDIT_CARD = "creditcard";

    public static final String CHANNEL_PB = "pb";
    public static final String CIRCUIT_BREAK_ERROR_MESSAGE = "Circuit break error";

    public static final String FLOW_NAME_TRANSFER = "transfer";
    public static final String FLOW_NAME_TRANSFER_TD = "transfer from td";


}

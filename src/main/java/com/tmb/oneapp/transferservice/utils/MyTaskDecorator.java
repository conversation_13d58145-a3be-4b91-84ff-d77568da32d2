package com.tmb.oneapp.transferservice.utils;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

public class MyTaskDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        return new MDCDecoratedRunnable(runnable);
    }

    public static class MDCDecoratedRunnable implements Runnable {

        private final Runnable runnable;

        private final Map<String, String> contextMap;

        public MDCDecoratedRunnable(Runnable runnable) {
            super();
            this.runnable = runnable;
            this.contextMap = MDC.getCopyOfContextMap();
        }

        @Override
        public void run() {
            try {
                MDC.setContextMap(contextMap);
                runnable.run();
            } finally {
                MDC.clear();
            }
        }
    }
}
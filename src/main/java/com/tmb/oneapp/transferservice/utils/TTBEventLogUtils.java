package com.tmb.oneapp.transferservice.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventLog;
import com.tmb.common.logger.TTBEventMonitoringType;
import com.tmb.common.logger.TTBEventStatus;
import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

@UtilityClass
public class TTBEventLogUtils {
    private static final TMBLogger<TTBEventLogUtils> logger = new TMBLogger<>(TTBEventLogUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @SafeVarargs
    public <T> void sendBusinessEventLog(TTBEventStatus eventStatus, String uri, int httpCode, int responseTime, T... parameters) {
        TTBEventLog event = new TTBEventLog(TTBEventMonitoringType.BUSINESS, uri, eventStatus, httpCode, responseTime);

        if (parameters != null && parameters.length > 0) {
            HashMap<String, Object> combinedParams = new HashMap<>();

            for (T param : parameters) {
                if (param != null) {
                    combinedParams.putAll(toHashMap(param));
                }
            }

            event.setParameters(combinedParams);
        }

        logger.debug("logging business event : {}", event);
        logger.event(event);
    }

    public <T> HashMap<String, Object> toHashMap(T parameters) {
        if (parameters == null) {
            return new HashMap<>();
        }

        if (parameters instanceof HashMap) {
            return new HashMap<>((HashMap<String, Object>) parameters);
        }
        try {
            Map<String, Object> map = objectMapper.convertValue(parameters, Map.class);
            return new HashMap<>(map);
        } catch (Exception e) {
            logger.error("Error converting object using ObjectMapper: {}", parameters, e);
            return new HashMap<>();
        }
    }
}
package com.tmb.oneapp.transferservice.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

public class TransferUtils {
    private static final TMBLogger<TransferUtils> logger = new TMBLogger<>(TransferUtils.class);

    public TransferUtils() {
        // not called
    }
    public static void handleException(Exception e) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        if (e instanceof TMBCommonException) {
            isAlwaysReturnHttpOk((TMBCommonException) e);
            throw (TMBCommonException) e;
        } else if (e instanceof TMBCustomCommonExceptionWithResponse) {
            throw (TMBCustomCommonExceptionWithResponse) e;
        } else if (e.getCause() instanceof TMBCommonException) {
            isAlwaysReturnHttpOk((TMBCommonException) e.getCause());
            throw (TMBCommonException) e.getCause();
        } else if (e.getCause() instanceof TMBCustomCommonExceptionWithResponse) {
            throw (TMBCustomCommonExceptionWithResponse) e.getCause();
        } else {
            throw failException(ResponseCode.FAILED);
        }
    }

    public static TMBCommonException handleExceptions(Exception e) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        if (e instanceof TMBCommonException ex) {
            isAlwaysReturnHttpOk(ex);
            throw ex;
        } else if (e instanceof TMBCustomCommonExceptionWithResponse ex) {
            throw ex;
        } else if (e.getCause() instanceof TMBCommonException ex) {
            isAlwaysReturnHttpOk(ex);
            throw ex;
        } else if (e.getCause() instanceof TMBCustomCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            return failException(ResponseCode.FAILED);
        }
    }

    private static void isAlwaysReturnHttpOk(TMBCommonException e) {
        e.setStatus(HttpStatus.OK);
    }

    public static TMBCommonException failException(ResponseCode responseCode) {
        return new TMBCommonException(
                responseCode.getCode(),
                responseCode.getMessage(),
                responseCode.getService(),
                HttpStatus.OK,
                null);
    }

    public static <T> Object getResponseBodyInFeignException(FeignException feignException, Class<T> className) throws TMBCommonException {
        try {
            Optional<ByteBuffer> byteBufferOptional = feignException.responseBody();

            ByteBuffer byteBuffer;
            if (byteBufferOptional.isPresent()) {
                byteBuffer = byteBufferOptional.get();
            } else {
                throw failException(ResponseCode.FAILED);
            }

            String s = StandardCharsets.UTF_8.decode(byteBuffer).toString();
            s = s.replace("\n", "");
            return TMBUtils.convertStringToJavaObj(s, className);
        } catch (Exception e) {
            logger.info("getResponseBodyInFeignException {}", e);
            throw failException(ResponseCode.FAILED);
        }
    }

    public static TMBCommonException circuitBreakException() {
        return new TMBCommonException(
                ResponseCode.CIRCUIT_BREAK_ERROR.getCode(),
                ResponseCode.CIRCUIT_BREAK_ERROR.getMessage(),
                ResponseCode.CIRCUIT_BREAK_ERROR.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);
    }


    public static String makeBankCode3Digits(String str) {
        return StringUtils.leftPad(str, 3, '0');
    }

}
package com.tmb.oneapp.transferservice.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class NumberUtils {
    public static String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }

        DecimalFormat commasFormat = new DecimalFormat("#,##0.00");
        commasFormat.setRoundingMode(RoundingMode.DOWN);

        return commasFormat.format(number);
    }
}

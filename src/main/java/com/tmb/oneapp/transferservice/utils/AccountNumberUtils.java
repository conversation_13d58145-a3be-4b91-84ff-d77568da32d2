package com.tmb.oneapp.transferservice.utils;

import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import org.apache.commons.lang3.StringUtils;

public class AccountNumberUtils {
    private AccountNumberUtils() {

    }

    public static String getToAccountNoOfNormalOrTD(String toAccountNo, String toAccType) {
        if (toAccType.equals(TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT)) {
            return toAccountNo + TransferServiceConstant.TERM_DEPOSIT_ACCOUNT_LAST_DIGIT_DEFAULT;
        }
        return toAccountNo;
    }

    public static String getFromAccountNoOfNormalOrTD(String fromAccountNo, String fromAccType, String depositNo) {
        if (fromAccType.equals(TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT)) {
            return fromAccountNo + depositNo;
        }
        return fromAccountNo;
    }

    private static String getFCDAccountType(String accountNo) {
        return switch (accountNo.charAt(4)) {
            case '1' -> TransferServiceConstant.ACCOUNT_TYPE_CURRENT;
            case '2', '7', '9' -> TransferServiceConstant.ACCOUNT_TYPE_SAVING;
            case '3' -> TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;
            default -> TransferServiceConstant.BLANK;
        };
    }

    public static String getAccountTypeByAccountNo(String accountNo) {
        return switch (accountNo.charAt(3)) {
            case '1' -> TransferServiceConstant.ACCOUNT_TYPE_CURRENT;
            case '2', '7', '9' -> TransferServiceConstant.ACCOUNT_TYPE_SAVING;
            case '3' -> TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;
            case '8' -> getFCDAccountType(accountNo);
            default -> TransferServiceConstant.BLANK;
        };
    }

    public static boolean isFcdAccount(String accountNo) {
        return "8".equals(StringUtils.substring(StringUtils.right(accountNo, 10), 3, 4));
    }

    public static String getAccountControl4ByAccountType(String accountType) {
        return switch (accountType) {
            case TransferServiceConstant.ACCOUNT_TYPE_CURRENT -> "0000";
            case TransferServiceConstant.ACCOUNT_TYPE_SAVING -> "0200";
            case TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT -> "0300";
            default -> "";
        };
    }
}

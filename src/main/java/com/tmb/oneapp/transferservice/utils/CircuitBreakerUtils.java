package com.tmb.oneapp.transferservice.utils;

import feign.FeignException;

public class CircuitBreakerUtils {

    public static Boolean isGatewayError(FeignException fe) {
        return fe instanceof FeignException.BadGateway || fe instanceof FeignException.GatewayTimeout || fe instanceof FeignException.ServiceUnavailable;
    }

    public static Boolean is5xxError(FeignException fe) {
        return (fe instanceof FeignException.InternalServerError) || isGatewayError(fe);
    }
}

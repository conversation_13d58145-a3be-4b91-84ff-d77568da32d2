package com.tmb.oneapp.transferservice.utils;

import com.tmb.common.logger.TMBLogger;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.ACCOUNT_TYPE_TERM_DEPOSIT;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.TERM_DEPOSIT_ACCOUNT_LAST_DIGIT_DEFAULT;

public class TransferValidations {
    private static final TMBLogger<TransferValidations> logger = new TMBLogger<>(TransferValidations.class);

    public TransferValidations() {
        // not called
    }

    public static String getToAccountNoOfNormalOrTD(String toAccountNo, String toAccType) {
        if (toAccType.equals(ACCOUNT_TYPE_TERM_DEPOSIT)) {
            return toAccountNo + TERM_DEPOSIT_ACCOUNT_LAST_DIGIT_DEFAULT;
        }
        return toAccountNo;
    }

    public static String getFromAccountNoOfNormalOrTD(String fromAccountNo, String fromAccType, String depositNo) {
        if (fromAccType.equals(ACCOUNT_TYPE_TERM_DEPOSIT)) {
            return fromAccountNo + depositNo;
        }
        return fromAccountNo;
    }

}
package com.tmb.oneapp.transferservice.utils;

import com.tmb.common.logger.TMBLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Base64;

@Component
public class EcmServiceUtils {
    private static final TMBLogger<EcmServiceUtils> logger = new TMBLogger<>(EcmServiceUtils.class);

    private EcmServiceUtils() {

    }

    //CMIS
    public static final String TF_DOCTYPE_SWIFT = "SWIFT";
    public static final String TF_DOCTYPE_EXIMDOC = "EXIMDOC";
    public static final String TF_DOCTYPE_SWIFT_EXIMDOC = "EXIMDOC','SWIFT";

    public static final String DOWNLOAD_SWIFT_EXIMDOC_STMT = "SELECT id,DocumentTitle FROM TradeFinanceTransactionDocuments WHERE DocumentTitle IN ('{tfDocType}') and TF_TOUCHREFNUMBER = '{txnRefId}'";

    public static final String TFDOCTYPE = "{tfDocType}";
    public static final String TXNREFID = "{txnRefId}";

    public static String getSwiftOrEximDocStatement(String txnRefId, String isSwiftOrEximDoc) {

        String statement = DOWNLOAD_SWIFT_EXIMDOC_STMT;
        txnRefId = txnRefId == null ? "" : txnRefId;

        if (TF_DOCTYPE_SWIFT.equalsIgnoreCase(isSwiftOrEximDoc)) {
            statement = statement.replace(TFDOCTYPE, TF_DOCTYPE_SWIFT).replace(TXNREFID, txnRefId);
        } else if (TF_DOCTYPE_EXIMDOC.equalsIgnoreCase(isSwiftOrEximDoc)) {
            statement = statement.replace(TFDOCTYPE, TF_DOCTYPE_EXIMDOC).replace(TXNREFID, txnRefId);
        } else {
            statement = statement.replace(TFDOCTYPE, TF_DOCTYPE_SWIFT_EXIMDOC).replace(TXNREFID, txnRefId);
        }
        return statement;
    }

    public static String getAuthorization(String cmisUserName, String cmisPassword) {
        String authorization = "";
        if (StringUtils.isNotBlank(cmisUserName) && StringUtils.isNotBlank(cmisPassword)) {
            String temp = cmisUserName + ":" + cmisPassword;
            byte[] encodedBytes = Base64.getEncoder().encode(temp.getBytes());
            String userPass = new String(encodedBytes);
            authorization = "Basic " + userPass;
        }
        return authorization;
    }

    public static void httpsAuthentication() {
        SSLContext sc;

        TrustManager[] trustAllCerts = new TrustManager[]{ new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                try {
                    x509Certificates.clone();
                } catch (Exception e) {
                    throw new CertificateException();
                }
            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                try {
                    x509Certificates.clone();
                } catch (Exception e) {
                    throw new CertificateException();
                }
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        }};
        try {
            sc = SSLContext.getInstance("TLSv1.2");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            HostnameVerifier allHostsValid = EcmServiceUtils::verify;

            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (Exception ex) {
            logger.error(" ERROR Connect Verify SSL ", ex.getMessage());
        }
    }


    public static String getAttachmentFileName(String docType, String finTxnDateTime) {
        String fileName;

        logger.info("formatted dateTime : " + finTxnDateTime);
        if (TF_DOCTYPE_EXIMDOC.equals(docType)) {
            fileName = "DEBIT_ADVICE_" + finTxnDateTime + ".pdf";
        } else {
            fileName = "SWIFT_DETAIL_" + finTxnDateTime + ".pdf";
        }
        logger.info("fileName : " + fileName);
        return fileName;
    }

    private static boolean verify(String hostname, SSLSession session) {
        return true;
    }
}

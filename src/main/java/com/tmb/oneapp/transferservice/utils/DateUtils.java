package com.tmb.oneapp.transferservice.utils;

import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtils {

    public static String getCurrentDateTTBValidateFormat() {
        SimpleDateFormat date = new SimpleDateFormat(TransferServiceConstant.BANK_TMB_VALIDATE_DATEFORMAT);
        return date.format(new Date(System.currentTimeMillis()));
    }

    public static String convertHumanDateToEpoch(String dateString) throws ParseException {
        long epoch = new SimpleDateFormat("yyyy-MM-dd").parse(dateString).getTime() / 1000;
        return String.valueOf(epoch);
    }

    public static String getCurrentDateTime() {
        return Long.toString(System.currentTimeMillis());
    }
}

package com.tmb.oneapp.transferservice.feign;

import com.tmb.oneapp.transferservice.model.InterestRateODSResponse;
import com.tmb.oneapp.transferservice.model.ODSRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "ods-interest-rate-service", url = "${ods-interest-rate-service-url}")
public interface ODSFeignClient {
    @GetMapping(value = "/v3.0/internal/interest-rate/get-rate-by-product-group")
    ResponseEntity<InterestRateODSResponse> getInterestRate(
            @RequestHeader HttpHeaders headers,
            @RequestBody ODSRequest body);
}

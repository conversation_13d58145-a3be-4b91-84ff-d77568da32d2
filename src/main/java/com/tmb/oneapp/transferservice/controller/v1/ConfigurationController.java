package com.tmb.oneapp.transferservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.transfer.TransferModuleModel;
import com.tmb.oneapp.transferservice.service.ConfigurationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

@RestController
@Tag(name = "Retrieve configuration data Controller")
public class ConfigurationController {

    private final ConfigurationService configurationService;


    public ConfigurationController(ConfigurationService configurationService) {
        this.configurationService = configurationService;
    }

    @GetMapping(value = "/configuration")
    public ResponseEntity<TmbOneServiceResponse<TransferModuleModel>> getAccountConfiguration(@RequestHeader HttpHeaders header) throws TMBCommonException {
        TransferModuleModel responseData = configurationService.getTransferConfiguration();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(TransferServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        TmbOneServiceResponse<TransferModuleModel> response = new TmbOneServiceResponse<>();
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), null));
        response.setData(responseData);
        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }
}

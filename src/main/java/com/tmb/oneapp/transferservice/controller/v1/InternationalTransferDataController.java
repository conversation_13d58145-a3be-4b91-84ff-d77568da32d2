package com.tmb.oneapp.transferservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.model.internationaltransfer.OTTCountry;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.model.ECMDocument;
import com.tmb.oneapp.transferservice.model.ECMDocumentRequest;
import com.tmb.oneapp.transferservice.model.InterestRateResponse;
import com.tmb.oneapp.transferservice.model.InternationalTransferPurpose;
import com.tmb.oneapp.transferservice.service.CallECMAppService;
import com.tmb.oneapp.transferservice.service.CommonDataServiceInterface;
import com.tmb.oneapp.transferservice.service.InternationalTransferDataService;
import com.tmb.oneapp.transferservice.service.InternationalTransferDataServiceInterface;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;

@RestController
@Tag(name = "API fetching master data of international transfer")
public class InternationalTransferDataController {
    private static final TMBLogger<InternationalTransferDataController> logger = new TMBLogger<>(InternationalTransferDataController.class);
    private final InternationalTransferDataService internationalTransferDataService;
    private final CallECMAppService callECMAppService;

    public InternationalTransferDataController(
            InternationalTransferDataService configDataService,
            CallECMAppService callECMAppService
    ) {
        this.internationalTransferDataService = configDataService;
        this.callECMAppService = callECMAppService;
    }

    /**
     * method : To call getting master data of International Transfer Purpose
     */
    @LogAround
    @GetMapping(value = "/list/ott-purposes")
    @Schema(description = "Get purpose master data")
    public ResponseEntity<TmbOneServiceResponse<List<InternationalTransferPurpose>>> getPurposeMasterData(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") String ignoredCorrelationId)
            throws TMBCommonException {
        return callService(internationalTransferDataService::fetchPurposeMasterData, null, null);
    }

    /**
     * method : To call getting master data of OTT Country
     */
    @LogAround
    @GetMapping(value = "/ott/countries")
    @Schema(description = "Get list ott-country master data")
    public ResponseEntity<TmbOneServiceResponse<List<OTTCountry>>> getOttCountryMaster(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") String ignoredCorrelationId)
            throws TMBCommonException {
        return callService(internationalTransferDataService::fetchOttCountryMasterData, null, null);
    }

    /**
     * method : calling service by dynamic response type
     *
     * @param function interface for no argument
     */
    private <T, C> ResponseEntity<TmbOneServiceResponse<T>> callService(InternationalTransferDataServiceInterface<T> function, CommonDataServiceInterface<T, C> bodyFunction, C bodyRequest)
            throws TMBCommonException {
        TmbOneServiceResponse<T> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(TransferServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            T data = null;
            if (function != null) {
                data = function.apply();
            } else {
                data = bodyFunction.apply(bodyRequest);
            }
            response.setStatus(new TmbStatus(
                    ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(),
                    ResponseCode.SUCCESS.getDescription()));
            response.setData(data);
        } catch (TMBCommonException e) {
            logger.error("API : " + "/v1/transfer/list/ott-purposes" + " is error in CommonConfigController : {} ", e);
            throw e;
        }
        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Schema(description = "Get interest rate from ODS by product code")
    @LogAround
    @PostMapping(value = "/ott/ods/interest-rates")
    public ResponseEntity<TmbOneServiceResponse<InterestRateResponse>> getInterestRateODS
            (@Valid @RequestBody String productCode) {
        logger.info("[IN] REQUEST DATA for get interest rate {}", productCode);
        TmbOneServiceResponse<InterestRateResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            InterestRateResponse data = internationalTransferDataService.getInterestRate(productCode);
            response.setData(data);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.info("Get data from ODS failed {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }

    @Schema(description = "Get Attach file swift debit from core bank")
    @LogAround
    @PostMapping(value = "/ott/download-doc")
    public ResponseEntity<TmbOneServiceResponse<List<ECMDocument>>> downloadDocument(
            @Valid @RequestBody ECMDocumentRequest inputReq
    ) {
        logger.info("[IN] REQUEST DATA for Get Attach File form base64 {}", inputReq);
        TmbOneServiceResponse<List<ECMDocument>> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            List<ECMDocument> data = callECMAppService.getAttachFile(inputReq);
            response.setData(data);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription()));

            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.info("download document fail {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDescription()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(response);
        }
    }
}

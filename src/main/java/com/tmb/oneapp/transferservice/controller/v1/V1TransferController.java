package com.tmb.oneapp.transferservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.model.request.TransferOnUsConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankConfirmResponse;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferConfirmResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.service.V1OnUsTransferService;
import com.tmb.oneapp.transferservice.service.V1TransferOtherBankService;
import com.tmb.oneapp.transferservice.utils.TransferUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.EB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS_PIN_LOCK;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.utils.TransferUtils.handleException;

@RestController
public class V1TransferController {
    private static final TMBLogger<V1TransferController> logger = new TMBLogger<>(V1TransferController.class);

    @Autowired
    private V1TransferOtherBankService v1TransferService;

    @Autowired
    private CustomerService customerService;

    @Autowired

    private V1OnUsTransferService onUsTransferService;

    public static TmbStatus getResponseSuccess() {
        return new TmbStatus(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getService(),
                null);
    }

    @Schema(description = "Transfer Other Bank : Validate ")
    @LogAround
    @PostMapping(value = "/off-us/validate")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> transOtherBankValidate(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
            @RequestBody @Valid TransferOtherBankValidateRequest otherBankVerifyRequest,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOtherBankValidateResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TransferOtherBankValidateResponse data = v1TransferService.transferOtherBankValidate(otherBankVerifyRequest, crmId, correlationId, header);
            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("Failed to process transOtherBankValidate error : {}", e);
            handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Schema(description = "Transfer On Us : Validate ")
    @LogAround
    @PostMapping(value = "/on-us/validate")
    public ResponseEntity<TmbOneServiceResponse<V1OnUsTransferValidateResponse>> transferOnUsValidate(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
            @RequestBody @Valid V1OnUsValidateRequest request,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<V1OnUsTransferValidateResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            V1OnUsTransferValidateResponse data = onUsTransferService.validate(request, crmId, correlationId, headers);

            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("Failed to process transferOnUsValidate error : {}", e);
            handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Schema(description = "Transfer On Us : Confirmation")
    @LogAround
    @PostMapping(value = "/on-us/confirm")
    public ResponseEntity<TmbOneServiceResponse<V1OnUsTransferConfirmResponse>> transferOnUsConfirmation(
            @RequestBody TransferOnUsConfirmRequest request,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID")
            @Valid
            @RequestHeaderNonNull String crmId,
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID")
            @RequestHeaderNonNull String correlationId,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException,
            TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<V1OnUsTransferConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            V1OnUsTransferConfirmResponse data = onUsTransferService.confirm(request, crmId, correlationId, headers);
            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(data);
        } catch (Exception e) {
            logger.error("Failed to process transOnUsConfirm error : {}", e);
            handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Schema(description = "Transfer Other Bank : Confirm ")
    @LogAround
    @PostMapping(value = "/off-us/confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> transOtherBankAndPromptPayConfirm(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
            @RequestBody @Valid TransferOtherBankConfirmRequest reqBody,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOtherBankConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            TransferOtherBankConfirmResponse data = v1TransferService.transferOtherBankConfirm(reqBody, crmId, correlationId, header);

            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("Failed to process transOtherBankAndPromptPayConfirm error : {}", e);
            handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    @Schema(description = "Transfer PromptPay : Confirm ")
    @LogAround
    @PostMapping(value = "/promptpay/confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> transferPromptPayConfirm(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID") @RequestHeader("X-CRMID") String crmIdReq,
            @Parameter(name = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader(DEVICE_ID) String deviceId,
            @RequestBody @Valid TransferOtherBankConfirmRequest reqBody,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOtherBankConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            String crmId = (StringUtils.isEmpty(crmIdReq) ? getCrmIdFromDeviceId(deviceId) : crmIdReq);
            header.set(HEADER_CRM_ID, crmId);
            TransferOtherBankConfirmResponse data = v1TransferService.transferOtherBankConfirm(reqBody, crmId, correlationId, header);
            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("Failed to process transPromptPayConfirm error : {}", e);
            handleException(e);
        }
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

    private String getCrmIdFromDeviceId(String deviceId) throws TMBCommonException {
        CustomerProfileStatus customerProfileStatus = customerService
                .getCustomerProfileByDeviceId(deviceId);
        checkCustomerActive(customerProfileStatus);
        return customerProfileStatus.getCrmId();
    }

    private void checkCustomerActive(CustomerProfileStatus customerProfileStatus) throws TMBCommonException {
        if (customerProfileStatus.getMbUserStatusId().equals(MB_CUSTOMER_STATUS_PIN_LOCK)) {
            throw TransferUtils.failException(ResponseCode.PIN_ERROR_LOCKED_CAUSE);
        }

        if (!customerProfileStatus.getEbCustomerStatusId().equals(EB_CUSTOMER_STATUS)
                || !customerProfileStatus.getMbUserStatusId().equals(MB_CUSTOMER_STATUS)) {
            logger.error("Customer is not active - invalid data");
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }

    @Schema(description = "Transfer Prompt pay : Validate ")
    @LogAround
    @PostMapping(value = "/promptpay/validate")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> transferPromptPayValidate(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID") @RequestHeader("X-CRMID") String crmIdReq,
            @Parameter(name = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @Valid @RequestHeader(DEVICE_ID) String deviceId,
            @RequestBody @Valid TransferOtherBankValidateRequest otherBankVerifyRequest,
            @RequestHeader HttpHeaders header) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOtherBankValidateResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            String crmId = (StringUtils.isEmpty(crmIdReq) ? getCrmIdFromDeviceId(deviceId) : crmIdReq);
            header.set(HEADER_CRM_ID, crmId);
            TransferOtherBankValidateResponse data = v1TransferService.transferPromptPayValidate(otherBankVerifyRequest, crmId, correlationId, header);
            tmbOneServiceResponse.setStatus(getResponseSuccess());
            tmbOneServiceResponse.setData(data);

        } catch (Exception e) {
            logger.error("Failed to process transferPromptPayValidate error : {}", e);
            handleException(e);
        }

        return ResponseEntity.ok(tmbOneServiceResponse);
    }


}

package com.tmb.oneapp.transferservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.transferservice.constant.ResponseCode;
import com.tmb.oneapp.transferservice.constant.TransferServiceConstant;
import com.tmb.oneapp.transferservice.feature.customer.service.CustomerService;
import com.tmb.oneapp.transferservice.model.request.TransferOnUsConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankConfirmRequest;
import com.tmb.oneapp.transferservice.model.request.TransferOtherBankValidateRequest;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankConfirmResponse;
import com.tmb.oneapp.transferservice.model.response.TransferOtherBankValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferConfirmResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsTransferValidateResponse;
import com.tmb.oneapp.transferservice.model.transfer.V1OnUsValidateRequest;
import com.tmb.oneapp.transferservice.service.V1OnUsTransferService;
import com.tmb.oneapp.transferservice.service.V1TransferOtherBankService;
import com.tmb.oneapp.transferservice.utils.TransferUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.DEVICE_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.EB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS;
import static com.tmb.oneapp.transferservice.constant.TransferServiceConstant.MB_CUSTOMER_STATUS_PIN_LOCK;
import static com.tmb.oneapp.transferservice.utils.TransferUtils.handleExceptions;

@RestController
public class V2TransferController {
    private static final TMBLogger<V2TransferController> logger = new TMBLogger<>(V2TransferController.class);

    private final V1OnUsTransferService v1OnUsTransferService;
    private final V1TransferOtherBankService v1TransferOtherBankService;
    private final CustomerService customerService;

    public V2TransferController(V1OnUsTransferService v1OnUsTransferService, V1TransferOtherBankService v1TransferOtherBankService, CustomerService customerService) {
        this.v1OnUsTransferService = v1OnUsTransferService;
        this.v1TransferOtherBankService = v1TransferOtherBankService;
        this.customerService = customerService;
    }

    private TmbStatus getResponseSuccess() {
        return new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDescription());
    }

    private HttpHeaders createResponseHeaders() {
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(TransferServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        return responseHeaders;
    }

    @LogAround
    @Operation(summary = "Validate on-us transfer", description = "Validate transfer within TMB bank")
    @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = V1OnUsTransferValidateResponse.class)))
    @PostMapping("/on-us/validate")
    public ResponseEntity<TmbOneServiceResponse<V1OnUsTransferValidateResponse>> validateOnUs(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader(HEADER_CRM_ID) @RequestHeaderNonNull String crmId,
            @RequestBody @Valid V1OnUsValidateRequest request,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        logger.debug("Starting on-us validate with correlationId: {}", correlationId);
        TmbOneServiceResponse<V1OnUsTransferValidateResponse> response = new TmbOneServiceResponse<>();

        try {
            V1OnUsTransferValidateResponse data = v1OnUsTransferService.validate(request, crmId, correlationId, headers);
            response.setStatus(getResponseSuccess());
            response.setData(data);
            return ResponseEntity.ok().headers(createResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Failed to process on-us validate error: {}", e.getMessage(), e);
            throw handleExceptions(e);
        }
    }

    @LogAround
    @Operation(summary = "Confirm on-us transfer", description = "Confirm transfer within TMB bank")
    @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = V1OnUsTransferConfirmResponse.class)))
    @PostMapping("/on-us/confirm")
    public ResponseEntity<TmbOneServiceResponse<V1OnUsTransferConfirmResponse>> confirmOnUs(
        @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
        @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader(HEADER_CRM_ID) @RequestHeaderNonNull String crmId,
        @RequestBody @Valid TransferOnUsConfirmRequest request,
        @RequestHeader HttpHeaders headers)
        throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        logger.debug("Starting on-us confirm with correlationId: {}", correlationId);
        TmbOneServiceResponse<V1OnUsTransferConfirmResponse> response = new TmbOneServiceResponse<>();

        try {
            V1OnUsTransferConfirmResponse data = v1OnUsTransferService.confirm(request, crmId, correlationId, headers);
            response.setStatus(getResponseSuccess());
            response.setData(data);
            return ResponseEntity.ok().headers(createResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Failed to process on-us confirm error: {}", e.getMessage(), e);
            throw handleExceptions(e);
        }
    }

    @LogAround
    @Operation(summary = "Validate off-us transfer", description = "Validate transfer to other banks")
    @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = TransferOtherBankValidateResponse.class)))
    @PostMapping("/off-us/validate")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> validateOffUs(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader(HEADER_CRM_ID) @RequestHeaderNonNull String crmId,
            @RequestBody @Valid TransferOtherBankValidateRequest request,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        logger.debug("Starting off-us validate with correlationId: {}", correlationId);
        TmbOneServiceResponse<TransferOtherBankValidateResponse> response = new TmbOneServiceResponse<>();

        try {
            TransferOtherBankValidateResponse data = v1TransferOtherBankService.transferOtherBankValidate(request, crmId, correlationId, headers);
            response.setStatus(getResponseSuccess());
            response.setData(data);
            return ResponseEntity.ok().headers(createResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Failed to process off-us validate error: {}", e.getMessage(), e);
            throw handleExceptions(e);
        }
    }


    @Operation(summary = "Confirm off-us transfer", description = "Confirm transfer to other banks")
    @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = TransferOtherBankConfirmResponse.class)))
    @LogAround
    @PostMapping("/off-us/confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> confirmOffUs(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID", example = "001100000000000000000000086006", required = true) @RequestHeader(HEADER_CRM_ID) @RequestHeaderNonNull String crmId,
            @RequestBody @Valid TransferOtherBankConfirmRequest request,
            @RequestHeader HttpHeaders headers)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        logger.debug("Starting off-us confirm with correlationId: {}", correlationId);
        TmbOneServiceResponse<TransferOtherBankConfirmResponse> response = new TmbOneServiceResponse<>();

        try {
            TransferOtherBankConfirmResponse data = v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, headers);
            response.setStatus(getResponseSuccess());
            response.setData(data);
            return ResponseEntity.ok().headers(createResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Failed to process off-us confirm error: {}", e.getMessage(), e);
            throw handleExceptions(e);
        }
    }

    @LogAround
    @Operation(summary = "Validate PromptPay transfer", description = "Validate PromptPay transfer")
    @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = TransferOtherBankValidateResponse.class)))
    @PostMapping("/promptpay/validate")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankValidateResponse>> validatePromptPay(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID") @RequestHeader(HEADER_CRM_ID) String crmId,
            @Parameter(name = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true)
            @RequestHeader(DEVICE_ID) @RequestHeaderNonNull String deviceId,
            @RequestBody @Valid TransferOtherBankValidateRequest request,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        logger.debug("Starting promptpay validate with correlationId: {}", correlationId);
        TmbOneServiceResponse<TransferOtherBankValidateResponse> response = new TmbOneServiceResponse<>();

        try {
            crmId = findCrmIdByDeviceId(crmId, deviceId, headers);

            TransferOtherBankValidateResponse data = v1TransferOtherBankService.transferPromptPayValidate(request, crmId, correlationId, headers);
            response.setStatus(getResponseSuccess());
            response.setData(data);
            return ResponseEntity.ok().headers(createResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Failed to process promptpay validate error: {}", e.getMessage(), e);
            throw handleExceptions(e);
        }
    }

    @LogAround
    @Operation(summary = "Confirm PromptPay transfer", description = "Confirm PromptPay transfer")
    @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = TransferOtherBankConfirmResponse.class)))
    @PostMapping("/promptpay/confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferOtherBankConfirmResponse>> confirmPromptPay(
            @Parameter(name = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = "X-CRMID") @RequestHeader(HEADER_CRM_ID) String crmId,
            @Parameter(name = "Device ID", example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true) @RequestHeader(DEVICE_ID) @RequestHeaderNonNull String deviceId,
            @RequestBody @Valid TransferOtherBankConfirmRequest request,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {

        logger.debug("Starting promptpay confirm with correlationId: {}", correlationId);
        TmbOneServiceResponse<TransferOtherBankConfirmResponse> response = new TmbOneServiceResponse<>();

        try {
            crmId = findCrmIdByDeviceId(crmId, deviceId, headers);

            TransferOtherBankConfirmResponse data = v1TransferOtherBankService.transferOtherBankConfirm(request, crmId, correlationId, headers);
            response.setStatus(getResponseSuccess());
            response.setData(data);
            return ResponseEntity.ok().headers(createResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Failed to process promptpay confirm error: {}", e.getMessage(), e);
            throw handleExceptions(e);
        }
    }

    private String findCrmIdByDeviceId(String crmId, String deviceId, HttpHeaders headers) throws TMBCommonException {
        crmId = (StringUtils.isEmpty(crmId) ? getCrmIdFromDeviceId(deviceId) : crmId);
        headers.set(HEADER_CRM_ID, crmId);
        return crmId;
    }

    private void checkCustomerActive(CustomerProfileStatus customerProfileStatus) throws TMBCommonException {
        if (customerProfileStatus.getMbUserStatusId().equals(MB_CUSTOMER_STATUS_PIN_LOCK)) {
            throw TransferUtils.failException(ResponseCode.PIN_ERROR_LOCKED_CAUSE);
        }

        if (!customerProfileStatus.getEbCustomerStatusId().equals(EB_CUSTOMER_STATUS) || !customerProfileStatus.getMbUserStatusId().equals(MB_CUSTOMER_STATUS)) {
            logger.error("Customer is not active - invalid data");
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.OK, null);
        }
    }

    private String getCrmIdFromDeviceId(String deviceId) throws TMBCommonException {
        CustomerProfileStatus customerProfileStatus = customerService.getCustomerProfileByDeviceId(deviceId);
        checkCustomerActive(customerProfileStatus);
        return customerProfileStatus.getCrmId();
    }
}
